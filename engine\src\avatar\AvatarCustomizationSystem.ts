/**
 * 虚拟化身定制系统
 * 负责管理虚拟化身的创建、定制和渲染
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { FaceReconstructionSystem } from './FaceReconstructionSystem';
import { BodyParameterizationSystem } from './BodyParameterizationSystem';
import { ClothingSystem } from './ClothingSystem';
import { TextureGenerationSystem } from './TextureGenerationSystem';
import { AvatarPreviewSystem } from './AvatarPreviewSystem';

/**
 * 虚拟化身配置接口
 */
export interface AvatarCustomizationConfig {
  /** 是否启用面部重建 */
  enableFaceReconstruction?: boolean;
  /** 是否启用身体参数化 */
  enableBodyParameterization?: boolean;
  /** 是否启用服装系统 */
  enableClothingSystem?: boolean;
  /** 是否启用纹理生成 */
  enableTextureGeneration?: boolean;
  /** 是否启用实时预览 */
  enableRealTimePreview?: boolean;
  /** 最大并发处理数 */
  maxConcurrentProcessing?: number;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 虚拟化身数据接口
 */
export interface AvatarData {
  /** 唯一标识 */
  id: string;
  /** 用户ID */
  userId?: string;
  /** 面部数据 */
  faceData?: FaceData;
  /** 身体数据 */
  bodyData?: BodyData;
  /** 服装数据 */
  clothingData?: ClothingData;
  /** 纹理数据 */
  textureData?: TextureData;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 面部数据接口
 */
export interface FaceData {
  /** 面部关键点 */
  landmarks: Float32Array;
  /** 面部几何 */
  geometry: THREE.BufferGeometry;
  /** 面部纹理 */
  texture: THREE.Texture;
  /** 面部参数 */
  parameters: FaceParameters;
}

/**
 * 面部参数接口
 */
export interface FaceParameters {
  /** 形状参数 */
  shape: Float32Array;
  /** 表情参数 */
  expression: Float32Array;
  /** 肤色 */
  skinTone: number;
  /** 面部特征调整 */
  features: {
    eyeSize: number;
    noseSize: number;
    mouthSize: number;
    jawWidth: number;
  };
}

/**
 * 身体数据接口
 */
export interface BodyData {
  /** 身体几何 */
  geometry: THREE.BufferGeometry;
  /** 身体纹理 */
  texture: THREE.Texture;
  /** 身体参数 */
  parameters: BodyParameters;
}

/**
 * 身体参数接口
 */
export interface BodyParameters {
  /** 性别 */
  gender: 'male' | 'female';
  /** 身高 (cm) */
  height: number;
  /** 体重 (kg) */
  weight: number;
  /** 体型 */
  build: number; // -2 (瘦弱) 到 2 (强壮)
  /** 肌肉量 */
  muscle: number; // 0 到 1
  /** 肤色 */
  skinTone: number;
}

/**
 * 服装数据接口
 */
export interface ClothingData {
  /** 服装项目列表 */
  items: ClothingItem[];
  /** 服装组合ID */
  outfitId?: string;
}

/**
 * 服装项目接口
 */
export interface ClothingItem {
  /** 服装ID */
  id: string;
  /** 服装类型 */
  type: ClothingType;
  /** 服装几何 */
  geometry: THREE.BufferGeometry;
  /** 服装材质 */
  material: THREE.Material;
  /** 服装颜色 */
  color: string;
  /** 服装尺寸 */
  size: string;
}

/**
 * 服装类型枚举
 */
export enum ClothingType {
  SHIRT = 'shirt',
  PANTS = 'pants',
  DRESS = 'dress',
  SHOES = 'shoes',
  HAT = 'hat',
  ACCESSORY = 'accessory'
}

/**
 * 纹理数据接口
 */
export interface TextureData {
  /** 面部纹理 */
  faceTexture: THREE.Texture;
  /** 身体纹理 */
  bodyTexture: THREE.Texture;
  /** 纹理分辨率 */
  resolution: number;
  /** 纹理质量 */
  quality: number;
}

/**
 * 虚拟化身定制系统
 */
export class AvatarCustomizationSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'AvatarCustomizationSystem';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 系统配置 */
  private config: AvatarCustomizationConfig;

  /** 面部重建系统 */
  private faceReconstructionSystem: FaceReconstructionSystem;

  /** 身体参数化系统 */
  private bodyParameterizationSystem: BodyParameterizationSystem;

  /** 服装系统 */
  private clothingSystem: ClothingSystem;

  /** 纹理生成系统 */
  private textureGenerationSystem: TextureGenerationSystem;

  /** 预览系统 */
  private previewSystem: AvatarPreviewSystem;

  /** 虚拟化身数据缓存 */
  private avatarCache: Map<string, AvatarData> = new Map();

  /** 当前处理中的虚拟化身 */
  private processingAvatars: Set<string> = new Set();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   */
  constructor(config: AvatarCustomizationConfig = {}) {
    super(0);
    
    this.config = {
      enableFaceReconstruction: true,
      enableBodyParameterization: true,
      enableClothingSystem: true,
      enableTextureGeneration: true,
      enableRealTimePreview: true,
      maxConcurrentProcessing: 5,
      debug: false,
      ...config
    };

    this.initializeSystems();
  }

  /**
   * 初始化子系统
   */
  private initializeSystems(): void {
    // 初始化面部重建系统
    if (this.config.enableFaceReconstruction) {
      this.faceReconstructionSystem = new FaceReconstructionSystem({
        debug: this.config.debug
      });
    }

    // 初始化身体参数化系统
    if (this.config.enableBodyParameterization) {
      this.bodyParameterizationSystem = new BodyParameterizationSystem({
        debug: this.config.debug
      });
    }

    // 初始化服装系统
    if (this.config.enableClothingSystem) {
      this.clothingSystem = new ClothingSystem({
        debug: this.config.debug
      });
    }

    // 初始化纹理生成系统
    if (this.config.enableTextureGeneration) {
      this.textureGenerationSystem = new TextureGenerationSystem({
        debug: this.config.debug
      });
    }

    // 初始化预览系统
    if (this.config.enableRealTimePreview) {
      this.previewSystem = new AvatarPreviewSystem({
        debug: this.config.debug
      });
    }
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    super.initialize();

    // 初始化子系统
    if (this.faceReconstructionSystem) {
      this.faceReconstructionSystem.initialize();
    }

    if (this.bodyParameterizationSystem) {
      this.bodyParameterizationSystem.initialize();
    }

    if (this.clothingSystem) {
      this.clothingSystem.initialize();
    }

    if (this.textureGenerationSystem) {
      this.textureGenerationSystem.initialize();
    }

    if (this.previewSystem) {
      this.previewSystem.initialize();
    }

    this.initialized = true;

    if (this.config.debug) {
      console.log('虚拟化身定制系统已初始化');
    }
  }

  /**
   * 系统更新
   */
  public update(deltaTime: number): void {
    if (!this.initialized) {
      return;
    }

    // 更新子系统
    if (this.faceReconstructionSystem) {
      this.faceReconstructionSystem.update(deltaTime);
    }

    if (this.bodyParameterizationSystem) {
      this.bodyParameterizationSystem.update(deltaTime);
    }

    if (this.clothingSystem) {
      this.clothingSystem.update(deltaTime);
    }

    if (this.textureGenerationSystem) {
      this.textureGenerationSystem.update(deltaTime);
    }

    if (this.previewSystem) {
      this.previewSystem.update(deltaTime);
    }
  }

  /**
   * 系统销毁
   */
  public dispose(): void {
    // 销毁子系统
    if (this.faceReconstructionSystem) {
      this.faceReconstructionSystem.dispose();
    }

    if (this.bodyParameterizationSystem) {
      this.bodyParameterizationSystem.dispose();
    }

    if (this.clothingSystem) {
      this.clothingSystem.dispose();
    }

    if (this.textureGenerationSystem) {
      this.textureGenerationSystem.dispose();
    }

    if (this.previewSystem) {
      this.previewSystem.dispose();
    }

    // 清理缓存
    this.avatarCache.clear();
    this.processingAvatars.clear();

    this.initialized = false;

    super.dispose();
  }

  /**
   * 创建新的虚拟化身
   */
  public async createAvatar(userId?: string): Promise<AvatarData> {
    const avatarId = this.generateAvatarId();

    if (this.processingAvatars.has(avatarId)) {
      throw new Error(`虚拟化身 ${avatarId} 正在处理中`);
    }

    if (this.processingAvatars.size >= this.config.maxConcurrentProcessing!) {
      throw new Error('已达到最大并发处理数');
    }

    this.processingAvatars.add(avatarId);

    try {
      const avatarData: AvatarData = {
        id: avatarId,
        userId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 缓存虚拟化身数据
      this.avatarCache.set(avatarId, avatarData);

      this.eventEmitter.emit('avatarCreated', { avatarId, userId });

      if (this.config.debug) {
        console.log(`虚拟化身 ${avatarId} 已创建`);
      }

      return avatarData;
    } finally {
      this.processingAvatars.delete(avatarId);
    }
  }

  /**
   * 从照片重建面部
   */
  public async reconstructFaceFromPhoto(avatarId: string, photoData: ImageData): Promise<FaceData> {
    if (!this.faceReconstructionSystem) {
      throw new Error('面部重建系统未启用');
    }

    const avatarData = this.avatarCache.get(avatarId);
    if (!avatarData) {
      throw new Error(`虚拟化身 ${avatarId} 不存在`);
    }

    if (this.processingAvatars.has(avatarId)) {
      throw new Error(`虚拟化身 ${avatarId} 正在处理中`);
    }

    this.processingAvatars.add(avatarId);

    try {
      const faceData = await this.faceReconstructionSystem.reconstructFromPhoto(photoData);

      // 更新虚拟化身数据
      avatarData.faceData = faceData;
      avatarData.updatedAt = new Date();

      this.eventEmitter.emit('faceReconstructed', { avatarId, faceData });

      if (this.config.debug) {
        console.log(`虚拟化身 ${avatarId} 面部重建完成`);
      }

      return faceData;
    } finally {
      this.processingAvatars.delete(avatarId);
    }
  }

  /**
   * 生成身体模型
   */
  public async generateBody(avatarId: string, bodyParams: BodyParameters): Promise<BodyData> {
    if (!this.bodyParameterizationSystem) {
      throw new Error('身体参数化系统未启用');
    }

    const avatarData = this.avatarCache.get(avatarId);
    if (!avatarData) {
      throw new Error(`虚拟化身 ${avatarId} 不存在`);
    }

    if (this.processingAvatars.has(avatarId)) {
      throw new Error(`虚拟化身 ${avatarId} 正在处理中`);
    }

    this.processingAvatars.add(avatarId);

    try {
      const bodyData = await this.bodyParameterizationSystem.generateBody(bodyParams);

      // 更新虚拟化身数据
      avatarData.bodyData = bodyData;
      avatarData.updatedAt = new Date();

      this.eventEmitter.emit('bodyGenerated', { avatarId, bodyData });

      if (this.config.debug) {
        console.log(`虚拟化身 ${avatarId} 身体生成完成`);
      }

      return bodyData;
    } finally {
      this.processingAvatars.delete(avatarId);
    }
  }

  /**
   * 应用服装
   */
  public async applyClothing(avatarId: string, clothingItems: ClothingItem[]): Promise<ClothingData> {
    if (!this.clothingSystem) {
      throw new Error('服装系统未启用');
    }

    const avatarData = this.avatarCache.get(avatarId);
    if (!avatarData) {
      throw new Error(`虚拟化身 ${avatarId} 不存在`);
    }

    if (!avatarData.bodyData) {
      throw new Error(`虚拟化身 ${avatarId} 缺少身体数据`);
    }

    if (this.processingAvatars.has(avatarId)) {
      throw new Error(`虚拟化身 ${avatarId} 正在处理中`);
    }

    this.processingAvatars.add(avatarId);

    try {
      const clothingData = await this.clothingSystem.applyClothing(avatarData.bodyData, clothingItems);

      // 更新虚拟化身数据
      avatarData.clothingData = clothingData;
      avatarData.updatedAt = new Date();

      this.eventEmitter.emit('clothingApplied', { avatarId, clothingData });

      if (this.config.debug) {
        console.log(`虚拟化身 ${avatarId} 服装应用完成`);
      }

      return clothingData;
    } finally {
      this.processingAvatars.delete(avatarId);
    }
  }

  /**
   * 生成纹理
   */
  public async generateTextures(avatarId: string): Promise<TextureData> {
    if (!this.textureGenerationSystem) {
      throw new Error('纹理生成系统未启用');
    }

    const avatarData = this.avatarCache.get(avatarId);
    if (!avatarData) {
      throw new Error(`虚拟化身 ${avatarId} 不存在`);
    }

    if (this.processingAvatars.has(avatarId)) {
      throw new Error(`虚拟化身 ${avatarId} 正在处理中`);
    }

    this.processingAvatars.add(avatarId);

    try {
      const textureData = await this.textureGenerationSystem.generateTextures(avatarData);

      // 更新虚拟化身数据
      avatarData.textureData = textureData;
      avatarData.updatedAt = new Date();

      this.eventEmitter.emit('texturesGenerated', { avatarId, textureData });

      if (this.config.debug) {
        console.log(`虚拟化身 ${avatarId} 纹理生成完成`);
      }

      return textureData;
    } finally {
      this.processingAvatars.delete(avatarId);
    }
  }

  /**
   * 获取虚拟化身数据
   */
  public getAvatarData(avatarId: string): AvatarData | undefined {
    return this.avatarCache.get(avatarId);
  }

  /**
   * 删除虚拟化身
   */
  public deleteAvatar(avatarId: string): boolean {
    if (this.processingAvatars.has(avatarId)) {
      throw new Error(`虚拟化身 ${avatarId} 正在处理中，无法删除`);
    }

    const deleted = this.avatarCache.delete(avatarId);

    if (deleted) {
      this.eventEmitter.emit('avatarDeleted', { avatarId });

      if (this.config.debug) {
        console.log(`虚拟化身 ${avatarId} 已删除`);
      }
    }

    return deleted;
  }

  /**
   * 生成虚拟化身ID
   */
  private generateAvatarId(): string {
    return `avatar_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener?: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 创建实例
   */
  public createInstance(): AvatarCustomizationSystem {
    return new AvatarCustomizationSystem(this.config);
  }
}
