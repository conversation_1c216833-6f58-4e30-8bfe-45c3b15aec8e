/**
 * 动画事件系统
 * 用于处理动画播放过程中的事件触发和管理
 */
import { EventEmitter } from '../../utils/EventEmitter';
import type { AnimationClip } from '../AnimationClip';

/**
 * 动画事件类型
 */
export enum AnimationEventType {
  /** 动画开始 */
  ANIMATION_START = 'animationStart',
  /** 动画结束 */
  ANIMATION_END = 'animationEnd',
  /** 动画循环 */
  ANIMATION_LOOP = 'animationLoop',
  /** 动画暂停 */
  ANIMATION_PAUSE = 'animationPause',
  /** 动画恢复 */
  ANIMATION_RESUME = 'animationResume',
  /** 动画标记点 */
  ANIMATION_MARKER = 'animationMarker',
  /** 动画混合开始 */
  BLEND_START = 'blendStart',
  /** 动画混合结束 */
  BLEND_END = 'blendEnd',
  /** 动画状态改变 */
  STATE_CHANGE = 'stateChange',
  /** 动画错误 */
  ANIMATION_ERROR = 'animationError'
}

/**
 * 动画事件数据
 */
export interface AnimationEventData {
  /** 事件类型 */
  type: AnimationEventType;
  /** 动画名称 */
  animationName: string;
  /** 当前时间 */
  time: number;
  /** 动画持续时间 */
  duration: number;
  /** 事件数据 */
  data?: any;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 动画标记点
 */
export interface AnimationMarker {
  /** 标记名称 */
  name: string;
  /** 时间位置（秒） */
  time: number;
  /** 标记数据 */
  data?: any;
  /** 是否只触发一次 */
  once?: boolean;
  /** 是否已触发 */
  triggered?: boolean;
}

/**
 * 动画事件配置
 */
export interface AnimationEventConfig {
  /** 是否启用事件系统 */
  enabled?: boolean;
  /** 事件检查间隔（毫秒） */
  checkInterval?: number;
  /** 最大事件队列大小 */
  maxQueueSize?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 动画事件系统
 */
export class AnimationEventSystem extends EventEmitter {
  /** 单例实例 */
  private static instance: AnimationEventSystem | null = null;

  /** 配置 */
  private config: AnimationEventConfig;
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AnimationEventConfig = {
    enabled: true,
    checkInterval: 16, // 60fps
    maxQueueSize: 1000,
    debug: false
  };

  /** 动画标记点映射 */
  private markers: Map<string, AnimationMarker[]> = new Map();
  /** 事件队列 */
  private eventQueue: AnimationEventData[] = [];
  /** 是否正在运行 */
  private isRunning: boolean = false;
  /** 检查定时器 */
  private checkTimer: NodeJS.Timeout | null = null;
  /** 当前播放的动画状态 */
  private playingAnimations: Map<string, {
    startTime: number;
    duration: number;
    lastTime: number;
    isLooping: boolean;
    isPaused: boolean;
  }> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<AnimationEventConfig>) {
    super();
    this.config = { ...AnimationEventSystem.DEFAULT_CONFIG, ...config };
  }

  /**
   * 获取单例实例
   * @param config 配置
   * @returns 事件系统实例
   */
  public static getInstance(config?: Partial<AnimationEventConfig>): AnimationEventSystem {
    if (!AnimationEventSystem.instance) {
      AnimationEventSystem.instance = new AnimationEventSystem(config);
    }
    return AnimationEventSystem.instance;
  }

  /**
   * 启动事件系统
   */
  public start(): void {
    if (this.isRunning || !this.config.enabled) return;

    this.isRunning = true;
    this.checkTimer = setInterval(() => {
      this.checkEvents();
    }, this.config.checkInterval!);

    if (this.config.debug) {
      console.log('动画事件系统已启动');
    }
  }

  /**
   * 停止事件系统
   */
  public stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }

    if (this.config.debug) {
      console.log('动画事件系统已停止');
    }
  }

  /**
   * 注册动画开始
   * @param animationName 动画名称
   * @param duration 动画持续时间
   * @param isLooping 是否循环
   */
  public registerAnimationStart(animationName: string, duration: number, isLooping: boolean = false): void {
    const now = Date.now();
    
    this.playingAnimations.set(animationName, {
      startTime: now,
      duration: duration * 1000, // 转换为毫秒
      lastTime: 0,
      isLooping,
      isPaused: false
    });

    this.queueEvent({
      type: AnimationEventType.ANIMATION_START,
      animationName,
      time: 0,
      duration,
      timestamp: now
    });

    // 重置标记点
    const markers = this.markers.get(animationName);
    if (markers) {
      markers.forEach(marker => {
        marker.triggered = false;
      });
    }
  }

  /**
   * 注册动画结束
   * @param animationName 动画名称
   */
  public registerAnimationEnd(animationName: string): void {
    const animationState = this.playingAnimations.get(animationName);
    if (!animationState) return;

    const now = Date.now();
    const currentTime = (now - animationState.startTime) / 1000;

    this.queueEvent({
      type: AnimationEventType.ANIMATION_END,
      animationName,
      time: currentTime,
      duration: animationState.duration / 1000,
      timestamp: now
    });

    this.playingAnimations.delete(animationName);
  }

  /**
   * 注册动画暂停
   * @param animationName 动画名称
   */
  public registerAnimationPause(animationName: string): void {
    const animationState = this.playingAnimations.get(animationName);
    if (!animationState) return;

    animationState.isPaused = true;
    const now = Date.now();
    const currentTime = (now - animationState.startTime) / 1000;

    this.queueEvent({
      type: AnimationEventType.ANIMATION_PAUSE,
      animationName,
      time: currentTime,
      duration: animationState.duration / 1000,
      timestamp: now
    });
  }

  /**
   * 注册动画恢复
   * @param animationName 动画名称
   */
  public registerAnimationResume(animationName: string): void {
    const animationState = this.playingAnimations.get(animationName);
    if (!animationState) return;

    animationState.isPaused = false;
    const now = Date.now();
    const currentTime = (now - animationState.startTime) / 1000;

    this.queueEvent({
      type: AnimationEventType.ANIMATION_RESUME,
      animationName,
      time: currentTime,
      duration: animationState.duration / 1000,
      timestamp: now
    });
  }

  /**
   * 添加动画标记点
   * @param animationName 动画名称
   * @param marker 标记点
   */
  public addMarker(animationName: string, marker: AnimationMarker): void {
    if (!this.markers.has(animationName)) {
      this.markers.set(animationName, []);
    }

    const markers = this.markers.get(animationName)!;
    markers.push(marker);

    // 按时间排序
    markers.sort((a, b) => a.time - b.time);
  }

  /**
   * 移除动画标记点
   * @param animationName 动画名称
   * @param markerName 标记点名称
   */
  public removeMarker(animationName: string, markerName: string): void {
    const markers = this.markers.get(animationName);
    if (!markers) return;

    const index = markers.findIndex(marker => marker.name === markerName);
    if (index !== -1) {
      markers.splice(index, 1);
    }
  }

  /**
   * 清除动画的所有标记点
   * @param animationName 动画名称
   */
  public clearMarkers(animationName: string): void {
    this.markers.delete(animationName);
  }

  /**
   * 获取动画标记点
   * @param animationName 动画名称
   * @returns 标记点列表
   */
  public getMarkers(animationName: string): AnimationMarker[] {
    return this.markers.get(animationName) || [];
  }

  /**
   * 检查事件
   */
  private checkEvents(): void {
    const now = Date.now();

    for (const [animationName, animationState] of this.playingAnimations.entries()) {
      if (animationState.isPaused) continue;

      const currentTime = (now - animationState.startTime) / 1000;
      const duration = animationState.duration / 1000;

      // 检查是否需要循环
      if (animationState.isLooping && currentTime >= duration) {
        // 触发循环事件
        this.queueEvent({
          type: AnimationEventType.ANIMATION_LOOP,
          animationName,
          time: currentTime,
          duration,
          timestamp: now
        });

        // 重置开始时间
        animationState.startTime = now;
        animationState.lastTime = 0;

        // 重置标记点
        const markers = this.markers.get(animationName);
        if (markers) {
          markers.forEach(marker => {
            if (!marker.once) {
              marker.triggered = false;
            }
          });
        }
      }

      // 检查标记点
      this.checkMarkers(animationName, currentTime, duration, now);

      animationState.lastTime = currentTime;
    }

    // 处理事件队列
    this.processEventQueue();
  }

  /**
   * 检查标记点
   * @param animationName 动画名称
   * @param currentTime 当前时间
   * @param duration 动画持续时间
   * @param timestamp 时间戳
   */
  private checkMarkers(animationName: string, currentTime: number, duration: number, timestamp: number): void {
    const markers = this.markers.get(animationName);
    if (!markers) return;

    for (const marker of markers) {
      if (marker.triggered && marker.once) continue;

      if (currentTime >= marker.time && !marker.triggered) {
        marker.triggered = true;

        this.queueEvent({
          type: AnimationEventType.ANIMATION_MARKER,
          animationName,
          time: currentTime,
          duration,
          data: {
            markerName: marker.name,
            markerData: marker.data
          },
          timestamp
        });
      }
    }
  }

  /**
   * 添加事件到队列
   * @param event 事件数据
   */
  private queueEvent(event: AnimationEventData): void {
    this.eventQueue.push(event);

    // 限制队列大小
    if (this.eventQueue.length > this.config.maxQueueSize!) {
      this.eventQueue.shift();
    }
  }

  /**
   * 处理事件队列
   */
  private processEventQueue(): void {
    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift()!;
      this.emit(event.type, event);

      if (this.config.debug) {
        console.log('动画事件:', event);
      }
    }
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<AnimationEventConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果禁用了事件系统，停止运行
    if (!this.config.enabled && this.isRunning) {
      this.stop();
    }
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): AnimationEventConfig {
    return { ...this.config };
  }

  /**
   * 销毁事件系统
   */
  public destroy(): void {
    this.stop();
    this.markers.clear();
    this.eventQueue = [];
    this.playingAnimations.clear();
    this.removeAllListeners();
  }
}
