/**
 * 多动作合成系统
 * 处理多个动作文件的上传、解析和合成
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { AnimationRetargeting } from './AnimationRetargeting';

/**
 * 解析后的动作数据
 */
export interface ParsedAction {
  /** 动作名称 */
  name: string;
  /** 动画片段 */
  clip: THREE.AnimationClip;
  /** 骨骼结构 */
  skeleton: THREE.Skeleton;
  /** 持续时间 */
  duration: number;
  /** 文件格式 */
  format: string;
  /** 元数据 */
  metadata: {
    fps: number;
    frameCount: number;
    boneCount: number;
    trackCount: number;
  };
}

/**
 * 重定向后的动作数据
 */
export interface RetargetedAction extends ParsedAction {
  /** 骨骼映射 */
  boneMapping: Map<string, string>;
  /** 重定向质量评分 */
  retargetingQuality: number;
}

/**
 * 动作过渡数据
 */
export interface ActionTransition {
  /** 源动作 */
  fromAction: string;
  /** 目标动作 */
  toAction: string;
  /** 过渡时间 */
  duration: number;
  /** 过渡曲线 */
  curve: THREE.AnimationClip;
  /** 过渡类型 */
  type: 'linear' | 'smooth' | 'custom';
}

/**
 * 动作状态机
 */
export interface ActionStateMachine {
  /** 状态映射 */
  states: Map<string, RetargetedAction>;
  /** 过渡映射 */
  transitions: Map<string, ActionTransition[]>;
  /** 默认状态 */
  defaultState: string;
  /** 当前状态 */
  currentState: string;
}

/**
 * 动作库
 */
export interface ActionLibrary {
  /** 动作集合 */
  actions: Map<string, RetargetedAction>;
  /** 分类映射 */
  categories: Map<string, string[]>;
  /** 标签映射 */
  tags: Map<string, string[]>;
  /** 搜索索引 */
  searchIndex: Map<string, string[]>;
}

/**
 * 合成配置
 */
export interface ActionCompositionConfig {
  /** 目标骨骼结构 */
  targetSkeleton: THREE.Skeleton;
  /** 过渡规则 */
  transitionRules: {
    defaultDuration: number;
    smoothingFactor: number;
    blendMode: 'linear' | 'spherical';
  };
  /** 质量设置 */
  qualitySettings: {
    minQualityThreshold: number;
    enableOptimization: boolean;
    compressionLevel: number;
  };
}

/**
 * 合成结果
 */
export interface ComposedActionSet {
  /** 动作集合 */
  actions: RetargetedAction[];
  /** 过渡集合 */
  transitions: ActionTransition[];
  /** 状态机 */
  stateMachine: ActionStateMachine;
  /** 动作库 */
  actionLibrary: ActionLibrary;
  /** 元数据 */
  metadata: {
    totalActions: number;
    totalDuration: number;
    qualityScore: number;
    compatibilityMatrix: number[][];
  };
}

/**
 * 多动作合成系统
 */
export class MultiActionCompositionSystem extends System {
  public static readonly TYPE = 'MultiActionCompositionSystem';

  /** 支持的文件格式 */
  private static readonly SUPPORTED_FORMATS = ['fbx', 'gltf', 'glb', 'bvh', 'mixamo'];

  /** FBX加载器 */
  private fbxLoader: any;
  /** GLTF加载器 */
  private gltfLoader: any;
  /** BVH加载器 */
  private bvhLoader: any;

  constructor() {
    super(0); // 使用默认优先级
    this.initializeLoaders();
  }

  /**
   * 创建系统实例
   * @returns 新的系统实例
   */
  protected createInstance(): System {
    return new MultiActionCompositionSystem();
  }

  /**
   * 初始化加载器
   */
  private initializeLoaders(): void {
    // 这里应该初始化各种格式的加载器
    // 由于依赖问题，暂时使用占位符
    this.fbxLoader = null;
    this.gltfLoader = null;
    this.bvhLoader = null;
  }

  /**
   * 合成多个动作文件
   * @param avatarId 虚拟化身ID
   * @param actionFiles 动作文件数组
   * @param compositionConfig 合成配置
   * @returns 合成结果
   */
  public async composeMultipleActions(
    avatarId: string,
    actionFiles: File[],
    compositionConfig: ActionCompositionConfig
  ): Promise<ComposedActionSet> {
    console.log('开始多动作合成...', {
      avatarId,
      fileCount: actionFiles.length
    });

    try {
      // 1. 并行解析所有动作文件
      const parsePromises = actionFiles.map(file => this.parseActionFile(file));
      const parsedActions = await Promise.all(parsePromises);
      console.log('动作文件解析完成', parsedActions.length);

      // 2. 验证动作兼容性
      this.validateActionCompatibility(parsedActions);
      console.log('动作兼容性验证完成');

      // 3. 骨骼重定向 - 统一到目标骨骼结构
      const retargetedActions = await this.retargetActionsToAvatar(
        parsedActions,
        avatarId,
        compositionConfig.targetSkeleton
      );
      console.log('骨骼重定向完成');

      // 4. 动作质量优化
      const optimizedActions = await this.optimizeActions(retargetedActions);
      console.log('动作质量优化完成');

      // 5. 生成动作过渡
      const transitions = this.generateActionTransitions(
        optimizedActions,
        compositionConfig.transitionRules
      );
      console.log('动作过渡生成完成');

      // 6. 创建动作状态机
      const stateMachine = this.createActionStateMachine(
        optimizedActions,
        transitions
      );
      console.log('动作状态机创建完成');

      // 7. 生成动作库
      const actionLibrary = this.buildActionLibrary(optimizedActions, stateMachine);
      console.log('动作库生成完成');

      const result: ComposedActionSet = {
        actions: optimizedActions,
        transitions,
        stateMachine,
        actionLibrary,
        metadata: {
          totalActions: optimizedActions.length,
          totalDuration: this.calculateTotalDuration(optimizedActions),
          qualityScore: this.assessCompositionQuality(optimizedActions),
          compatibilityMatrix: this.generateCompatibilityMatrix(optimizedActions)
        }
      };

      console.log('多动作合成完成', result.metadata);
      return result;
    } catch (error) {
      console.error('多动作合成失败:', error);
      throw error;
    }
  }

  /**
   * 解析动作文件
   * @param file 动作文件
   * @returns 解析后的动作数据
   */
  private async parseActionFile(file: File): Promise<ParsedAction> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!fileExtension || !MultiActionCompositionSystem.SUPPORTED_FORMATS.includes(fileExtension)) {
      throw new Error(`不支持的动作文件格式: ${fileExtension}`);
    }

    console.log(`解析动作文件: ${file.name} (${fileExtension})`);

    switch (fileExtension) {
      case 'fbx':
        return await this.parseFBXAction(file);
      case 'gltf':
      case 'glb':
        return await this.parseGLTFAction(file);
      case 'bvh':
        return await this.parseBVHAction(file);
      case 'mixamo':
        return await this.parseMixamoAction(file);
      default:
        throw new Error(`不支持的动作文件格式: ${fileExtension}`);
    }
  }

  /**
   * 解析FBX动作文件
   * @param file FBX文件
   * @returns 解析后的动作数据
   */
  private async parseFBXAction(file: File): Promise<ParsedAction> {
    // 这里应该使用FBXLoader解析文件
    // 暂时返回模拟数据
    const mockClip = new THREE.AnimationClip(file.name, 1.0, []);
    const mockSkeleton = new THREE.Skeleton([]);

    return {
      name: file.name.replace('.fbx', ''),
      clip: mockClip,
      skeleton: mockSkeleton,
      duration: 1.0,
      format: 'fbx',
      metadata: {
        fps: 30,
        frameCount: 30,
        boneCount: 0,
        trackCount: 0
      }
    };
  }

  /**
   * 解析GLTF动作文件
   * @param file GLTF文件
   * @returns 解析后的动作数据
   */
  private async parseGLTFAction(file: File): Promise<ParsedAction> {
    // 这里应该使用GLTFLoader解析文件
    // 暂时返回模拟数据
    const mockClip = new THREE.AnimationClip(file.name, 1.0, []);
    const mockSkeleton = new THREE.Skeleton([]);

    return {
      name: file.name.replace(/\.(gltf|glb)$/, ''),
      clip: mockClip,
      skeleton: mockSkeleton,
      duration: 1.0,
      format: file.name.endsWith('.glb') ? 'glb' : 'gltf',
      metadata: {
        fps: 30,
        frameCount: 30,
        boneCount: 0,
        trackCount: 0
      }
    };
  }

  /**
   * 解析BVH动作文件
   * @param file BVH文件
   * @returns 解析后的动作数据
   */
  private async parseBVHAction(file: File): Promise<ParsedAction> {
    // 这里应该使用BVHLoader解析文件
    // 暂时返回模拟数据
    const mockClip = new THREE.AnimationClip(file.name, 1.0, []);
    const mockSkeleton = new THREE.Skeleton([]);

    return {
      name: file.name.replace('.bvh', ''),
      clip: mockClip,
      skeleton: mockSkeleton,
      duration: 1.0,
      format: 'bvh',
      metadata: {
        fps: 30,
        frameCount: 30,
        boneCount: 0,
        trackCount: 0
      }
    };
  }

  /**
   * 解析Mixamo动作文件
   * @param file Mixamo文件
   * @returns 解析后的动作数据
   */
  private async parseMixamoAction(file: File): Promise<ParsedAction> {
    // 这里应该解析Mixamo格式文件
    // 暂时返回模拟数据
    const mockClip = new THREE.AnimationClip(file.name, 1.0, []);
    const mockSkeleton = new THREE.Skeleton([]);

    return {
      name: file.name.replace('.mixamo', ''),
      clip: mockClip,
      skeleton: mockSkeleton,
      duration: 1.0,
      format: 'mixamo',
      metadata: {
        fps: 30,
        frameCount: 30,
        boneCount: 0,
        trackCount: 0
      }
    };
  }

  /**
   * 验证动作兼容性
   * @param actions 动作数组
   */
  private validateActionCompatibility(actions: ParsedAction[]): void {
    if (actions.length === 0) {
      throw new Error('没有有效的动作文件');
    }

    // 检查动作基本信息
    for (const action of actions) {
      if (!action.clip || action.duration <= 0) {
        throw new Error(`动作 ${action.name} 数据无效`);
      }
    }

    console.log('动作兼容性验证通过');
  }

  /**
   * 骨骼重定向到虚拟化身
   * @param actions 动作数组
   * @param avatarId 虚拟化身ID
   * @param targetSkeleton 目标骨骼结构
   * @returns 重定向后的动作数组
   */
  private async retargetActionsToAvatar(
    actions: ParsedAction[],
    avatarId: string,
    targetSkeleton: THREE.Skeleton
  ): Promise<RetargetedAction[]> {
    const retargetedActions: RetargetedAction[] = [];

    for (const action of actions) {
      console.log(`重定向动作: ${action.name}`);

      // 创建骨骼映射
      const boneMapping = this.createBoneMapping(action.skeleton, targetSkeleton);

      // 执行重定向
      const retargetedClip = this.retargetAnimationClip(
        action.clip,
        action.skeleton,
        targetSkeleton,
        boneMapping
      );

      const retargetedAction: RetargetedAction = {
        ...action,
        clip: retargetedClip,
        skeleton: targetSkeleton,
        boneMapping,
        retargetingQuality: this.assessRetargetingQuality(action, retargetedClip)
      };

      retargetedActions.push(retargetedAction);
    }

    return retargetedActions;
  }

  /**
   * 创建骨骼映射
   * @param sourceSkeleton 源骨骼结构
   * @param targetSkeleton 目标骨骼结构
   * @returns 骨骼映射
   */
  private createBoneMapping(
    sourceSkeleton: THREE.Skeleton,
    targetSkeleton: THREE.Skeleton
  ): Map<string, string> {
    const boneMapping = new Map<string, string>();

    // 简单的名称匹配策略
    const sourceBoneNames = sourceSkeleton.bones.map(bone => bone.name);
    const targetBoneNames = targetSkeleton.bones.map(bone => bone.name);

    for (const sourceBone of sourceBoneNames) {
      // 直接匹配
      if (targetBoneNames.includes(sourceBone)) {
        boneMapping.set(sourceBone, sourceBone);
        continue;
      }

      // 模糊匹配
      const matchedBone = this.findBestBoneMatch(sourceBone, targetBoneNames);
      if (matchedBone) {
        boneMapping.set(sourceBone, matchedBone);
      }
    }

    return boneMapping;
  }

  /**
   * 寻找最佳骨骼匹配
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneNames 目标骨骼名称数组
   * @returns 最佳匹配的骨骼名称
   */
  private findBestBoneMatch(sourceBoneName: string, targetBoneNames: string[]): string | null {
    const normalizedSource = sourceBoneName.toLowerCase();

    // 关键词匹配
    const keywords = ['spine', 'head', 'neck', 'shoulder', 'arm', 'hand', 'leg', 'foot', 'hip'];

    for (const keyword of keywords) {
      if (normalizedSource.includes(keyword)) {
        const matches = targetBoneNames.filter(name =>
          name.toLowerCase().includes(keyword)
        );

        if (matches.length > 0) {
          // 选择最相似的
          return matches.reduce((best, current) => {
            const bestSimilarity = this.calculateStringSimilarity(normalizedSource, best.toLowerCase());
            const currentSimilarity = this.calculateStringSimilarity(normalizedSource, current.toLowerCase());
            return currentSimilarity > bestSimilarity ? current : best;
          });
        }
      }
    }

    return null;
  }

  /**
   * 计算字符串相似度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 相似度 (0-1)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.calculateEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * 计算编辑距离
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 重定向动画片段
   * @param sourceClip 源动画片段
   * @param sourceSkeleton 源骨骼结构
   * @param targetSkeleton 目标骨骼结构
   * @param boneMapping 骨骼映射
   * @returns 重定向后的动画片段
   */
  private retargetAnimationClip(
    sourceClip: THREE.AnimationClip,
    sourceSkeleton: THREE.Skeleton,
    targetSkeleton: THREE.Skeleton,
    boneMapping: Map<string, string>
  ): THREE.AnimationClip {
    const retargetedTracks: THREE.KeyframeTrack[] = [];

    for (const track of sourceClip.tracks) {
      const trackName = track.name;
      const boneName = this.extractBoneNameFromTrack(trackName);

      if (boneName && boneMapping.has(boneName)) {
        const targetBoneName = boneMapping.get(boneName)!;
        const retargetedTrackName = trackName.replace(boneName, targetBoneName);

        // 创建重定向后的轨道
        const retargetedTrack = track.clone();
        retargetedTrack.name = retargetedTrackName;

        retargetedTracks.push(retargetedTrack);
      }
    }

    return new THREE.AnimationClip(
      `${sourceClip.name}_retargeted`,
      sourceClip.duration,
      retargetedTracks
    );
  }

  /**
   * 从轨道名称提取骨骼名称
   * @param trackName 轨道名称
   * @returns 骨骼名称
   */
  private extractBoneNameFromTrack(trackName: string): string | null {
    // 常见的轨道名称格式：
    // "BoneName.position"
    // "BoneName.quaternion"
    // "BoneName.scale"
    const parts = trackName.split('.');
    return parts.length > 1 ? parts[0] : null;
  }

  /**
   * 评估重定向质量
   * @param originalAction 原始动作
   * @param retargetedClip 重定向后的动画片段
   * @returns 质量评分
   */
  private assessRetargetingQuality(originalAction: ParsedAction, retargetedClip: THREE.AnimationClip): number {
    // 基于轨道数量和持续时间的简单质量评估
    const originalTrackCount = originalAction.clip.tracks.length;
    const retargetedTrackCount = retargetedClip.tracks.length;

    if (originalTrackCount === 0) return 0;

    const trackRatio = retargetedTrackCount / originalTrackCount;
    const durationRatio = Math.abs(retargetedClip.duration - originalAction.clip.duration) < 0.01 ? 1 : 0.8;

    return Math.min(trackRatio * durationRatio, 1.0);
  }

  /**
   * 优化动作
   * @param actions 动作数组
   * @returns 优化后的动作数组
   */
  private async optimizeActions(actions: RetargetedAction[]): Promise<RetargetedAction[]> {
    const optimizedActions: RetargetedAction[] = [];

    for (const action of actions) {
      // 优化动画片段
      const optimizedClip = this.optimizeAnimationClip(action.clip);

      const optimizedAction: RetargetedAction = {
        ...action,
        clip: optimizedClip
      };

      optimizedActions.push(optimizedAction);
    }

    return optimizedActions;
  }

  /**
   * 优化动画片段
   * @param clip 动画片段
   * @returns 优化后的动画片段
   */
  private optimizeAnimationClip(clip: THREE.AnimationClip): THREE.AnimationClip {
    // 简单的优化：移除空轨道
    const optimizedTracks = clip.tracks.filter(track => {
      return track.times.length > 0 && track.values.length > 0;
    });

    return new THREE.AnimationClip(clip.name, clip.duration, optimizedTracks);
  }

  /**
   * 生成动作过渡
   * @param actions 动作数组
   * @param transitionRules 过渡规则
   * @returns 过渡数组
   */
  private generateActionTransitions(
    actions: RetargetedAction[],
    transitionRules: ActionCompositionConfig['transitionRules']
  ): ActionTransition[] {
    const transitions: ActionTransition[] = [];

    // 为每对动作生成过渡
    for (let i = 0; i < actions.length; i++) {
      for (let j = 0; j < actions.length; j++) {
        if (i !== j) {
          const fromAction = actions[i];
          const toAction = actions[j];

          const transition = this.createActionTransition(
            fromAction,
            toAction,
            transitionRules
          );

          transitions.push(transition);
        }
      }
    }

    return transitions;
  }

  /**
   * 创建动作过渡
   * @param fromAction 源动作
   * @param toAction 目标动作
   * @param transitionRules 过渡规则
   * @returns 动作过渡
   */
  private createActionTransition(
    fromAction: RetargetedAction,
    toAction: RetargetedAction,
    transitionRules: ActionCompositionConfig['transitionRules']
  ): ActionTransition {
    const duration = transitionRules.defaultDuration;

    // 创建简单的线性过渡动画片段
    const transitionClip = new THREE.AnimationClip(
      `${fromAction.name}_to_${toAction.name}`,
      duration,
      []
    );

    return {
      fromAction: fromAction.name,
      toAction: toAction.name,
      duration,
      curve: transitionClip,
      type: 'linear'
    };
  }

  /**
   * 创建动作状态机
   * @param actions 动作数组
   * @param transitions 过渡数组
   * @returns 动作状态机
   */
  private createActionStateMachine(
    actions: RetargetedAction[],
    transitions: ActionTransition[]
  ): ActionStateMachine {
    const states = new Map<string, RetargetedAction>();
    const transitionMap = new Map<string, ActionTransition[]>();

    // 添加状态
    for (const action of actions) {
      states.set(action.name, action);
    }

    // 组织过渡
    for (const transition of transitions) {
      const fromTransitions = transitionMap.get(transition.fromAction) || [];
      fromTransitions.push(transition);
      transitionMap.set(transition.fromAction, fromTransitions);
    }

    return {
      states,
      transitions: transitionMap,
      defaultState: actions.length > 0 ? actions[0].name : '',
      currentState: actions.length > 0 ? actions[0].name : ''
    };
  }

  /**
   * 构建动作库
   * @param actions 动作数组
   * @param stateMachine 状态机
   * @returns 动作库
   */
  private buildActionLibrary(
    actions: RetargetedAction[],
    stateMachine: ActionStateMachine
  ): ActionLibrary {
    const actionMap = new Map<string, RetargetedAction>();
    const categories = new Map<string, string[]>();
    const tags = new Map<string, string[]>();
    const searchIndex = new Map<string, string[]>();

    // 添加动作
    for (const action of actions) {
      actionMap.set(action.name, action);

      // 简单的分类逻辑
      const category = this.categorizeAction(action);
      const categoryActions = categories.get(category) || [];
      categoryActions.push(action.name);
      categories.set(category, categoryActions);

      // 生成标签
      const actionTags = this.generateActionTags(action);
      tags.set(action.name, actionTags);

      // 构建搜索索引
      const searchTerms = [action.name, category, ...actionTags];
      searchIndex.set(action.name, searchTerms);
    }

    return {
      actions: actionMap,
      categories,
      tags,
      searchIndex
    };
  }

  /**
   * 动作分类
   * @param action 动作
   * @returns 分类名称
   */
  private categorizeAction(action: RetargetedAction): string {
    const name = action.name.toLowerCase();

    if (name.includes('walk') || name.includes('run')) {
      return 'locomotion';
    }
    if (name.includes('idle') || name.includes('stand')) {
      return 'idle';
    }
    if (name.includes('jump') || name.includes('leap')) {
      return 'jump';
    }
    if (name.includes('dance')) {
      return 'dance';
    }
    if (name.includes('fight') || name.includes('attack')) {
      return 'combat';
    }

    return 'general';
  }

  /**
   * 生成动作标签
   * @param action 动作
   * @returns 标签数组
   */
  private generateActionTags(action: RetargetedAction): string[] {
    const tags: string[] = [];
    const name = action.name.toLowerCase();

    // 基于名称生成标签
    if (name.includes('slow')) tags.push('slow');
    if (name.includes('fast')) tags.push('fast');
    if (name.includes('left')) tags.push('left');
    if (name.includes('right')) tags.push('right');
    if (name.includes('forward')) tags.push('forward');
    if (name.includes('backward')) tags.push('backward');

    // 基于持续时间生成标签
    if (action.duration < 1) tags.push('short');
    else if (action.duration > 5) tags.push('long');
    else tags.push('medium');

    return tags;
  }

  /**
   * 计算总持续时间
   * @param actions 动作数组
   * @returns 总持续时间
   */
  private calculateTotalDuration(actions: RetargetedAction[]): number {
    return actions.reduce((total, action) => total + action.duration, 0);
  }

  /**
   * 评估合成质量
   * @param actions 动作数组
   * @returns 质量评分
   */
  private assessCompositionQuality(actions: RetargetedAction[]): number {
    if (actions.length === 0) return 0;

    const qualitySum = actions.reduce((sum, action) => sum + action.retargetingQuality, 0);
    return qualitySum / actions.length;
  }

  /**
   * 生成兼容性矩阵
   * @param actions 动作数组
   * @returns 兼容性矩阵
   */
  private generateCompatibilityMatrix(actions: RetargetedAction[]): number[][] {
    const matrix: number[][] = [];

    for (let i = 0; i < actions.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < actions.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0; // 自身完全兼容
        } else {
          matrix[i][j] = this.calculateActionCompatibility(actions[i], actions[j]);
        }
      }
    }

    return matrix;
  }

  /**
   * 计算动作兼容性
   * @param action1 动作1
   * @param action2 动作2
   * @returns 兼容性评分
   */
  private calculateActionCompatibility(action1: RetargetedAction, action2: RetargetedAction): number {
    let compatibility = 0;
    let factors = 0;

    // 1. 骨骼结构兼容性
    if (action1.skeleton === action2.skeleton) {
      compatibility += 1.0;
    } else {
      compatibility += 0.5; // 不同骨骼结构但可能兼容
    }
    factors += 1;

    // 2. 持续时间相似性
    const durationDiff = Math.abs(action1.duration - action2.duration);
    const durationCompatibility = Math.max(0, 1 - durationDiff / Math.max(action1.duration, action2.duration));
    compatibility += durationCompatibility;
    factors += 1;

    // 3. 重定向质量
    const avgQuality = (action1.retargetingQuality + action2.retargetingQuality) / 2;
    compatibility += avgQuality;
    factors += 1;

    return factors > 0 ? compatibility / factors : 0;
  }
}
