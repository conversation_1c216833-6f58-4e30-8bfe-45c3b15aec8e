/**
 * 动画压缩器
 * 用于压缩和优化动画数据，减少内存占用和提高性能
 */
import * as THREE from 'three';

/**
 * 压缩配置
 */
export interface CompressionConfig {
  /** 位置精度（小数位数） */
  positionPrecision?: number;
  /** 旋转精度（小数位数） */
  rotationPrecision?: number;
  /** 缩放精度（小数位数） */
  scalePrecision?: number;
  /** 时间精度（小数位数） */
  timePrecision?: number;
  /** 是否移除冗余关键帧 */
  removeRedundantKeys?: boolean;
  /** 冗余阈值 */
  redundancyThreshold?: number;
  /** 是否启用关键帧优化 */
  optimizeKeyframes?: boolean;
  /** 是否启用曲线简化 */
  simplifyCurves?: boolean;
  /** 曲线简化容差 */
  simplificationTolerance?: number;
  /** 是否启用量化 */
  enableQuantization?: boolean;
  /** 量化位数 */
  quantizationBits?: number;
}

/**
 * 压缩统计信息
 */
export interface CompressionStats {
  /** 原始大小（字节） */
  originalSize: number;
  /** 压缩后大小（字节） */
  compressedSize: number;
  /** 压缩比 */
  compressionRatio: number;
  /** 原始关键帧数量 */
  originalKeyframes: number;
  /** 压缩后关键帧数量 */
  compressedKeyframes: number;
  /** 关键帧减少比例 */
  keyframeReduction: number;
  /** 处理时间（毫秒） */
  processingTime: number;
}

/**
 * 动画压缩器
 */
export class AnimationCompressor {
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CompressionConfig = {
    positionPrecision: 3,
    rotationPrecision: 4,
    scalePrecision: 3,
    timePrecision: 3,
    removeRedundantKeys: true,
    redundancyThreshold: 0.001,
    optimizeKeyframes: true,
    simplifyCurves: true,
    simplificationTolerance: 0.01,
    enableQuantization: false,
    quantizationBits: 16
  };

  /** 配置 */
  private config: CompressionConfig;

  /**
   * 构造函数
   * @param config 压缩配置
   */
  constructor(config?: Partial<CompressionConfig>) {
    this.config = { ...AnimationCompressor.DEFAULT_CONFIG, ...config };
  }

  /**
   * 压缩动画片段
   * @param clip 动画片段
   * @returns 压缩后的动画片段和统计信息
   */
  public compressClip(clip: THREE.AnimationClip): { clip: THREE.AnimationClip; stats: CompressionStats } {
    const startTime = performance.now();
    
    // 计算原始大小
    const originalSize = this.calculateClipSize(clip);
    const originalKeyframes = this.countKeyframes(clip);

    // 创建压缩后的轨道
    const compressedTracks: THREE.KeyframeTrack[] = [];

    for (const track of clip.tracks) {
      const compressedTrack = this.compressTrack(track);
      if (compressedTrack) {
        compressedTracks.push(compressedTrack);
      }
    }

    // 创建压缩后的动画片段
    const compressedClip = new THREE.AnimationClip(
      `${clip.name}_compressed`,
      clip.duration,
      compressedTracks,
      clip.blendMode
    );

    // 计算压缩后大小
    const compressedSize = this.calculateClipSize(compressedClip);
    const compressedKeyframes = this.countKeyframes(compressedClip);
    const processingTime = performance.now() - startTime;

    // 生成统计信息
    const stats: CompressionStats = {
      originalSize,
      compressedSize,
      compressionRatio: originalSize > 0 ? compressedSize / originalSize : 1,
      originalKeyframes,
      compressedKeyframes,
      keyframeReduction: originalKeyframes > 0 ? (originalKeyframes - compressedKeyframes) / originalKeyframes : 0,
      processingTime
    };

    return { clip: compressedClip, stats };
  }

  /**
   * 压缩轨道
   * @param track 轨道
   * @returns 压缩后的轨道
   */
  private compressTrack(track: THREE.KeyframeTrack): THREE.KeyframeTrack | null {
    let times = [...track.times];
    let values = [...track.values];

    // 移除冗余关键帧
    if (this.config.removeRedundantKeys) {
      const result = this.removeRedundantKeyframes(times, values, track.getValueSize());
      times = result.times;
      values = result.values;
    }

    // 优化关键帧
    if (this.config.optimizeKeyframes) {
      const result = this.optimizeKeyframes(times, values, track.getValueSize());
      times = result.times;
      values = result.values;
    }

    // 简化曲线
    if (this.config.simplifyCurves) {
      const result = this.simplifyCurve(times, values, track.getValueSize());
      times = result.times;
      values = result.values;
    }

    // 量化数据
    if (this.config.enableQuantization) {
      values = this.quantizeValues(values);
    }

    // 精度处理
    times = this.roundTimes(times);
    values = this.roundValues(values, track);

    // 如果没有关键帧，返回null
    if (times.length === 0) {
      return null;
    }

    // 创建新轨道
    const TrackConstructor = track.constructor as any;
    return new TrackConstructor(track.name, times, values, track.getInterpolation());
  }

  /**
   * 移除冗余关键帧
   * @param times 时间数组
   * @param values 值数组
   * @param valueSize 值大小
   * @returns 处理后的时间和值数组
   */
  private removeRedundantKeyframes(times: number[], values: number[], valueSize: number): { times: number[]; values: number[] } {
    if (times.length <= 2) {
      return { times, values };
    }

    const newTimes: number[] = [times[0]];
    const newValues: number[] = [];

    // 添加第一个关键帧的值
    for (let i = 0; i < valueSize; i++) {
      newValues.push(values[i]);
    }

    for (let i = 1; i < times.length - 1; i++) {
      const prevIndex = i - 1;
      const currIndex = i;
      const nextIndex = i + 1;

      let isRedundant = true;

      // 检查每个分量
      for (let j = 0; j < valueSize; j++) {
        const prevValue = values[prevIndex * valueSize + j];
        const currValue = values[currIndex * valueSize + j];
        const nextValue = values[nextIndex * valueSize + j];

        // 线性插值检查
        const t = (times[currIndex] - times[prevIndex]) / (times[nextIndex] - times[prevIndex]);
        const interpolatedValue = prevValue + (nextValue - prevValue) * t;
        const difference = Math.abs(currValue - interpolatedValue);

        if (difference > this.config.redundancyThreshold!) {
          isRedundant = false;
          break;
        }
      }

      // 如果不是冗余的，保留这个关键帧
      if (!isRedundant) {
        newTimes.push(times[currIndex]);
        for (let j = 0; j < valueSize; j++) {
          newValues.push(values[currIndex * valueSize + j]);
        }
      }
    }

    // 添加最后一个关键帧
    newTimes.push(times[times.length - 1]);
    const lastIndex = times.length - 1;
    for (let i = 0; i < valueSize; i++) {
      newValues.push(values[lastIndex * valueSize + i]);
    }

    return { times: newTimes, values: newValues };
  }

  /**
   * 优化关键帧
   * @param times 时间数组
   * @param values 值数组
   * @param valueSize 值大小
   * @returns 处理后的时间和值数组
   */
  private optimizeKeyframes(times: number[], values: number[], valueSize: number): { times: number[]; values: number[] } {
    // 这里可以实现更复杂的关键帧优化算法
    // 例如：Douglas-Peucker算法、Ramer-Douglas-Peucker算法等
    return { times, values };
  }

  /**
   * 简化曲线
   * @param times 时间数组
   * @param values 值数组
   * @param valueSize 值大小
   * @returns 处理后的时间和值数组
   */
  private simplifyCurve(times: number[], values: number[], valueSize: number): { times: number[]; values: number[] } {
    // 实现曲线简化算法
    // 这里使用简单的距离阈值方法
    if (times.length <= 2) {
      return { times, values };
    }

    const newTimes: number[] = [times[0]];
    const newValues: number[] = [];

    // 添加第一个关键帧的值
    for (let i = 0; i < valueSize; i++) {
      newValues.push(values[i]);
    }

    let lastKeptIndex = 0;

    for (let i = 1; i < times.length - 1; i++) {
      let maxDistance = 0;

      // 计算到线段的最大距离
      for (let j = 0; j < valueSize; j++) {
        const startValue = values[lastKeptIndex * valueSize + j];
        const endValue = values[(times.length - 1) * valueSize + j];
        const currentValue = values[i * valueSize + j];

        const t = (times[i] - times[lastKeptIndex]) / (times[times.length - 1] - times[lastKeptIndex]);
        const interpolatedValue = startValue + (endValue - startValue) * t;
        const distance = Math.abs(currentValue - interpolatedValue);

        maxDistance = Math.max(maxDistance, distance);
      }

      // 如果距离超过阈值，保留这个关键帧
      if (maxDistance > this.config.simplificationTolerance!) {
        newTimes.push(times[i]);
        for (let j = 0; j < valueSize; j++) {
          newValues.push(values[i * valueSize + j]);
        }
        lastKeptIndex = newTimes.length - 1;
      }
    }

    // 添加最后一个关键帧
    newTimes.push(times[times.length - 1]);
    const lastIndex = times.length - 1;
    for (let i = 0; i < valueSize; i++) {
      newValues.push(values[lastIndex * valueSize + i]);
    }

    return { times: newTimes, values: newValues };
  }

  /**
   * 量化值
   * @param values 值数组
   * @returns 量化后的值数组
   */
  private quantizeValues(values: number[]): number[] {
    const bits = this.config.quantizationBits!;
    const maxValue = Math.pow(2, bits) - 1;

    return values.map(value => {
      // 将值映射到[0, 1]范围
      const normalizedValue = (value + 1) / 2; // 假设值在[-1, 1]范围内
      // 量化
      const quantizedValue = Math.round(normalizedValue * maxValue) / maxValue;
      // 映射回原始范围
      return quantizedValue * 2 - 1;
    });
  }

  /**
   * 四舍五入时间
   * @param times 时间数组
   * @returns 四舍五入后的时间数组
   */
  private roundTimes(times: number[]): number[] {
    const precision = this.config.timePrecision!;
    const factor = Math.pow(10, precision);
    return times.map(time => Math.round(time * factor) / factor);
  }

  /**
   * 四舍五入值
   * @param values 值数组
   * @param track 轨道
   * @returns 四舍五入后的值数组
   */
  private roundValues(values: number[], track: THREE.KeyframeTrack): number[] {
    let precision: number;

    // 根据轨道类型选择精度
    if (track.name.includes('.position')) {
      precision = this.config.positionPrecision!;
    } else if (track.name.includes('.quaternion') || track.name.includes('.rotation')) {
      precision = this.config.rotationPrecision!;
    } else if (track.name.includes('.scale')) {
      precision = this.config.scalePrecision!;
    } else {
      precision = this.config.positionPrecision!; // 默认精度
    }

    const factor = Math.pow(10, precision);
    return values.map(value => Math.round(value * factor) / factor);
  }

  /**
   * 计算动画片段大小
   * @param clip 动画片段
   * @returns 大小（字节）
   */
  private calculateClipSize(clip: THREE.AnimationClip): number {
    let size = 0;

    for (const track of clip.tracks) {
      // 时间数组大小
      size += track.times.length * 4; // float32
      // 值数组大小
      size += track.values.length * 4; // float32
      // 轨道名称大小
      size += track.name.length * 2; // UTF-16
    }

    return size;
  }

  /**
   * 计算关键帧数量
   * @param clip 动画片段
   * @returns 关键帧数量
   */
  private countKeyframes(clip: THREE.AnimationClip): number {
    let count = 0;

    for (const track of clip.tracks) {
      count += track.times.length;
    }

    return count;
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<CompressionConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): CompressionConfig {
    return { ...this.config };
  }

  /**
   * 批量压缩动画片段
   * @param clips 动画片段数组
   * @returns 压缩结果数组
   */
  public compressClips(clips: THREE.AnimationClip[]): Array<{ clip: THREE.AnimationClip; stats: CompressionStats }> {
    return clips.map(clip => this.compressClip(clip));
  }
}
