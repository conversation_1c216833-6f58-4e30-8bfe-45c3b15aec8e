/**
 * AI动画合成系统测试
 * 验证AI动画合成功能的正确性
 */
import { AIAnimationSynthesisComponent, AIAnimationSynthesisSystem, AIAnimationSynthesisConfig } from '../AIAnimationSynthesis';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { LocalAIAnimationModel } from '../ai/LocalAIAnimationModel';

describe('AIAnimationSynthesis', () => {
  let world: World;
  let entity: Entity;
  let component: AIAnimationSynthesisComponent;
  let system: AIAnimationSynthesisSystem;

  beforeEach(() => {
    world = new World();
    entity = new Entity();
    world.addEntity(entity);
  });

  afterEach(() => {
    if (component) {
      component.onDestroy();
    }
    if (system) {
      world.removeSystem(system);
    }
  });

  describe('AIAnimationSynthesisComponent', () => {
    test('应该正确创建组件', () => {
      const config: Partial<AIAnimationSynthesisConfig> = {
        debug: true,
        batchSize: 2,
        maxCacheSize: 50
      };

      component = new AIAnimationSynthesisComponent(entity, config);
      
      expect(component).toBeDefined();
      expect(component.type).toBe('AIAnimationSynthesis');
    });

    test('应该正确处理动画生成请求', async () => {
      component = new AIAnimationSynthesisComponent(entity, { debug: true });
      
      // 设置AI模型
      const aiModel = new LocalAIAnimationModel({ debug: true });
      await aiModel.initialize();
      component.setAIModel(aiModel);

      // 请求生成动画
      const requestId = component.requestAnimation({
        prompt: '开心地挥手',
        type: 'body',
        duration: 3.0,
        loop: false
      });

      expect(requestId).toBeDefined();
      expect(typeof requestId).toBe('string');
    });

    test('应该正确处理批处理请求', async () => {
      const config: Partial<AIAnimationSynthesisConfig> = {
        debug: true,
        batchSize: 2
      };

      component = new AIAnimationSynthesisComponent(entity, config);
      
      // 设置AI模型
      const aiModel = new LocalAIAnimationModel({ debug: true });
      await aiModel.initialize();
      component.setAIModel(aiModel);

      // 添加多个请求
      const requestId1 = component.requestAnimation({
        prompt: '开心地挥手',
        type: 'body',
        duration: 3.0,
        loop: false
      });

      const requestId2 = component.requestAnimation({
        prompt: '微笑表情',
        type: 'facial',
        duration: 2.0,
        loop: true
      });

      expect(requestId1).toBeDefined();
      expect(requestId2).toBeDefined();
    });

    test('应该正确管理缓存', () => {
      const config: Partial<AIAnimationSynthesisConfig> = {
        maxCacheSize: 2,
        cacheExpireTime: 1000
      };

      component = new AIAnimationSynthesisComponent(entity, config);

      // 模拟添加缓存结果
      const result1 = {
        id: 'test1',
        success: true,
        generationTime: 500
      };

      const result2 = {
        id: 'test2',
        success: true,
        generationTime: 600
      };

      // 通过私有方法测试缓存功能
      (component as any).setCacheResult('test1', result1);
      (component as any).setCacheResult('test2', result2);

      expect(component.getResult('test1')).toBeDefined();
      expect(component.getResult('test2')).toBeDefined();
    });

    test('应该正确取消请求', () => {
      component = new AIAnimationSynthesisComponent(entity);

      const requestId = component.requestAnimation({
        prompt: '测试动画',
        type: 'body',
        duration: 5.0,
        loop: false
      });

      const canceled = component.cancelRequest(requestId);
      expect(canceled).toBe(true);

      // 尝试取消不存在的请求
      const notCanceled = component.cancelRequest('nonexistent');
      expect(notCanceled).toBe(false);
    });

    test('应该正确清理缓存', () => {
      component = new AIAnimationSynthesisComponent(entity);

      // 添加测试结果到缓存
      const result = {
        id: 'test',
        success: true,
        generationTime: 500
      };

      (component as any).setCacheResult('test', result);
      expect(component.getResult('test')).toBeDefined();

      // 清理特定缓存
      component.clearCache('test');
      expect(component.getResult('test')).toBeNull();

      // 添加多个结果
      (component as any).setCacheResult('test1', result);
      (component as any).setCacheResult('test2', result);

      // 清理所有缓存
      component.clearCache();
      expect(component.getResult('test1')).toBeNull();
      expect(component.getResult('test2')).toBeNull();
    });
  });

  describe('AIAnimationSynthesisSystem', () => {
    test('应该正确创建系统', () => {
      const config: Partial<AIAnimationSynthesisConfig> = {
        debug: true,
        useLocalModel: true
      };

      system = new AIAnimationSynthesisSystem(world, config);
      
      expect(system).toBeDefined();
      expect(system.type).toBe('AIAnimationSynthesis');
    });

    test('应该正确管理组件', () => {
      system = new AIAnimationSynthesisSystem(world);
      world.addSystem(system);

      // 创建组件
      const component = system.createAIAnimationSynthesis(entity);
      expect(component).toBeDefined();

      // 获取组件
      const retrieved = system.getAIAnimationSynthesis(entity);
      expect(retrieved).toBe(component);

      // 移除组件
      system.removeAIAnimationSynthesis(entity);
      const removed = system.getAIAnimationSynthesis(entity);
      expect(removed).toBeNull();
    });

    test('应该正确生成不同类型的动画', () => {
      system = new AIAnimationSynthesisSystem(world);
      world.addSystem(system);

      const component = system.createAIAnimationSynthesis(entity);

      // 生成身体动画
      const bodyRequestId = system.generateBodyAnimation(entity, '跳跃动作', 3.0);
      expect(bodyRequestId).toBeDefined();

      // 生成面部动画
      const facialRequestId = system.generateFacialAnimation(entity, '开心表情', 2.0);
      expect(facialRequestId).toBeDefined();

      // 生成组合动画
      const combinedRequestId = system.generateCombinedAnimation(entity, '开心地跳跃', 4.0);
      expect(combinedRequestId).toBeDefined();
    });

    test('应该正确处理事件', (done) => {
      system = new AIAnimationSynthesisSystem(world);
      world.addSystem(system);

      let eventReceived = false;

      // 添加事件监听器
      system.addEventListener('modelLoaded', (data) => {
        eventReceived = true;
        expect(data).toBeDefined();
        done();
      });

      // 等待模型加载事件
      setTimeout(() => {
        if (!eventReceived) {
          done();
        }
      }, 2000);
    });
  });
});
