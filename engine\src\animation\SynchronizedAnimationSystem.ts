/**
 * 面部动画集成系统
 * 将面部动画与身体动作进行同步和集成
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { RetargetedAction } from './MultiActionCompositionSystem';
import { FacialExpressionType, VisemeType } from '../avatar/components/FacialAnimationComponent';

/**
 * 面部动画数据
 */
export interface FacialAnimationData {
  /** 表情类型 */
  expression: FacialExpressionType;
  /** 强度 */
  intensity: number;
  /** 持续时间 */
  duration: number;
  /** 开始时间 */
  startTime: number;
  /** 过渡时间 */
  transitionTime: number;
}

/**
 * 面部动画集合
 */
export interface FacialAnimationSet {
  /** 动画映射 */
  animations: Map<string, FacialAnimationData[]>;
  /** 默认表情 */
  defaultExpression: FacialExpressionType;
  /** 总持续时间 */
  totalDuration: number;
}

/**
 * 动作集合
 */
export interface ActionSet {
  /** 动作映射 */
  actions: Map<string, RetargetedAction>;
  /** 动作序列 */
  sequence: string[];
  /** 总持续时间 */
  totalDuration: number;
}

/**
 * 情感状态
 */
export interface EmotionalState {
  /** 时间点 */
  time: number;
  /** 主要情感 */
  primaryEmotion: FacialExpressionType;
  /** 情感强度 */
  intensity: number;
  /** 次要情感 */
  secondaryEmotion?: FacialExpressionType;
  /** 次要强度 */
  secondaryIntensity?: number;
  /** 置信度 */
  confidence: number;
}

/**
 * 时间轴对齐结果
 */
export interface AlignedTimelines {
  /** 动作时间轴 */
  actions: Map<string, TimelineData>;
  /** 面部动画时间轴 */
  facial: Map<string, TimelineData>;
  /** 同步点 */
  syncPoints: SyncPoint[];
  /** 总持续时间 */
  totalDuration: number;
}

/**
 * 时间轴数据
 */
export interface TimelineData {
  /** 名称 */
  name: string;
  /** 开始时间 */
  startTime: number;
  /** 持续时间 */
  duration: number;
  /** 关键帧 */
  keyframes: Keyframe[];
}

/**
 * 关键帧
 */
export interface Keyframe {
  /** 时间 */
  time: number;
  /** 值 */
  value: any;
  /** 插值类型 */
  interpolation: 'linear' | 'step' | 'cubic';
}

/**
 * 同步点
 */
export interface SyncPoint {
  /** 时间 */
  time: number;
  /** 动作名称 */
  actionName: string;
  /** 面部动画名称 */
  facialName: string;
  /** 同步类型 */
  type: 'start' | 'peak' | 'end' | 'transition';
}

/**
 * 匹配的表情集合
 */
export interface MatchedExpressionSet extends Map<string, FacialExpression[]> {}

/**
 * 面部表情
 */
export interface FacialExpression {
  /** 时间 */
  time: number;
  /** 表情 */
  expression: FacialExpressionType;
  /** 强度 */
  intensity: number;
  /** 过渡曲线 */
  transition: number;
}

/**
 * 同步配置
 */
export interface SynchronizationConfig {
  /** 时间对齐设置 */
  timeAlignment: {
    tolerance: number;
    method: 'strict' | 'flexible' | 'adaptive';
  };
  /** 匹配规则 */
  matchingRules: {
    blendingWeights: Map<FacialExpressionType, number>;
    emotionMapping: Map<string, FacialExpressionType>;
    intensityScaling: number;
  };
  /** 过渡平滑设置 */
  transitionSmoothing: {
    enabled: boolean;
    duration: number;
    curve: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
  };
}

/**
 * 同步动画集合
 */
export interface SynchronizedAnimationSet {
  /** 动画集合 */
  animations: Map<string, THREE.AnimationClip>;
  /** 同步映射 */
  synchronizationMap: Map<string, SyncPoint[]>;
  /** 质量指标 */
  qualityMetrics: {
    synchronizationAccuracy: number;
    emotionalCoherence: number;
    transitionSmoothness: number;
  };
  /** 元数据 */
  metadata: {
    totalSyncPoints: number;
    averageExpressionIntensity: number;
    emotionalCoherence: number;
  };
}

/**
 * 同步动画系统
 */
export class SynchronizedAnimationSystem extends System {
  public static readonly TYPE = 'SynchronizedAnimationSystem';

  /** 情感映射表 */
  private static readonly EMOTION_MAPPING = new Map<string, FacialExpressionType>([
    ['happy', FacialExpressionType.HAPPY],
    ['sad', FacialExpressionType.SAD],
    ['angry', FacialExpressionType.ANGRY],
    ['surprised', FacialExpressionType.SURPRISED],
    ['fear', FacialExpressionType.FEAR],
    ['disgust', FacialExpressionType.DISGUST],
    ['neutral', FacialExpressionType.NEUTRAL]
  ]);

  constructor() {
    super(0); // 使用默认优先级
  }

  /**
   * 创建系统实例
   * @returns 新的系统实例
   */
  protected createInstance(): System {
    return new SynchronizedAnimationSystem();
  }

  /**
   * 同步面部动画和身体动作
   * @param bodyActions 身体动作集合
   * @param facialAnimations 面部动画集合
   * @param syncConfig 同步配置
   * @returns 同步动画集合
   */
  public async synchronizeAnimations(
    bodyActions: ActionSet,
    facialAnimations: FacialAnimationSet,
    syncConfig: SynchronizationConfig
  ): Promise<SynchronizedAnimationSet> {
    console.log('开始同步面部动画和身体动作...');

    try {
      // 1. 时间轴对齐
      const alignedTimelines = this.alignAnimationTimelines(
        bodyActions,
        facialAnimations,
        syncConfig.timeAlignment
      );
      console.log('时间轴对齐完成');

      // 2. 情感状态分析
      const emotionalStates = this.analyzeEmotionalStates(bodyActions);
      console.log('情感状态分析完成');

      // 3. 表情与动作智能匹配
      const matchedExpressions = this.matchExpressionsToActions(
        alignedTimelines,
        emotionalStates,
        syncConfig.matchingRules
      );
      console.log('表情动作匹配完成');

      // 4. 生成过渡表情
      const transitionExpressions = this.generateTransitionExpressions(
        matchedExpressions,
        syncConfig.transitionSmoothing
      );
      console.log('过渡表情生成完成');

      // 5. 合成最终动画
      const combinedAnimations = this.combineAnimations(
        alignedTimelines,
        transitionExpressions
      );
      console.log('动画合成完成');

      // 6. 质量验证和优化
      const optimizedAnimations = this.optimizeSynchronizedAnimations(
        combinedAnimations
      );
      console.log('动画优化完成');

      const result: SynchronizedAnimationSet = {
        animations: optimizedAnimations,
        synchronizationMap: this.createSynchronizationMap(optimizedAnimations),
        qualityMetrics: this.calculateSynchronizationQuality(optimizedAnimations),
        metadata: {
          totalSyncPoints: this.countSynchronizationPoints(optimizedAnimations),
          averageExpressionIntensity: this.calculateAverageIntensity(optimizedAnimations),
          emotionalCoherence: this.assessEmotionalCoherence(optimizedAnimations)
        }
      };

      console.log('面部动画同步完成', result.metadata);
      return result;
    } catch (error) {
      console.error('面部动画同步失败:', error);
      throw error;
    }
  }

  /**
   * 对齐动画时间轴
   * @param bodyActions 身体动作
   * @param facialAnimations 面部动画
   * @param alignmentConfig 对齐配置
   * @returns 对齐后的时间轴
   */
  private alignAnimationTimelines(
    bodyActions: ActionSet,
    facialAnimations: FacialAnimationSet,
    alignmentConfig: SynchronizationConfig['timeAlignment']
  ): AlignedTimelines {
    const actionTimelines = new Map<string, TimelineData>();
    const facialTimelines = new Map<string, TimelineData>();
    const syncPoints: SyncPoint[] = [];

    // 处理身体动作时间轴
    let currentTime = 0;
    for (const actionName of bodyActions.sequence) {
      const action = bodyActions.actions.get(actionName);
      if (action) {
        const timeline: TimelineData = {
          name: actionName,
          startTime: currentTime,
          duration: action.duration,
          keyframes: this.extractKeyframesFromAction(action)
        };
        actionTimelines.set(actionName, timeline);
        currentTime += action.duration;
      }
    }

    // 处理面部动画时间轴
    for (const [animName, facialData] of facialAnimations.animations.entries()) {
      const timeline: TimelineData = {
        name: animName,
        startTime: 0,
        duration: facialAnimations.totalDuration,
        keyframes: this.extractKeyframesFromFacial(facialData)
      };
      facialTimelines.set(animName, timeline);
    }

    // 生成同步点
    for (const [actionName, actionTimeline] of actionTimelines.entries()) {
      // 动作开始同步点
      syncPoints.push({
        time: actionTimeline.startTime,
        actionName,
        facialName: 'default',
        type: 'start'
      });

      // 动作结束同步点
      syncPoints.push({
        time: actionTimeline.startTime + actionTimeline.duration,
        actionName,
        facialName: 'default',
        type: 'end'
      });
    }

    return {
      actions: actionTimelines,
      facial: facialTimelines,
      syncPoints,
      totalDuration: Math.max(bodyActions.totalDuration, facialAnimations.totalDuration)
    };
  }

  /**
   * 从动作中提取关键帧
   * @param action 动作数据
   * @returns 关键帧数组
   */
  private extractKeyframesFromAction(action: RetargetedAction): Keyframe[] {
    const keyframes: Keyframe[] = [];

    // 从动画片段的轨道中提取关键帧
    for (const track of action.clip.tracks) {
      for (let i = 0; i < track.times.length; i++) {
        keyframes.push({
          time: track.times[i],
          value: track.values[i],
          interpolation: 'linear'
        });
      }
    }

    return keyframes.sort((a, b) => a.time - b.time);
  }

  /**
   * 从面部动画中提取关键帧
   * @param facialData 面部动画数据
   * @returns 关键帧数组
   */
  private extractKeyframesFromFacial(facialData: FacialAnimationData[]): Keyframe[] {
    const keyframes: Keyframe[] = [];

    for (const data of facialData) {
      keyframes.push({
        time: data.startTime,
        value: {
          expression: data.expression,
          intensity: data.intensity
        },
        interpolation: 'linear'
      });
    }

    return keyframes.sort((a, b) => a.time - b.time);
  }

  /**
   * 分析情感状态
   * @param bodyActions 身体动作
   * @returns 情感状态数组
   */
  private analyzeEmotionalStates(bodyActions: ActionSet): EmotionalState[] {
    const emotionalStates: EmotionalState[] = [];
    let currentTime = 0;

    for (const actionName of bodyActions.sequence) {
      const action = bodyActions.actions.get(actionName);
      if (action) {
        // 基于动作名称分析情感
        const emotion = this.analyzeActionEmotion(actionName);

        emotionalStates.push({
          time: currentTime,
          primaryEmotion: emotion.primary,
          intensity: emotion.intensity,
          secondaryEmotion: emotion.secondary,
          secondaryIntensity: emotion.secondaryIntensity,
          confidence: emotion.confidence
        });

        currentTime += action.duration;
      }
    }

    return emotionalStates;
  }

  /**
   * 分析动作情感
   * @param actionName 动作名称
   * @returns 情感分析结果
   */
  private analyzeActionEmotion(actionName: string): {
    primary: FacialExpressionType;
    intensity: number;
    secondary?: FacialExpressionType;
    secondaryIntensity?: number;
    confidence: number;
  } {
    const name = actionName.toLowerCase();

    // 基于关键词的情感分析
    if (name.includes('happy') || name.includes('joy') || name.includes('dance')) {
      return {
        primary: FacialExpressionType.HAPPY,
        intensity: 0.8,
        confidence: 0.9
      };
    }

    if (name.includes('sad') || name.includes('cry') || name.includes('mourn')) {
      return {
        primary: FacialExpressionType.SAD,
        intensity: 0.7,
        confidence: 0.8
      };
    }

    if (name.includes('angry') || name.includes('fight') || name.includes('attack')) {
      return {
        primary: FacialExpressionType.ANGRY,
        intensity: 0.9,
        confidence: 0.85
      };
    }

    if (name.includes('surprise') || name.includes('shock') || name.includes('jump')) {
      return {
        primary: FacialExpressionType.SURPRISED,
        intensity: 0.8,
        confidence: 0.7
      };
    }

    if (name.includes('fear') || name.includes('scared') || name.includes('afraid')) {
      return {
        primary: FacialExpressionType.FEAR,
        intensity: 0.7,
        confidence: 0.75
      };
    }

    // 默认中性表情
    return {
      primary: FacialExpressionType.NEUTRAL,
      intensity: 0.5,
      confidence: 0.6
    };
  }

  /**
   * 智能表情匹配
   * @param timelines 对齐的时间轴
   * @param emotionalStates 情感状态
   * @param matchingRules 匹配规则
   * @returns 匹配的表情集合
   */
  private matchExpressionsToActions(
    timelines: AlignedTimelines,
    emotionalStates: EmotionalState[],
    matchingRules: SynchronizationConfig['matchingRules']
  ): MatchedExpressionSet {
    const matchedExpressions = new Map<string, FacialExpression[]>();

    for (const [actionName, timeline] of timelines.actions.entries()) {
      const actionExpressions: FacialExpression[] = [];

      // 分析动作的情感特征
      const actionEmotion = this.analyzeActionEmotion(actionName);

      // 根据动作类型选择基础表情
      const baseExpression = actionEmotion.primary;

      // 生成时间序列表情
      const timeStep = 0.1; // 100ms间隔
      for (let t = timeline.startTime; t < timeline.startTime + timeline.duration; t += timeStep) {
        const currentState = this.getEmotionalStateAtTime(emotionalStates, t);
        const expression = this.blendExpressions(
          baseExpression,
          currentState,
          matchingRules.blendingWeights
        );

        actionExpressions.push({
          time: t,
          expression,
          intensity: this.calculateExpressionIntensity(currentState, matchingRules.intensityScaling),
          transition: this.calculateTransitionCurve(t, timeline.startTime, timeline.duration)
        });
      }

      matchedExpressions.set(actionName, actionExpressions);
    }

    return matchedExpressions;
  }

  /**
   * 获取指定时间的情感状态
   * @param emotionalStates 情感状态数组
   * @param time 时间
   * @returns 情感状态
   */
  private getEmotionalStateAtTime(emotionalStates: EmotionalState[], time: number): EmotionalState {
    // 找到最接近的情感状态
    let closestState = emotionalStates[0];
    let minDistance = Math.abs(time - closestState.time);

    for (const state of emotionalStates) {
      const distance = Math.abs(time - state.time);
      if (distance < minDistance) {
        minDistance = distance;
        closestState = state;
      }
    }

    return closestState;
  }

  /**
   * 混合表情
   * @param baseExpression 基础表情
   * @param emotionalState 情感状态
   * @param blendingWeights 混合权重
   * @returns 混合后的表情
   */
  private blendExpressions(
    baseExpression: FacialExpressionType,
    emotionalState: EmotionalState,
    blendingWeights: Map<FacialExpressionType, number>
  ): FacialExpressionType {
    // 简单的表情混合逻辑
    const baseWeight = blendingWeights.get(baseExpression) || 0.5;
    const stateWeight = blendingWeights.get(emotionalState.primaryEmotion) || 0.5;

    // 如果情感状态权重更高，使用情感状态的表情
    if (stateWeight > baseWeight && emotionalState.confidence > 0.7) {
      return emotionalState.primaryEmotion;
    }

    return baseExpression;
  }

  /**
   * 计算表情强度
   * @param emotionalState 情感状态
   * @param intensityScaling 强度缩放
   * @returns 表情强度
   */
  private calculateExpressionIntensity(
    emotionalState: EmotionalState,
    intensityScaling: number
  ): number {
    return Math.min(emotionalState.intensity * intensityScaling, 1.0);
  }

  /**
   * 计算过渡曲线
   * @param currentTime 当前时间
   * @param startTime 开始时间
   * @param duration 持续时间
   * @returns 过渡值
   */
  private calculateTransitionCurve(currentTime: number, startTime: number, duration: number): number {
    const normalizedTime = (currentTime - startTime) / duration;

    // 使用平滑的过渡曲线
    return 0.5 * (1 + Math.cos(Math.PI * normalizedTime));
  }

  /**
   * 生成过渡表情
   * @param matchedExpressions 匹配的表情
   * @param smoothingConfig 平滑配置
   * @returns 过渡表情
   */
  private generateTransitionExpressions(
    matchedExpressions: MatchedExpressionSet,
    smoothingConfig: SynchronizationConfig['transitionSmoothing']
  ): MatchedExpressionSet {
    if (!smoothingConfig.enabled) {
      return matchedExpressions;
    }

    const smoothedExpressions = new Map<string, FacialExpression[]>();

    for (const [actionName, expressions] of matchedExpressions.entries()) {
      const smoothedList: FacialExpression[] = [];

      for (let i = 0; i < expressions.length; i++) {
        const current = expressions[i];
        const smoothed = { ...current };

        // 应用平滑过渡
        if (i > 0 && i < expressions.length - 1) {
          const prev = expressions[i - 1];
          const next = expressions[i + 1];

          // 平滑强度
          smoothed.intensity = this.smoothValue(
            prev.intensity,
            current.intensity,
            next.intensity,
            smoothingConfig.curve
          );

          // 平滑过渡
          smoothed.transition = this.smoothValue(
            prev.transition,
            current.transition,
            next.transition,
            smoothingConfig.curve
          );
        }

        smoothedList.push(smoothed);
      }

      smoothedExpressions.set(actionName, smoothedList);
    }

    return smoothedExpressions;
  }

  /**
   * 平滑数值
   * @param prev 前一个值
   * @param current 当前值
   * @param next 下一个值
   * @param curve 曲线类型
   * @returns 平滑后的值
   */
  private smoothValue(prev: number, current: number, next: number, curve: string): number {
    switch (curve) {
      case 'linear':
        return (prev + current + next) / 3;
      case 'ease-in':
        return current * 0.6 + prev * 0.3 + next * 0.1;
      case 'ease-out':
        return current * 0.6 + next * 0.3 + prev * 0.1;
      case 'ease-in-out':
        return current * 0.5 + (prev + next) * 0.25;
      default:
        return current;
    }
  }

  /**
   * 合成动画
   * @param timelines 时间轴
   * @param expressions 表情
   * @returns 合成的动画
   */
  private combineAnimations(
    timelines: AlignedTimelines,
    expressions: MatchedExpressionSet
  ): Map<string, THREE.AnimationClip> {
    const combinedAnimations = new Map<string, THREE.AnimationClip>();

    for (const [actionName, actionTimeline] of timelines.actions.entries()) {
      const actionExpressions = expressions.get(actionName) || [];

      // 创建合成的动画片段
      const combinedClip = this.createCombinedClip(
        actionName,
        actionTimeline,
        actionExpressions
      );

      combinedAnimations.set(actionName, combinedClip);
    }

    return combinedAnimations;
  }

  /**
   * 创建合成的动画片段
   * @param name 名称
   * @param timeline 时间轴
   * @param expressions 表情数组
   * @returns 动画片段
   */
  private createCombinedClip(
    name: string,
    timeline: TimelineData,
    expressions: FacialExpression[]
  ): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];

    // 创建表情轨道
    if (expressions.length > 0) {
      const times: number[] = [];
      const values: number[] = [];

      for (const expr of expressions) {
        times.push(expr.time - timeline.startTime); // 相对时间
        values.push(expr.intensity);
      }

      const expressionTrack = new THREE.NumberKeyframeTrack(
        'facial.expression',
        times,
        values
      );
      tracks.push(expressionTrack);
    }

    return new THREE.AnimationClip(`${name}_combined`, timeline.duration, tracks);
  }

  /**
   * 优化同步动画
   * @param animations 动画映射
   * @returns 优化后的动画
   */
  private optimizeSynchronizedAnimations(
    animations: Map<string, THREE.AnimationClip>
  ): Map<string, THREE.AnimationClip> {
    const optimized = new Map<string, THREE.AnimationClip>();

    for (const [name, clip] of animations.entries()) {
      // 移除冗余关键帧
      const optimizedTracks = clip.tracks.map(track => this.optimizeTrack(track));

      const optimizedClip = new THREE.AnimationClip(
        clip.name,
        clip.duration,
        optimizedTracks
      );

      optimized.set(name, optimizedClip);
    }

    return optimized;
  }

  /**
   * 优化轨道
   * @param track 轨道
   * @returns 优化后的轨道
   */
  private optimizeTrack(track: THREE.KeyframeTrack): THREE.KeyframeTrack {
    // 简单的优化：移除相邻的重复值
    const times: number[] = [];
    const values: number[] = [];

    for (let i = 0; i < track.times.length; i++) {
      const time = track.times[i];
      const value = track.values[i];

      // 检查是否与前一个值相同
      if (i === 0 || Math.abs(value - values[values.length - 1]) > 0.001) {
        times.push(time);
        values.push(value);
      }
    }

    return new THREE.NumberKeyframeTrack(track.name, times, values);
  }

  /**
   * 创建同步映射
   * @param animations 动画映射
   * @returns 同步映射
   */
  private createSynchronizationMap(
    animations: Map<string, THREE.AnimationClip>
  ): Map<string, SyncPoint[]> {
    const syncMap = new Map<string, SyncPoint[]>();

    for (const [name, clip] of animations.entries()) {
      const syncPoints: SyncPoint[] = [
        {
          time: 0,
          actionName: name,
          facialName: 'default',
          type: 'start'
        },
        {
          time: clip.duration,
          actionName: name,
          facialName: 'default',
          type: 'end'
        }
      ];

      syncMap.set(name, syncPoints);
    }

    return syncMap;
  }

  /**
   * 计算同步质量
   * @param animations 动画映射
   * @returns 质量指标
   */
  private calculateSynchronizationQuality(
    animations: Map<string, THREE.AnimationClip>
  ): SynchronizedAnimationSet['qualityMetrics'] {
    let totalAccuracy = 0;
    let totalCoherence = 0;
    let totalSmoothness = 0;
    let count = 0;

    for (const [name, clip] of animations.entries()) {
      // 简单的质量评估
      const accuracy = clip.tracks.length > 0 ? 0.9 : 0.5;
      const coherence = this.assessEmotionalCoherenceForClip(clip);
      const smoothness = this.assessTransitionSmoothness(clip);

      totalAccuracy += accuracy;
      totalCoherence += coherence;
      totalSmoothness += smoothness;
      count++;
    }

    return {
      synchronizationAccuracy: count > 0 ? totalAccuracy / count : 0,
      emotionalCoherence: count > 0 ? totalCoherence / count : 0,
      transitionSmoothness: count > 0 ? totalSmoothness / count : 0
    };
  }

  /**
   * 评估单个动画片段的情感一致性
   * @param clip 动画片段
   * @returns 一致性评分
   */
  private assessEmotionalCoherenceForClip(clip: THREE.AnimationClip): number {
    // 简单的一致性评估
    if (clip.tracks.length === 0) return 0.5;

    // 检查轨道的连续性
    let coherenceScore = 0;
    for (const track of clip.tracks) {
      if (track.times.length > 1) {
        coherenceScore += 0.8; // 有连续的关键帧
      } else {
        coherenceScore += 0.4; // 只有单个关键帧
      }
    }

    return Math.min(coherenceScore / clip.tracks.length, 1.0);
  }

  /**
   * 评估过渡平滑度
   * @param clip 动画片段
   * @returns 平滑度评分
   */
  private assessTransitionSmoothness(clip: THREE.AnimationClip): number {
    if (clip.tracks.length === 0) return 0.5;

    let smoothnessScore = 0;
    for (const track of clip.tracks) {
      if (track.times.length > 2) {
        // 检查值的变化是否平滑
        let smoothness = 0;
        for (let i = 1; i < track.values.length - 1; i++) {
          const prev = track.values[i - 1];
          const current = track.values[i];
          const next = track.values[i + 1];

          // 计算二阶导数的绝对值（平滑度指标）
          const secondDerivative = Math.abs(next - 2 * current + prev);
          smoothness += 1 / (1 + secondDerivative);
        }
        smoothnessScore += smoothness / (track.values.length - 2);
      } else {
        smoothnessScore += 0.5;
      }
    }

    return smoothnessScore / clip.tracks.length;
  }

  /**
   * 计算同步点数量
   * @param animations 动画映射
   * @returns 同步点总数
   */
  private countSynchronizationPoints(animations: Map<string, THREE.AnimationClip>): number {
    return animations.size * 2; // 每个动画有开始和结束两个同步点
  }

  /**
   * 计算平均表情强度
   * @param animations 动画映射
   * @returns 平均强度
   */
  private calculateAverageIntensity(animations: Map<string, THREE.AnimationClip>): number {
    let totalIntensity = 0;
    let count = 0;

    for (const [name, clip] of animations.entries()) {
      for (const track of clip.tracks) {
        if (track.name.includes('expression')) {
          for (const value of track.values) {
            totalIntensity += value;
            count++;
          }
        }
      }
    }

    return count > 0 ? totalIntensity / count : 0;
  }

  /**
   * 评估情感一致性
   * @param animations 动画映射
   * @returns 一致性评分
   */
  private assessEmotionalCoherence(animations: Map<string, THREE.AnimationClip>): number {
    let totalCoherence = 0;
    let count = 0;

    for (const [name, clip] of animations.entries()) {
      const coherence = this.assessEmotionalCoherenceForClip(clip);
      totalCoherence += coherence;
      count++;
    }

    return count > 0 ? totalCoherence / count : 0;
  }
}
