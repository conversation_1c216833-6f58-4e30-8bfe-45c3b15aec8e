/**
 * 动画调试器
 * 用于动画系统的调试和可视化
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 调试模式
 */
export enum DebugMode {
  /** 无调试 */
  NONE = 'none',
  /** 基本调试 */
  BASIC = 'basic',
  /** 详细调试 */
  DETAILED = 'detailed',
  /** 性能调试 */
  PERFORMANCE = 'performance',
  /** 可视化调试 */
  VISUAL = 'visual'
}

/**
 * 调试信息
 */
export interface DebugInfo {
  /** 时间戳 */
  timestamp: number;
  /** 调试级别 */
  level: 'info' | 'warn' | 'error';
  /** 分类 */
  category: string;
  /** 消息 */
  message: string;
  /** 数据 */
  data?: any;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 帧率 */
  fps: number;
  /** 动画更新时间（毫秒） */
  animationUpdateTime: number;
  /** 混合时间（毫秒） */
  blendTime: number;
  /** 内存使用（MB） */
  memoryUsage: number;
  /** 活跃动画数量 */
  activeAnimations: number;
  /** 活跃混合层数量 */
  activeBlendLayers: number;
}

/**
 * 可视化配置
 */
export interface VisualizationConfig {
  /** 是否显示骨骼 */
  showSkeleton?: boolean;
  /** 是否显示关节 */
  showJoints?: boolean;
  /** 是否显示运动轨迹 */
  showMotionPath?: boolean;
  /** 是否显示混合权重 */
  showBlendWeights?: boolean;
  /** 是否显示边界框 */
  showBoundingBox?: boolean;
  /** 骨骼颜色 */
  skeletonColor?: number;
  /** 关节颜色 */
  jointColor?: number;
  /** 轨迹颜色 */
  pathColor?: number;
}

/**
 * 调试配置
 */
export interface DebuggerConfig {
  /** 调试模式 */
  mode?: DebugMode;
  /** 是否启用控制台输出 */
  enableConsole?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 性能监控间隔（毫秒） */
  performanceInterval?: number;
  /** 最大日志条数 */
  maxLogEntries?: number;
  /** 可视化配置 */
  visualization?: VisualizationConfig;
}

/**
 * 动画调试器
 */
export class AnimationDebugger extends EventEmitter {
  /** 单例实例 */
  private static instance: AnimationDebugger | null = null;

  /** 配置 */
  private config: DebuggerConfig;
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: DebuggerConfig = {
    mode: DebugMode.NONE,
    enableConsole: true,
    enablePerformanceMonitoring: false,
    performanceInterval: 1000,
    maxLogEntries: 1000,
    visualization: {
      showSkeleton: false,
      showJoints: false,
      showMotionPath: false,
      showBlendWeights: false,
      showBoundingBox: false,
      skeletonColor: 0x00ff00,
      jointColor: 0xff0000,
      pathColor: 0x0000ff
    }
  };

  /** 调试日志 */
  private logs: DebugInfo[] = [];
  /** 性能指标 */
  private metrics: PerformanceMetrics = {
    fps: 0,
    animationUpdateTime: 0,
    blendTime: 0,
    memoryUsage: 0,
    activeAnimations: 0,
    activeBlendLayers: 0
  };
  /** 性能监控定时器 */
  private performanceTimer: NodeJS.Timeout | null = null;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 上次性能检查时间 */
  private lastPerformanceCheck: number = 0;
  /** 可视化对象 */
  private visualObjects: Map<string, THREE.Object3D> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<DebuggerConfig>) {
    super();
    this.config = { ...AnimationDebugger.DEFAULT_CONFIG, ...config };
  }

  /**
   * 获取单例实例
   * @param config 配置
   * @returns 调试器实例
   */
  public static getInstance(config?: Partial<DebuggerConfig>): AnimationDebugger {
    if (!AnimationDebugger.instance) {
      AnimationDebugger.instance = new AnimationDebugger(config);
    }
    return AnimationDebugger.instance;
  }

  /**
   * 启用调试
   * @param mode 调试模式
   */
  public enable(mode: DebugMode = DebugMode.BASIC): void {
    this.config.mode = mode;

    if (mode !== DebugMode.NONE) {
      this.log('info', 'debugger', `调试器已启用: ${mode}`);

      if (this.config.enablePerformanceMonitoring) {
        this.startPerformanceMonitoring();
      }
    }
  }

  /**
   * 禁用调试
   */
  public disable(): void {
    this.config.mode = DebugMode.NONE;
    this.stopPerformanceMonitoring();
    this.clearVisualizations();
    this.log('info', 'debugger', '调试器已禁用');
  }

  /**
   * 记录日志
   * @param level 级别
   * @param category 分类
   * @param message 消息
   * @param data 数据
   */
  public log(level: 'info' | 'warn' | 'error', category: string, message: string, data?: any): void {
    if (this.config.mode === DebugMode.NONE) return;

    const logEntry: DebugInfo = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data
    };

    this.logs.push(logEntry);

    // 限制日志数量
    if (this.logs.length > this.config.maxLogEntries!) {
      this.logs.shift();
    }

    // 控制台输出
    if (this.config.enableConsole) {
      const prefix = `[${category}]`;
      switch (level) {
        case 'info':
          console.log(prefix, message, data || '');
          break;
        case 'warn':
          console.warn(prefix, message, data || '');
          break;
        case 'error':
          console.error(prefix, message, data || '');
          break;
      }
    }

    // 发出事件
    this.emit('log', logEntry);
  }

  /**
   * 记录动画开始
   * @param animationName 动画名称
   * @param duration 持续时间
   */
  public logAnimationStart(animationName: string, duration: number): void {
    this.log('info', 'animation', `动画开始: ${animationName}`, { duration });
  }

  /**
   * 记录动画结束
   * @param animationName 动画名称
   * @param actualDuration 实际持续时间
   */
  public logAnimationEnd(animationName: string, actualDuration: number): void {
    this.log('info', 'animation', `动画结束: ${animationName}`, { actualDuration });
  }

  /**
   * 记录混合开始
   * @param fromAnimation 源动画
   * @param toAnimation 目标动画
   * @param blendTime 混合时间
   */
  public logBlendStart(fromAnimation: string, toAnimation: string, blendTime: number): void {
    this.log('info', 'blend', `混合开始: ${fromAnimation} -> ${toAnimation}`, { blendTime });
  }

  /**
   * 记录混合结束
   * @param fromAnimation 源动画
   * @param toAnimation 目标动画
   */
  public logBlendEnd(fromAnimation: string, toAnimation: string): void {
    this.log('info', 'blend', `混合结束: ${fromAnimation} -> ${toAnimation}`);
  }

  /**
   * 记录性能指标
   * @param metrics 性能指标
   */
  public logPerformanceMetrics(metrics: Partial<PerformanceMetrics>): void {
    this.metrics = { ...this.metrics, ...metrics };

    if (this.config.mode === DebugMode.PERFORMANCE || this.config.mode === DebugMode.DETAILED) {
      this.log('info', 'performance', '性能指标更新', this.metrics);
    }

    this.emit('performanceUpdate', this.metrics);
  }

  /**
   * 记录错误
   * @param category 分类
   * @param error 错误
   */
  public logError(category: string, error: Error): void {
    this.log('error', category, error.message, {
      stack: error.stack,
      name: error.name
    });
  }

  /**
   * 记录警告
   * @param category 分类
   * @param message 消息
   * @param data 数据
   */
  public logWarning(category: string, message: string, data?: any): void {
    this.log('warn', category, message, data);
  }

  /**
   * 获取日志
   * @param category 分类过滤
   * @param level 级别过滤
   * @returns 日志数组
   */
  public getLogs(category?: string, level?: 'info' | 'warn' | 'error'): DebugInfo[] {
    let filteredLogs = this.logs;

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    return filteredLogs;
  }

  /**
   * 清除日志
   */
  public clearLogs(): void {
    this.logs = [];
    this.log('info', 'debugger', '日志已清除');
  }

  /**
   * 获取性能指标
   * @returns 性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 创建骨骼可视化
   * @param skeleton 骨骼对象
   * @param scene 场景
   * @returns 骨骼辅助对象
   */
  public createSkeletonVisualization(skeleton: THREE.Skeleton, scene: THREE.Scene): THREE.SkeletonHelper {
    const helper = new THREE.SkeletonHelper(skeleton.bones[0]);
    helper.material.color.setHex(this.config.visualization!.skeletonColor!);
    scene.add(helper);

    this.visualObjects.set('skeleton', helper);
    this.log('info', 'visualization', '骨骼可视化已创建');

    return helper;
  }

  /**
   * 创建运动轨迹可视化
   * @param points 轨迹点
   * @param scene 场景
   * @returns 轨迹对象
   */
  public createMotionPathVisualization(points: THREE.Vector3[], scene: THREE.Scene): THREE.Line {
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    const material = new THREE.LineBasicMaterial({ 
      color: this.config.visualization!.pathColor! 
    });
    const line = new THREE.Line(geometry, material);
    scene.add(line);

    this.visualObjects.set('motionPath', line);
    this.log('info', 'visualization', '运动轨迹可视化已创建');

    return line;
  }

  /**
   * 创建边界框可视化
   * @param boundingBox 边界框
   * @param scene 场景
   * @returns 边界框辅助对象
   */
  public createBoundingBoxVisualization(boundingBox: THREE.Box3, scene: THREE.Scene): THREE.Box3Helper {
    const helper = new THREE.Box3Helper(boundingBox, 0xffff00);
    scene.add(helper);

    this.visualObjects.set('boundingBox', helper);
    this.log('info', 'visualization', '边界框可视化已创建');

    return helper;
  }

  /**
   * 更新帧计数
   */
  public updateFrameCount(): void {
    this.frameCount++;
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    this.stopPerformanceMonitoring();
    this.lastPerformanceCheck = Date.now();

    this.performanceTimer = setInterval(() => {
      const now = Date.now();
      const deltaTime = now - this.lastPerformanceCheck;
      
      // 计算FPS
      const fps = Math.round((this.frameCount * 1000) / deltaTime);
      this.frameCount = 0;
      this.lastPerformanceCheck = now;

      // 更新性能指标
      this.logPerformanceMetrics({ fps });

      // 内存使用情况（如果支持）
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // MB
        this.logPerformanceMetrics({ memoryUsage });
      }

    }, this.config.performanceInterval!);

    this.log('info', 'performance', '性能监控已启动');
  }

  /**
   * 停止性能监控
   */
  private stopPerformanceMonitoring(): void {
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;
      this.log('info', 'performance', '性能监控已停止');
    }
  }

  /**
   * 清除可视化对象
   */
  private clearVisualizations(): void {
    for (const [name, object] of this.visualObjects.entries()) {
      if (object.parent) {
        object.parent.remove(object);
      }
      this.log('info', 'visualization', `已移除可视化对象: ${name}`);
    }
    this.visualObjects.clear();
  }

  /**
   * 导出调试数据
   * @returns 调试数据JSON
   */
  public exportDebugData(): string {
    const data = {
      config: this.config,
      logs: this.logs,
      metrics: this.metrics,
      timestamp: Date.now()
    };

    return JSON.stringify(data, null, 2);
  }

  /**
   * 导入调试数据
   * @param jsonData 调试数据JSON
   */
  public importDebugData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.logs) {
        this.logs = data.logs;
      }
      
      if (data.metrics) {
        this.metrics = data.metrics;
      }

      this.log('info', 'debugger', '调试数据已导入');
    } catch (error) {
      this.logError('debugger', error as Error);
    }
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<DebuggerConfig>): void {
    this.config = { ...this.config, ...config };
    this.log('info', 'debugger', '配置已更新', config);
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): DebuggerConfig {
    return { ...this.config };
  }

  /**
   * 销毁调试器
   */
  public destroy(): void {
    this.disable();
    this.clearLogs();
    this.removeAllListeners();
  }
}
