/**
 * 神经网络感知处理器
 *
 * 基于深度学习的感知数据处理系统，包括：
 * - 卷积神经网络(CNN)用于视觉处理
 * - 循环神经网络(RNN/LSTM)用于时序数据
 * - 注意力机制用于特征选择
 * - 多模态融合网络
 */

import { EventEmitter } from 'events';
import {
  VisualPerceptionData,
  AuditoryPerceptionData
} from '../perception/MultiModalPerceptionSystem';

/**
 * 神经网络层类型
 */
export enum LayerType {
  DENSE = 'dense',
  CONV2D = 'conv2d',
  LSTM = 'lstm',
  ATTENTION = 'attention',
  POOLING = 'pooling',
  DROPOUT = 'dropout',
  BATCH_NORM = 'batch_norm'
}

/**
 * 激活函数类型
 */
export enum ActivationType {
  RELU = 'relu',
  SIGMOID = 'sigmoid',
  TANH = 'tanh',
  SOFTMAX = 'softmax',
  LEAKY_RELU = 'leaky_relu',
  SWISH = 'swish',
  LINEAR = 'linear'
}

/**
 * 神经网络层配置
 */
export interface LayerConfig {
  type: LayerType;
  inputShape: number[];
  outputShape: number[];
  activation: ActivationType;
  parameters: { [key: string]: any };
}

/**
 * 张量数据结构
 */
export class Tensor {
  public data: Float32Array;
  public shape: number[];
  public size: number;

  constructor(data: Float32Array | number[], shape: number[]) {
    this.shape = shape;
    this.size = shape.reduce((a, b) => a * b, 1);

    if (data instanceof Float32Array) {
      this.data = data;
    } else {
      this.data = new Float32Array(data);
    }

    if (this.data.length !== this.size) {
      throw new Error(`数据长度 ${this.data.length} 与形状 ${shape} 不匹配`);
    }
  }

  /**
   * 获取指定位置的值
   */
  public get(...indices: number[]): number {
    const index = this.getIndex(indices);
    return this.data[index];
  }

  /**
   * 设置指定位置的值
   */
  public set(value: number, ...indices: number[]): void {
    const index = this.getIndex(indices);
    this.data[index] = value;
  }

  /**
   * 计算多维索引对应的一维索引
   */
  private getIndex(indices: number[]): number {
    let index = 0;
    let stride = 1;

    for (let i = this.shape.length - 1; i >= 0; i--) {
      index += indices[i] * stride;
      stride *= this.shape[i];
    }

    return index;
  }

  /**
   * 重塑张量
   */
  public reshape(newShape: number[]): Tensor {
    const newSize = newShape.reduce((a, b) => a * b, 1);
    if (newSize !== this.size) {
      throw new Error(`无法将大小 ${this.size} 重塑为 ${newShape}`);
    }

    return new Tensor(this.data, newShape);
  }

  /**
   * 克隆张量
   */
  public clone(): Tensor {
    return new Tensor(new Float32Array(this.data), [...this.shape]);
  }

  /**
   * 应用函数到所有元素
   */
  public apply(fn: (value: number) => number): Tensor {
    const newData = new Float32Array(this.data.length);
    for (let i = 0; i < this.data.length; i++) {
      newData[i] = fn(this.data[i]);
    }
    return new Tensor(newData, this.shape);
  }
}

/**
 * 激活函数
 */
export class ActivationFunctions {
  public static relu(x: number): number {
    return Math.max(0, x);
  }

  public static sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x));
  }

  public static tanh(x: number): number {
    return Math.tanh(x);
  }

  public static leakyRelu(x: number, alpha: number = 0.01): number {
    return x > 0 ? x : alpha * x;
  }

  public static swish(x: number): number {
    return x * ActivationFunctions.sigmoid(x);
  }

  public static linear(x: number): number {
    return x;
  }

  public static softmax(input: Tensor): Tensor {
    const maxVal = Math.max(...Array.from(input.data));
    const expValues = input.apply(x => Math.exp(x - maxVal));
    const sum = expValues.data.reduce((a, b) => a + b, 0);
    return expValues.apply(x => x / sum);
  }

  public static apply(input: Tensor, activation: ActivationType): Tensor {
    switch (activation) {
      case ActivationType.RELU:
        return input.apply(ActivationFunctions.relu);
      case ActivationType.SIGMOID:
        return input.apply(ActivationFunctions.sigmoid);
      case ActivationType.TANH:
        return input.apply(ActivationFunctions.tanh);
      case ActivationType.LEAKY_RELU:
        return input.apply(x => ActivationFunctions.leakyRelu(x));
      case ActivationType.SWISH:
        return input.apply(ActivationFunctions.swish);
      case ActivationType.LINEAR:
        return input.apply(ActivationFunctions.linear);
      case ActivationType.SOFTMAX:
        return ActivationFunctions.softmax(input);
      default:
        return input;
    }
  }
}

/**
 * 神经网络层基类
 */
export abstract class NeuralLayer {
  protected config: LayerConfig;
  protected weights?: Tensor;
  protected biases?: Tensor;
  protected trainable: boolean = true;

  constructor(config: LayerConfig) {
    this.config = config;
    this.initializeParameters();
  }

  /**
   * 初始化参数
   */
  protected abstract initializeParameters(): void;

  /**
   * 前向传播
   */
  public abstract forward(input: Tensor): Tensor;

  /**
   * 获取输出形状
   */
  public getOutputShape(): number[] {
    return this.config.outputShape;
  }

  /**
   * 获取参数数量
   */
  public getParameterCount(): number {
    let count = 0;
    if (this.weights) count += this.weights.size;
    if (this.biases) count += this.biases.size;
    return count;
  }
}

/**
 * 全连接层
 */
export class DenseLayer extends NeuralLayer {
  protected initializeParameters(): void {
    const inputSize = this.config.inputShape[0];
    const outputSize = this.config.outputShape[0];

    // Xavier初始化
    const limit = Math.sqrt(6 / (inputSize + outputSize));
    const weightData = new Float32Array(inputSize * outputSize);
    const biasData = new Float32Array(outputSize);

    for (let i = 0; i < weightData.length; i++) {
      weightData[i] = (Math.random() * 2 - 1) * limit;
    }

    biasData.fill(0);

    this.weights = new Tensor(weightData, [inputSize, outputSize]);
    this.biases = new Tensor(biasData, [outputSize]);
  }

  public forward(input: Tensor): Tensor {
    if (!this.weights || !this.biases) {
      throw new Error('层参数未初始化');
    }

    const inputSize = input.size;
    const outputSize = this.biases.size;
    const outputData = new Float32Array(outputSize);

    // 矩阵乘法 + 偏置
    for (let i = 0; i < outputSize; i++) {
      let sum = this.biases.data[i];
      for (let j = 0; j < inputSize; j++) {
        sum += input.data[j] * this.weights.data[j * outputSize + i];
      }
      outputData[i] = sum;
    }

    const output = new Tensor(outputData, [outputSize]);

    // 应用激活函数
    return ActivationFunctions.apply(output, this.config.activation);
  }
}

/**
 * 卷积层（简化2D实现）
 */
export class Conv2DLayer extends NeuralLayer {
  private kernelSize: number;
  private stride: number;
  private padding: number;
  private filters: number;

  constructor(config: LayerConfig) {
    super(config);
    this.kernelSize = config.parameters.kernelSize || 3;
    this.stride = config.parameters.stride || 1;
    this.padding = config.parameters.padding || 0;
    this.filters = config.parameters.filters || 32;
  }

  protected initializeParameters(): void {
    const [_inputHeight, _inputWidth, inputChannels] = this.config.inputShape;
    const kernelSize = this.kernelSize;

    // 卷积核权重
    const weightSize = kernelSize * kernelSize * inputChannels * this.filters;
    const weightData = new Float32Array(weightSize);

    // He初始化
    const limit = Math.sqrt(2 / (kernelSize * kernelSize * inputChannels));
    for (let i = 0; i < weightSize; i++) {
      weightData[i] = (Math.random() * 2 - 1) * limit;
    }

    this.weights = new Tensor(weightData, [kernelSize, kernelSize, inputChannels, this.filters]);
    this.biases = new Tensor(new Float32Array(this.filters), [this.filters]);
  }

  public forward(input: Tensor): Tensor {
    if (!this.weights || !this.biases) {
      throw new Error('层参数未初始化');
    }

    const [inputHeight, inputWidth, inputChannels] = input.shape;
    const outputHeight = Math.floor((inputHeight + 2 * this.padding - this.kernelSize) / this.stride) + 1;
    const outputWidth = Math.floor((inputWidth + 2 * this.padding - this.kernelSize) / this.stride) + 1;

    const outputData = new Float32Array(outputHeight * outputWidth * this.filters);
    const output = new Tensor(outputData, [outputHeight, outputWidth, this.filters]);

    // 卷积操作（简化实现）
    for (let f = 0; f < this.filters; f++) {
      for (let y = 0; y < outputHeight; y++) {
        for (let x = 0; x < outputWidth; x++) {
          let sum = this.biases.data[f];

          for (let ky = 0; ky < this.kernelSize; ky++) {
            for (let kx = 0; kx < this.kernelSize; kx++) {
              for (let c = 0; c < inputChannels; c++) {
                const inputY = y * this.stride + ky - this.padding;
                const inputX = x * this.stride + kx - this.padding;

                if (inputY >= 0 && inputY < inputHeight && inputX >= 0 && inputX < inputWidth) {
                  const inputValue = input.get(inputY, inputX, c);
                  const weightValue = this.weights!.get(ky, kx, c, f);
                  sum += inputValue * weightValue;
                }
              }
            }
          }

          output.set(sum, y, x, f);
        }
      }
    }

    // 应用激活函数
    return ActivationFunctions.apply(output, this.config.activation);
  }
}

/**
 * LSTM层（简化实现）
 */
export class LSTMLayer extends NeuralLayer {
  private hiddenSize: number;
  private sequenceLength: number;

  // LSTM门权重
  private forgetGateWeights?: Tensor;
  private inputGateWeights?: Tensor;
  private candidateWeights?: Tensor;
  private outputGateWeights?: Tensor;

  constructor(config: LayerConfig) {
    super(config);
    this.hiddenSize = config.parameters.hiddenSize || 64;
    this.sequenceLength = config.parameters.sequenceLength || 10;
  }

  protected initializeParameters(): void {
    const inputSize = this.config.inputShape[1]; // [sequenceLength, inputSize]
    const hiddenSize = this.hiddenSize;
    const totalInputSize = inputSize + hiddenSize; // 输入 + 隐藏状态

    // 初始化各个门的权重
    const initWeights = (size: number) => {
      const data = new Float32Array(size);
      const limit = Math.sqrt(1 / totalInputSize);
      for (let i = 0; i < size; i++) {
        data[i] = (Math.random() * 2 - 1) * limit;
      }
      return data;
    };

    this.forgetGateWeights = new Tensor(initWeights(totalInputSize * hiddenSize), [totalInputSize, hiddenSize]);
    this.inputGateWeights = new Tensor(initWeights(totalInputSize * hiddenSize), [totalInputSize, hiddenSize]);
    this.candidateWeights = new Tensor(initWeights(totalInputSize * hiddenSize), [totalInputSize, hiddenSize]);
    this.outputGateWeights = new Tensor(initWeights(totalInputSize * hiddenSize), [totalInputSize, hiddenSize]);

    this.biases = new Tensor(new Float32Array(hiddenSize * 4), [hiddenSize * 4]);
  }

  public forward(input: Tensor): Tensor {
    if (!this.forgetGateWeights || !this.inputGateWeights ||
        !this.candidateWeights || !this.outputGateWeights || !this.biases) {
      throw new Error('LSTM参数未初始化');
    }

    const [sequenceLength, inputSize] = input.shape;
    const hiddenSize = this.hiddenSize;

    // 初始化隐藏状态和细胞状态
    let hiddenState = new Tensor(new Float32Array(hiddenSize), [hiddenSize]);
    let cellState = new Tensor(new Float32Array(hiddenSize), [hiddenSize]);

    const outputs: Tensor[] = [];

    // 处理序列中的每个时间步
    for (let t = 0; t < sequenceLength; t++) {
      // 获取当前时间步的输入
      const currentInput = new Tensor(
        input.data.slice(t * inputSize, (t + 1) * inputSize),
        [inputSize]
      );

      // 连接输入和隐藏状态
      const combined = new Tensor(
        new Float32Array(inputSize + hiddenSize),
        [inputSize + hiddenSize]
      );
      combined.data.set(currentInput.data, 0);
      combined.data.set(hiddenState.data, inputSize);

      // 计算各个门
      const forgetGate = this.computeGate(combined, this.forgetGateWeights, 0);
      const inputGate = this.computeGate(combined, this.inputGateWeights, hiddenSize);
      const candidateValues = this.computeGate(combined, this.candidateWeights, hiddenSize * 2, ActivationType.TANH);
      const outputGate = this.computeGate(combined, this.outputGateWeights, hiddenSize * 3);

      // 更新细胞状态
      const newCellState = new Tensor(new Float32Array(hiddenSize), [hiddenSize]);
      for (let i = 0; i < hiddenSize; i++) {
        newCellState.data[i] = forgetGate.data[i] * cellState.data[i] +
                               inputGate.data[i] * candidateValues.data[i];
      }
      cellState = newCellState;

      // 更新隐藏状态
      const newHiddenState = new Tensor(new Float32Array(hiddenSize), [hiddenSize]);
      for (let i = 0; i < hiddenSize; i++) {
        newHiddenState.data[i] = outputGate.data[i] * Math.tanh(cellState.data[i]);
      }
      hiddenState = newHiddenState;

      outputs.push(hiddenState.clone());
    }

    // 返回最后的隐藏状态或所有输出
    return outputs[outputs.length - 1];
  }

  /**
   * 计算LSTM门
   */
  private computeGate(
    input: Tensor,
    weights: Tensor,
    biasOffset: number,
    activation: ActivationType = ActivationType.SIGMOID
  ): Tensor {
    const hiddenSize = this.hiddenSize;
    const outputData = new Float32Array(hiddenSize);

    for (let i = 0; i < hiddenSize; i++) {
      let sum = this.biases!.data[biasOffset + i];
      for (let j = 0; j < input.size; j++) {
        sum += input.data[j] * weights.data[j * hiddenSize + i];
      }
      outputData[i] = sum;
    }

    const output = new Tensor(outputData, [hiddenSize]);
    return ActivationFunctions.apply(output, activation);
  }
}

/**
 * 注意力层
 */
export class AttentionLayer extends NeuralLayer {
  private headCount: number;
  private keyDim: number;

  private queryWeights?: Tensor;
  private keyWeights?: Tensor;
  private valueWeights?: Tensor;
  private outputWeights?: Tensor;

  constructor(config: LayerConfig) {
    super(config);
    this.headCount = config.parameters.headCount || 8;
    this.keyDim = config.parameters.keyDim || 64;
  }

  protected initializeParameters(): void {
    const inputSize = this.config.inputShape[1]; // [sequenceLength, inputSize]
    const totalDim = this.headCount * this.keyDim;

    const initWeights = (inputDim: number, outputDim: number) => {
      const data = new Float32Array(inputDim * outputDim);
      const limit = Math.sqrt(1 / inputDim);
      for (let i = 0; i < data.length; i++) {
        data[i] = (Math.random() * 2 - 1) * limit;
      }
      return new Tensor(data, [inputDim, outputDim]);
    };

    this.queryWeights = initWeights(inputSize, totalDim);
    this.keyWeights = initWeights(inputSize, totalDim);
    this.valueWeights = initWeights(inputSize, totalDim);
    this.outputWeights = initWeights(totalDim, inputSize);
  }

  public forward(input: Tensor): Tensor {
    if (!this.queryWeights || !this.keyWeights || !this.valueWeights || !this.outputWeights) {
      throw new Error('注意力层参数未初始化');
    }

    const [_sequenceLength, _inputSize] = input.shape;

    // 计算Q, K, V
    const queries = this.matmul(input, this.queryWeights);
    const keys = this.matmul(input, this.keyWeights);
    const values = this.matmul(input, this.valueWeights);

    // 多头注意力
    const attentionOutput = this.multiHeadAttention(queries, keys, values);

    // 输出投影
    return this.matmul(attentionOutput, this.outputWeights);
  }

  /**
   * 多头注意力计算
   */
  private multiHeadAttention(queries: Tensor, keys: Tensor, values: Tensor): Tensor {
    const [_sequenceLength, totalDim] = queries.shape;
    const headDim = totalDim / this.headCount;

    const outputs: Tensor[] = [];

    for (let h = 0; h < this.headCount; h++) {
      const startIdx = h * headDim;
      const endIdx = (h + 1) * headDim;

      // 提取当前头的Q, K, V
      const headQueries = this.sliceTensor(queries, startIdx, endIdx);
      const headKeys = this.sliceTensor(keys, startIdx, endIdx);
      const headValues = this.sliceTensor(values, startIdx, endIdx);

      // 计算注意力分数
      const scores = this.computeAttentionScores(headQueries, headKeys);

      // 应用注意力权重到值
      const headOutput = this.applyAttention(scores, headValues);
      outputs.push(headOutput);
    }

    // 连接所有头的输出
    return this.concatenateTensors(outputs);
  }

  /**
   * 计算注意力分数
   */
  private computeAttentionScores(queries: Tensor, keys: Tensor): Tensor {
    const [sequenceLength, headDim] = queries.shape;
    const scoresData = new Float32Array(sequenceLength * sequenceLength);

    for (let i = 0; i < sequenceLength; i++) {
      for (let j = 0; j < sequenceLength; j++) {
        let score = 0;
        for (let k = 0; k < headDim; k++) {
          score += queries.get(i, k) * keys.get(j, k);
        }
        score /= Math.sqrt(headDim); // 缩放
        scoresData[i * sequenceLength + j] = score;
      }
    }

    const scores = new Tensor(scoresData, [sequenceLength, sequenceLength]);

    // 应用softmax
    const softmaxScores = new Tensor(new Float32Array(sequenceLength * sequenceLength), [sequenceLength, sequenceLength]);
    for (let i = 0; i < sequenceLength; i++) {
      const rowData = new Float32Array(sequenceLength);
      for (let j = 0; j < sequenceLength; j++) {
        rowData[j] = scores.get(i, j);
      }
      const rowTensor = new Tensor(rowData, [sequenceLength]);
      const softmaxRow = ActivationFunctions.softmax(rowTensor);

      for (let j = 0; j < sequenceLength; j++) {
        softmaxScores.set(softmaxRow.data[j], i, j);
      }
    }

    return softmaxScores;
  }

  /**
   * 应用注意力权重
   */
  private applyAttention(scores: Tensor, values: Tensor): Tensor {
    const [sequenceLength, headDim] = values.shape;
    const outputData = new Float32Array(sequenceLength * headDim);

    for (let i = 0; i < sequenceLength; i++) {
      for (let k = 0; k < headDim; k++) {
        let sum = 0;
        for (let j = 0; j < sequenceLength; j++) {
          sum += scores.get(i, j) * values.get(j, k);
        }
        outputData[i * headDim + k] = sum;
      }
    }

    return new Tensor(outputData, [sequenceLength, headDim]);
  }

  /**
   * 矩阵乘法
   */
  private matmul(a: Tensor, b: Tensor): Tensor {
    const [aRows, aCols] = a.shape;
    const [bRows, bCols] = b.shape;

    if (aCols !== bRows) {
      throw new Error(`矩阵维度不匹配: ${a.shape} x ${b.shape}`);
    }

    const resultData = new Float32Array(aRows * bCols);

    for (let i = 0; i < aRows; i++) {
      for (let j = 0; j < bCols; j++) {
        let sum = 0;
        for (let k = 0; k < aCols; k++) {
          sum += a.get(i, k) * b.get(k, j);
        }
        resultData[i * bCols + j] = sum;
      }
    }

    return new Tensor(resultData, [aRows, bCols]);
  }

  /**
   * 切片张量
   */
  private sliceTensor(tensor: Tensor, startIdx: number, endIdx: number): Tensor {
    const [rows, _cols] = tensor.shape;
    const sliceWidth = endIdx - startIdx;
    const sliceData = new Float32Array(rows * sliceWidth);

    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < sliceWidth; j++) {
        sliceData[i * sliceWidth + j] = tensor.get(i, startIdx + j);
      }
    }

    return new Tensor(sliceData, [rows, sliceWidth]);
  }

  /**
   * 连接张量
   */
  private concatenateTensors(tensors: Tensor[]): Tensor {
    if (tensors.length === 0) {
      throw new Error('无法连接空张量数组');
    }

    const [rows] = tensors[0].shape;
    const totalCols = tensors.reduce((sum, t) => sum + t.shape[1], 0);
    const resultData = new Float32Array(rows * totalCols);

    let colOffset = 0;
    for (const tensor of tensors) {
      const [, cols] = tensor.shape;
      for (let i = 0; i < rows; i++) {
        for (let j = 0; j < cols; j++) {
          resultData[i * totalCols + colOffset + j] = tensor.get(i, j);
        }
      }
      colOffset += cols;
    }

    return new Tensor(resultData, [rows, totalCols]);
  }
}

/**
 * 池化层
 */
export class PoolingLayer extends NeuralLayer {
  private poolSize: number;
  private stride: number;
  private poolType: 'max' | 'average';

  constructor(config: LayerConfig) {
    super(config);
    this.poolSize = config.parameters.poolSize || 2;
    this.stride = config.parameters.stride || 2;
    this.poolType = config.parameters.poolType || 'max';
  }

  protected initializeParameters(): void {
    // 池化层不需要可训练参数
  }

  public forward(input: Tensor): Tensor {
    const [inputHeight, inputWidth, channels] = input.shape;
    const outputHeight = Math.floor((inputHeight - this.poolSize) / this.stride) + 1;
    const outputWidth = Math.floor((inputWidth - this.poolSize) / this.stride) + 1;

    const outputData = new Float32Array(outputHeight * outputWidth * channels);
    const output = new Tensor(outputData, [outputHeight, outputWidth, channels]);

    for (let c = 0; c < channels; c++) {
      for (let y = 0; y < outputHeight; y++) {
        for (let x = 0; x < outputWidth; x++) {
          const startY = y * this.stride;
          const startX = x * this.stride;

          let poolValue = this.poolType === 'max' ? -Infinity : 0;
          let count = 0;

          for (let py = 0; py < this.poolSize; py++) {
            for (let px = 0; px < this.poolSize; px++) {
              const inputY = startY + py;
              const inputX = startX + px;

              if (inputY < inputHeight && inputX < inputWidth) {
                const value = input.get(inputY, inputX, c);

                if (this.poolType === 'max') {
                  poolValue = Math.max(poolValue, value);
                } else {
                  poolValue += value;
                  count++;
                }
              }
            }
          }

          if (this.poolType === 'average' && count > 0) {
            poolValue /= count;
          }

          output.set(poolValue, y, x, c);
        }
      }
    }

    return output;
  }
}

/**
 * 批归一化层
 */
export class BatchNormLayer extends NeuralLayer {
  private epsilon: number;
  private momentum: number;
  private runningMean?: Tensor;
  private runningVar?: Tensor;
  private gamma?: Tensor; // 缩放参数
  private beta?: Tensor;  // 偏移参数

  constructor(config: LayerConfig) {
    super(config);
    this.epsilon = config.parameters.epsilon || 1e-5;
    this.momentum = config.parameters.momentum || 0.9;
  }

  protected initializeParameters(): void {
    const featureSize = this.config.inputShape[this.config.inputShape.length - 1];

    // 初始化可训练参数
    this.gamma = new Tensor(new Float32Array(featureSize).fill(1), [featureSize]);
    this.beta = new Tensor(new Float32Array(featureSize).fill(0), [featureSize]);

    // 初始化运行时统计
    this.runningMean = new Tensor(new Float32Array(featureSize).fill(0), [featureSize]);
    this.runningVar = new Tensor(new Float32Array(featureSize).fill(1), [featureSize]);
  }

  public forward(input: Tensor): Tensor {
    if (!this.gamma || !this.beta || !this.runningMean || !this.runningVar) {
      throw new Error('批归一化参数未初始化');
    }

    const batchSize = input.shape[0] || 1;
    const featureSize = input.shape[input.shape.length - 1];
    const outputData = new Float32Array(input.size);

    // 计算批次统计
    const batchMean = new Float32Array(featureSize);
    const batchVar = new Float32Array(featureSize);

    // 计算均值
    for (let f = 0; f < featureSize; f++) {
      let sum = 0;
      let count = 0;

      for (let i = 0; i < input.size; i += featureSize) {
        sum += input.data[i + f];
        count++;
      }

      batchMean[f] = sum / count;
    }

    // 计算方差
    for (let f = 0; f < featureSize; f++) {
      let sum = 0;
      let count = 0;

      for (let i = 0; i < input.size; i += featureSize) {
        const diff = input.data[i + f] - batchMean[f];
        sum += diff * diff;
        count++;
      }

      batchVar[f] = sum / count;
    }

    // 归一化并应用缩放和偏移
    for (let i = 0; i < input.size; i++) {
      const featureIndex = i % featureSize;
      const normalized = (input.data[i] - batchMean[featureIndex]) /
                        Math.sqrt(batchVar[featureIndex] + this.epsilon);
      outputData[i] = this.gamma.data[featureIndex] * normalized + this.beta.data[featureIndex];
    }

    // 更新运行时统计
    for (let f = 0; f < featureSize; f++) {
      this.runningMean.data[f] = this.momentum * this.runningMean.data[f] +
                                (1 - this.momentum) * batchMean[f];
      this.runningVar.data[f] = this.momentum * this.runningVar.data[f] +
                               (1 - this.momentum) * batchVar[f];
    }

    return new Tensor(outputData, input.shape);
  }

  public getParameterCount(): number {
    let count = super.getParameterCount();
    if (this.gamma) count += this.gamma.size;
    if (this.beta) count += this.beta.size;
    return count;
  }
}

/**
 * Dropout层
 */
export class DropoutLayer extends NeuralLayer {
  private dropoutRate: number;
  private training: boolean = true;

  constructor(config: LayerConfig) {
    super(config);
    this.dropoutRate = config.parameters.dropoutRate || 0.5;
  }

  protected initializeParameters(): void {
    // Dropout层不需要可训练参数
  }

  public forward(input: Tensor): Tensor {
    if (!this.training || this.dropoutRate === 0) {
      return input.clone();
    }

    const outputData = new Float32Array(input.size);
    const scale = 1 / (1 - this.dropoutRate);

    for (let i = 0; i < input.size; i++) {
      if (Math.random() > this.dropoutRate) {
        outputData[i] = input.data[i] * scale;
      } else {
        outputData[i] = 0;
      }
    }

    return new Tensor(outputData, input.shape);
  }

  public setTraining(training: boolean): void {
    this.training = training;
  }
}

/**
 * 神经网络模型
 */
export class NeuralNetwork {
  private layers: NeuralLayer[] = [];
  private compiled = false;

  /**
   * 添加层
   */
  public addLayer(layer: NeuralLayer): void {
    this.layers.push(layer);
  }

  /**
   * 编译模型
   */
  public compile(): void {
    if (this.layers.length === 0) {
      throw new Error('模型至少需要一层');
    }

    // 验证层之间的形状兼容性
    for (let i = 1; i < this.layers.length; i++) {
      const prevOutput = this.layers[i - 1].getOutputShape();
      const currentInput = this.layers[i]['config'].inputShape;

      if (!this.shapesCompatible(prevOutput, currentInput)) {
        throw new Error(`层 ${i - 1} 输出形状 ${prevOutput} 与层 ${i} 输入形状 ${currentInput} 不兼容`);
      }
    }

    this.compiled = true;
  }

  /**
   * 前向传播
   */
  public predict(input: Tensor): Tensor {
    if (!this.compiled) {
      throw new Error('模型未编译');
    }

    let current = input;
    for (const layer of this.layers) {
      current = layer.forward(current);
    }

    return current;
  }

  /**
   * 获取模型参数数量
   */
  public getParameterCount(): number {
    return this.layers.reduce((sum, layer) => sum + layer.getParameterCount(), 0);
  }

  /**
   * 检查形状兼容性
   */
  private shapesCompatible(shape1: number[], shape2: number[]): boolean {
    if (shape1.length !== shape2.length) return false;

    for (let i = 0; i < shape1.length; i++) {
      if (shape1[i] !== shape2[i] && shape1[i] !== -1 && shape2[i] !== -1) {
        return false;
      }
    }

    return true;
  }
}

/**
 * 神经网络感知处理器
 */
export class NeuralPerceptionProcessor extends EventEmitter {
  private visualNetwork: NeuralNetwork;
  private auditoryNetwork: NeuralNetwork;
  private fusionNetwork: NeuralNetwork;
  private isInitialized = false;

  // 性能统计
  private processingTimes: number[] = [];
  private _accuracyHistory: number[] = [];
  private totalProcessed = 0;

  // 模型训练相关
  private trainingMode = false;
  private trainingData: { visual: Tensor[], auditory: Tensor[], labels: Tensor[] } = {
    visual: [],
    auditory: [],
    labels: []
  };
  private validationData: { visual: Tensor[], auditory: Tensor[], labels: Tensor[] } = {
    visual: [],
    auditory: [],
    labels: []
  };

  // 模型保存和加载
  private modelSavePath = './models/neural_perception/';
  private lastSaveTime = 0;
  private autoSaveInterval = 300000; // 5分钟

  // 批处理支持
  private batchSize = 32;
  private processingQueue: Array<{
    data: VisualPerceptionData | AuditoryPerceptionData,
    type: 'visual' | 'auditory',
    resolve: (result: Tensor) => void,
    reject: (error: Error) => void
  }> = [];
  private batchProcessingEnabled = true;

  constructor() {
    super();
    this.initializeNetworks();
    this.startAutoSave();
  }

  /**
   * 初始化神经网络
   */
  private initializeNetworks(): void {
    // 视觉处理网络 (CNN + Dense)
    this.visualNetwork = new NeuralNetwork();

    // 卷积层用于特征提取
    this.visualNetwork.addLayer(new Conv2DLayer({
      type: LayerType.CONV2D,
      inputShape: [64, 64, 3], // 64x64 RGB图像
      outputShape: [62, 62, 32],
      activation: ActivationType.RELU,
      parameters: { kernelSize: 3, stride: 1, padding: 0, filters: 32 }
    }));

    this.visualNetwork.addLayer(new Conv2DLayer({
      type: LayerType.CONV2D,
      inputShape: [62, 62, 32],
      outputShape: [30, 30, 64],
      activation: ActivationType.RELU,
      parameters: { kernelSize: 3, stride: 2, padding: 0, filters: 64 }
    }));

    // 展平层（通过重塑实现）
    this.visualNetwork.addLayer(new DenseLayer({
      type: LayerType.DENSE,
      inputShape: [57600], // 30*30*64 = 57600
      outputShape: [256],
      activation: ActivationType.RELU,
      parameters: {}
    }));

    this.visualNetwork.addLayer(new DenseLayer({
      type: LayerType.DENSE,
      inputShape: [256],
      outputShape: [128],
      activation: ActivationType.RELU,
      parameters: {}
    }));

    // 听觉处理网络 (LSTM + Dense)
    this.auditoryNetwork = new NeuralNetwork();

    this.auditoryNetwork.addLayer(new LSTMLayer({
      type: LayerType.LSTM,
      inputShape: [100, 64], // 100个时间步，64维特征
      outputShape: [128],
      activation: ActivationType.TANH,
      parameters: { hiddenSize: 128, sequenceLength: 100 }
    }));

    this.auditoryNetwork.addLayer(new DenseLayer({
      type: LayerType.DENSE,
      inputShape: [128],
      outputShape: [64],
      activation: ActivationType.RELU,
      parameters: {}
    }));

    // 多模态融合网络 (Attention + Dense)
    this.fusionNetwork = new NeuralNetwork();

    this.fusionNetwork.addLayer(new AttentionLayer({
      type: LayerType.ATTENTION,
      inputShape: [2, 128], // 2个模态，每个128维
      outputShape: [2, 128],
      activation: ActivationType.LINEAR,
      parameters: { headCount: 4, keyDim: 32 }
    }));

    this.fusionNetwork.addLayer(new DenseLayer({
      type: LayerType.DENSE,
      inputShape: [256], // 2*128 = 256
      outputShape: [128],
      activation: ActivationType.RELU,
      parameters: {}
    }));

    this.fusionNetwork.addLayer(new DenseLayer({
      type: LayerType.DENSE,
      inputShape: [128],
      outputShape: [64],
      activation: ActivationType.SIGMOID,
      parameters: {}
    }));

    // 编译所有网络
    this.visualNetwork.compile();
    this.auditoryNetwork.compile();
    this.fusionNetwork.compile();

    this.isInitialized = true;
  }

  /**
   * 处理视觉感知数据
   */
  public async processVisualPerception(data: VisualPerceptionData): Promise<Tensor> {
    if (!this.isInitialized) {
      throw new Error('神经网络未初始化');
    }

    const startTime = performance.now();

    try {
      // 将视觉数据转换为张量
      const inputTensor = this.visualDataToTensor(data);

      // 通过视觉网络处理
      const output = this.visualNetwork.predict(inputTensor);

      // 更新性能统计
      this.updatePerformanceStats(performance.now() - startTime);

      this.emit('visualProcessed', {
        inputShape: inputTensor.shape,
        outputShape: output.shape,
        processingTime: performance.now() - startTime
      });

      return output;

    } catch (error) {
      console.error('视觉感知处理失败:', error);
      throw error;
    }
  }

  /**
   * 处理听觉感知数据
   */
  public async processAuditoryPerception(data: AuditoryPerceptionData): Promise<Tensor> {
    if (!this.isInitialized) {
      throw new Error('神经网络未初始化');
    }

    const startTime = performance.now();

    try {
      // 将听觉数据转换为张量
      const inputTensor = this.auditoryDataToTensor(data);

      // 通过听觉网络处理
      const output = this.auditoryNetwork.predict(inputTensor);

      // 更新性能统计
      this.updatePerformanceStats(performance.now() - startTime);

      this.emit('auditoryProcessed', {
        inputShape: inputTensor.shape,
        outputShape: output.shape,
        processingTime: performance.now() - startTime
      });

      return output;

    } catch (error) {
      console.error('听觉感知处理失败:', error);
      throw error;
    }
  }

  /**
   * 多模态融合处理
   */
  public async fuseMultiModalPerception(
    visualFeatures: Tensor,
    auditoryFeatures: Tensor
  ): Promise<Tensor> {
    if (!this.isInitialized) {
      throw new Error('神经网络未初始化');
    }

    const startTime = performance.now();

    try {
      // 将多模态特征组合
      const fusionInput = this.combineModalityFeatures(visualFeatures, auditoryFeatures);

      // 通过融合网络处理
      const output = this.fusionNetwork.predict(fusionInput);

      // 更新性能统计
      this.updatePerformanceStats(performance.now() - startTime);

      this.emit('modalityFused', {
        visualShape: visualFeatures.shape,
        auditoryShape: auditoryFeatures.shape,
        outputShape: output.shape,
        processingTime: performance.now() - startTime
      });

      return output;

    } catch (error) {
      console.error('多模态融合失败:', error);
      throw error;
    }
  }

  /**
   * 将视觉数据转换为张量
   */
  private visualDataToTensor(data: VisualPerceptionData): Tensor {
    // 简化实现：创建模拟的64x64x3图像数据
    const imageSize = 64 * 64 * 3;
    const imageData = new Float32Array(imageSize);

    // 基于检测到的对象生成特征
    if (data.data.objects && data.data.objects.length > 0) {
      for (let i = 0; i < Math.min(data.data.objects.length, 10); i++) {
        const obj = data.data.objects[i];
        const baseIndex = i * (imageSize / 10);

        // 编码对象位置
        imageData[baseIndex] = obj.position.x / 1000; // 归一化
        imageData[baseIndex + 1] = obj.position.y / 1000;
        imageData[baseIndex + 2] = obj.position.z / 1000;

        // 编码对象大小
        if (obj.size) {
          imageData[baseIndex + 3] = obj.size.x / 100;
          imageData[baseIndex + 4] = obj.size.y / 100;
          imageData[baseIndex + 5] = obj.size.z / 100;
        }

        // 编码置信度
        imageData[baseIndex + 6] = obj.confidence;
      }
    }

    // 编码光照信息
    if (data.data.lighting) {
      const lightingOffset = imageSize - 10;
      imageData[lightingOffset] = data.data.lighting.intensity;
      imageData[lightingOffset + 1] = data.data.lighting.direction.x;
      imageData[lightingOffset + 2] = data.data.lighting.direction.y;
      imageData[lightingOffset + 3] = data.data.lighting.direction.z;
    }

    return new Tensor(imageData, [64, 64, 3]);
  }

  /**
   * 将听觉数据转换为张量
   */
  private auditoryDataToTensor(data: AuditoryPerceptionData): Tensor {
    const sequenceLength = 100;
    const featureSize = 64;
    const tensorData = new Float32Array(sequenceLength * featureSize);

    // 基于检测到的声音生成时序特征
    if (data.data.sounds && data.data.sounds.length > 0) {
      for (let t = 0; t < sequenceLength; t++) {
        const timeOffset = t * featureSize;

        for (let i = 0; i < Math.min(data.data.sounds.length, 5); i++) {
          const sound = data.data.sounds[i];
          const soundOffset = timeOffset + i * 12;

          // 编码声音特征
          tensorData[soundOffset] = sound.volume;
          tensorData[soundOffset + 1] = sound.frequency / 20000; // 归一化频率
          tensorData[soundOffset + 2] = sound.duration / 10000; // 归一化持续时间
          tensorData[soundOffset + 3] = sound.confidence;

          // 编码声源位置
          tensorData[soundOffset + 4] = sound.source.x / 1000;
          tensorData[soundOffset + 5] = sound.source.y / 1000;
          tensorData[soundOffset + 6] = sound.source.z / 1000;

          // 时间衰减
          const timeDecay = Math.exp(-t / 50);
          for (let j = 0; j < 7; j++) {
            tensorData[soundOffset + j] *= timeDecay;
          }
        }

        // 编码环境噪音
        tensorData[timeOffset + 60] = data.data.ambientNoise;

        // 编码语音识别结果
        if (data.data.speechRecognition && data.data.speechRecognition.length > 0) {
          const speech = data.data.speechRecognition[0];
          tensorData[timeOffset + 61] = speech.confidence;
          tensorData[timeOffset + 62] = speech.emotion === 'positive' ? 1 :
                                       speech.emotion === 'negative' ? -1 : 0;
        }
      }
    }

    return new Tensor(tensorData, [sequenceLength, featureSize]);
  }

  /**
   * 组合模态特征
   */
  private combineModalityFeatures(visual: Tensor, auditory: Tensor): Tensor {
    // 确保特征维度一致
    const targetDim = 128;

    // 调整视觉特征维度
    let visualFeatures = visual;
    if (visual.size !== targetDim) {
      // 简化：截断或填充到目标维度
      const adjustedData = new Float32Array(targetDim);
      const copySize = Math.min(visual.size, targetDim);
      adjustedData.set(visual.data.slice(0, copySize));
      visualFeatures = new Tensor(adjustedData, [targetDim]);
    }

    // 调整听觉特征维度
    let auditoryFeatures = auditory;
    if (auditory.size !== targetDim) {
      const adjustedData = new Float32Array(targetDim);
      const copySize = Math.min(auditory.size, targetDim);
      adjustedData.set(auditory.data.slice(0, copySize));
      auditoryFeatures = new Tensor(adjustedData, [targetDim]);
    }

    // 组合特征
    const combinedData = new Float32Array(2 * targetDim);
    combinedData.set(visualFeatures.data, 0);
    combinedData.set(auditoryFeatures.data, targetDim);

    return new Tensor(combinedData, [2, targetDim]);
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(processingTime: number): void {
    this.processingTimes.push(processingTime);
    this.totalProcessed++;

    // 限制历史记录长度
    if (this.processingTimes.length > 1000) {
      this.processingTimes.shift();
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): any {
    const avgProcessingTime = this.processingTimes.length > 0 ?
      this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length : 0;

    return {
      totalProcessed: this.totalProcessed,
      averageProcessingTime: avgProcessingTime,
      minProcessingTime: Math.min(...this.processingTimes),
      maxProcessingTime: Math.max(...this.processingTimes),
      visualNetworkParams: this.visualNetwork.getParameterCount(),
      auditoryNetworkParams: this.auditoryNetwork.getParameterCount(),
      fusionNetworkParams: this.fusionNetwork.getParameterCount(),
      totalParameters: this.visualNetwork.getParameterCount() +
                      this.auditoryNetwork.getParameterCount() +
                      this.fusionNetwork.getParameterCount()
    };
  }

  /**
   * 重置统计
   */
  public resetStats(): void {
    this.processingTimes = [];
    this._accuracyHistory = [];
    this.totalProcessed = 0;
  }

  /**
   * 获取网络信息
   */
  public getNetworkInfo(): any {
    return {
      visualNetwork: {
        layerCount: this.visualNetwork['layers'].length,
        parameters: this.visualNetwork.getParameterCount(),
        compiled: this.visualNetwork['compiled']
      },
      auditoryNetwork: {
        layerCount: this.auditoryNetwork['layers'].length,
        parameters: this.auditoryNetwork.getParameterCount(),
        compiled: this.auditoryNetwork['compiled']
      },
      fusionNetwork: {
        layerCount: this.fusionNetwork['layers'].length,
        parameters: this.fusionNetwork.getParameterCount(),
        compiled: this.fusionNetwork['compiled']
      },
      isInitialized: this.isInitialized,
      trainingMode: this.trainingMode,
      batchProcessingEnabled: this.batchProcessingEnabled,
      queueSize: this.processingQueue.length
    };
  }

  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    setInterval(() => {
      if (Date.now() - this.lastSaveTime > this.autoSaveInterval) {
        this.saveModels().catch(error => {
          console.error('自动保存模型失败:', error);
        });
      }
    }, this.autoSaveInterval);
  }

  /**
   * 保存模型
   */
  public async saveModels(): Promise<void> {
    try {
      // 这里应该实现实际的模型保存逻辑
      // 由于是浏览器环境，可以保存到IndexedDB或发送到服务器
      const modelData = {
        visualNetwork: this.serializeNetwork(this.visualNetwork),
        auditoryNetwork: this.serializeNetwork(this.auditoryNetwork),
        fusionNetwork: this.serializeNetwork(this.fusionNetwork),
        timestamp: Date.now(),
        version: '1.0'
      };

      // 模拟保存到本地存储
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('neural_perception_models', JSON.stringify(modelData));
      }

      this.lastSaveTime = Date.now();
      this.emit('modelsSaved', { timestamp: this.lastSaveTime });

    } catch (error) {
      console.error('保存模型失败:', error);
      throw error;
    }
  }

  /**
   * 加载模型
   */
  public async loadModels(): Promise<void> {
    try {
      if (typeof localStorage === 'undefined') {
        throw new Error('本地存储不可用');
      }

      const savedData = localStorage.getItem('neural_perception_models');
      if (!savedData) {
        throw new Error('未找到保存的模型');
      }

      const modelData = JSON.parse(savedData);

      // 这里应该实现实际的模型加载逻辑
      // 由于网络结构复杂，这里只是示例
      console.log('模型加载完成:', modelData.timestamp);

      this.emit('modelsLoaded', { timestamp: modelData.timestamp });

    } catch (error) {
      console.error('加载模型失败:', error);
      throw error;
    }
  }

  /**
   * 序列化网络（简化实现）
   */
  private serializeNetwork(network: NeuralNetwork): any {
    return {
      layerCount: network['layers'].length,
      parameters: network.getParameterCount(),
      compiled: network['compiled']
    };
  }

  /**
   * 设置训练模式
   */
  public setTrainingMode(enabled: boolean): void {
    this.trainingMode = enabled;
    this.emit('trainingModeChanged', { enabled });
  }

  /**
   * 添加训练数据
   */
  public addTrainingData(
    visualData: Tensor,
    auditoryData: Tensor,
    label: Tensor,
    isValidation: boolean = false
  ): void {
    const targetData = isValidation ? this.validationData : this.trainingData;

    targetData.visual.push(visualData);
    targetData.auditory.push(auditoryData);
    targetData.labels.push(label);

    // 限制数据集大小
    const maxSize = 10000;
    if (targetData.visual.length > maxSize) {
      targetData.visual.shift();
      targetData.auditory.shift();
      targetData.labels.shift();
    }

    this.emit('trainingDataAdded', {
      isValidation,
      totalSize: targetData.visual.length
    });
  }

  /**
   * 训练模型
   */
  public async trainModels(epochs: number = 10): Promise<void> {
    if (!this.trainingMode) {
      throw new Error('未启用训练模式');
    }

    if (this.trainingData.visual.length === 0) {
      throw new Error('没有训练数据');
    }

    try {
      this.emit('trainingStarted', { epochs });

      // 这里应该实现实际的训练逻辑
      // 由于当前实现是前向传播网络，这里只是模拟训练过程
      for (let epoch = 0; epoch < epochs; epoch++) {
        const startTime = performance.now();

        // 模拟训练过程
        let totalLoss = 0;
        const batchCount = Math.ceil(this.trainingData.visual.length / this.batchSize);

        for (let batch = 0; batch < batchCount; batch++) {
          const batchStart = batch * this.batchSize;
          const batchEnd = Math.min(batchStart + this.batchSize, this.trainingData.visual.length);

          // 模拟批次训练
          for (let i = batchStart; i < batchEnd; i++) {
            const visualInput = this.trainingData.visual[i];
            const auditoryInput = this.trainingData.auditory[i];
            const label = this.trainingData.labels[i];

            // 前向传播
            const visualOutput = this.visualNetwork.predict(visualInput);
            const auditoryOutput = this.auditoryNetwork.predict(auditoryInput);
            const fusionInput = this.combineModalityFeatures(visualOutput, auditoryOutput);
            const prediction = this.fusionNetwork.predict(fusionInput);

            // 计算损失（简化实现）
            const loss = this.calculateLoss(prediction, label);
            totalLoss += loss;
          }
        }

        const avgLoss = totalLoss / this.trainingData.visual.length;
        const epochTime = performance.now() - startTime;

        this.emit('epochCompleted', {
          epoch: epoch + 1,
          loss: avgLoss,
          time: epochTime
        });
      }

      this.emit('trainingCompleted', { epochs });

    } catch (error) {
      this.emit('trainingError', { error: error.message });
      throw error;
    }
  }

  /**
   * 计算损失（简化实现）
   */
  private calculateLoss(prediction: Tensor, label: Tensor): number {
    let loss = 0;
    const size = Math.min(prediction.size, label.size);

    for (let i = 0; i < size; i++) {
      const diff = prediction.data[i] - label.data[i];
      loss += diff * diff;
    }

    return loss / size;
  }

  /**
   * 设置批处理模式
   */
  public setBatchProcessing(enabled: boolean, batchSize?: number): void {
    this.batchProcessingEnabled = enabled;
    if (batchSize) {
      this.batchSize = batchSize;
    }

    this.emit('batchProcessingChanged', {
      enabled,
      batchSize: this.batchSize
    });
  }

  /**
   * 批量处理视觉感知数据
   */
  public async batchProcessVisualPerception(dataArray: VisualPerceptionData[]): Promise<Tensor[]> {
    if (!this.isInitialized) {
      throw new Error('神经网络未初始化');
    }

    const startTime = performance.now();
    const results: Tensor[] = [];

    try {
      // 批量转换数据
      const inputTensors = dataArray.map(data => this.visualDataToTensor(data));

      // 批量处理
      for (const inputTensor of inputTensors) {
        const output = this.visualNetwork.predict(inputTensor);
        results.push(output);
      }

      const processingTime = performance.now() - startTime;
      this.updatePerformanceStats(processingTime);

      this.emit('batchVisualProcessed', {
        batchSize: dataArray.length,
        processingTime,
        averageTimePerItem: processingTime / dataArray.length
      });

      return results;

    } catch (error) {
      console.error('批量视觉感知处理失败:', error);
      throw error;
    }
  }

  /**
   * 批量处理听觉感知数据
   */
  public async batchProcessAuditoryPerception(dataArray: AuditoryPerceptionData[]): Promise<Tensor[]> {
    if (!this.isInitialized) {
      throw new Error('神经网络未初始化');
    }

    const startTime = performance.now();
    const results: Tensor[] = [];

    try {
      // 批量转换数据
      const inputTensors = dataArray.map(data => this.auditoryDataToTensor(data));

      // 批量处理
      for (const inputTensor of inputTensors) {
        const output = this.auditoryNetwork.predict(inputTensor);
        results.push(output);
      }

      const processingTime = performance.now() - startTime;
      this.updatePerformanceStats(processingTime);

      this.emit('batchAuditoryProcessed', {
        batchSize: dataArray.length,
        processingTime,
        averageTimePerItem: processingTime / dataArray.length
      });

      return results;

    } catch (error) {
      console.error('批量听觉感知处理失败:', error);
      throw error;
    }
  }

  /**
   * 获取模型准确率
   */
  public getAccuracy(): number {
    if (this._accuracyHistory.length === 0) {
      return 0;
    }

    return this._accuracyHistory[this._accuracyHistory.length - 1];
  }

  /**
   * 更新准确率历史
   */
  public updateAccuracy(accuracy: number): void {
    this._accuracyHistory.push(accuracy);

    // 限制历史记录长度
    if (this._accuracyHistory.length > 1000) {
      this._accuracyHistory.shift();
    }

    this.emit('accuracyUpdated', { accuracy });
  }

  /**
   * 获取训练统计
   */
  public getTrainingStats(): any {
    return {
      trainingMode: this.trainingMode,
      trainingDataSize: this.trainingData.visual.length,
      validationDataSize: this.validationData.visual.length,
      accuracyHistory: this._accuracyHistory.slice(-10), // 最近10次
      currentAccuracy: this.getAccuracy(),
      lastSaveTime: this.lastSaveTime
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 清理处理队列
    this.processingQueue.forEach(item => {
      item.reject(new Error('处理器已销毁'));
    });
    this.processingQueue = [];

    // 清理统计数据
    this.processingTimes = [];
    this._accuracyHistory = [];
    this.trainingData = { visual: [], auditory: [], labels: [] };
    this.validationData = { visual: [], auditory: [], labels: [] };

    // 移除所有监听器
    this.removeAllListeners();

    this.emit('disposed');
  }

  /**
   * 模型量化（减少模型大小和计算量）
   */
  public quantizeModels(bits: number = 8): void {
    try {
      this.quantizeNetwork(this.visualNetwork, bits);
      this.quantizeNetwork(this.auditoryNetwork, bits);
      this.quantizeNetwork(this.fusionNetwork, bits);

      this.emit('modelsQuantized', { bits });
    } catch (error) {
      console.error('模型量化失败:', error);
      throw error;
    }
  }

  /**
   * 量化单个网络
   */
  private quantizeNetwork(network: NeuralNetwork, bits: number): void {
    const layers = network['layers'] as NeuralLayer[];

    for (const layer of layers) {
      if (layer['weights']) {
        this.quantizeTensor(layer['weights'], bits);
      }
      if (layer['biases']) {
        this.quantizeTensor(layer['biases'], bits);
      }
    }
  }

  /**
   * 量化张量
   */
  private quantizeTensor(tensor: Tensor, bits: number): void {
    const maxVal = Math.max(...Array.from(tensor.data));
    const minVal = Math.min(...Array.from(tensor.data));
    const range = maxVal - minVal;
    const scale = range / (Math.pow(2, bits) - 1);

    for (let i = 0; i < tensor.data.length; i++) {
      const quantized = Math.round((tensor.data[i] - minVal) / scale);
      tensor.data[i] = quantized * scale + minVal;
    }
  }

  /**
   * 模型剪枝（移除不重要的连接）
   */
  public pruneModels(threshold: number = 0.01): void {
    try {
      this.pruneNetwork(this.visualNetwork, threshold);
      this.pruneNetwork(this.auditoryNetwork, threshold);
      this.pruneNetwork(this.fusionNetwork, threshold);

      this.emit('modelsPruned', { threshold });
    } catch (error) {
      console.error('模型剪枝失败:', error);
      throw error;
    }
  }

  /**
   * 剪枝单个网络
   */
  private pruneNetwork(network: NeuralNetwork, threshold: number): void {
    const layers = network['layers'] as NeuralLayer[];

    for (const layer of layers) {
      if (layer['weights']) {
        this.pruneTensor(layer['weights'], threshold);
      }
    }
  }

  /**
   * 剪枝张量
   */
  private pruneTensor(tensor: Tensor, threshold: number): void {
    for (let i = 0; i < tensor.data.length; i++) {
      if (Math.abs(tensor.data[i]) < threshold) {
        tensor.data[i] = 0;
      }
    }
  }

  /**
   * 获取模型复杂度分析
   */
  public getModelComplexity(): any {
    const visualParams = this.visualNetwork.getParameterCount();
    const auditoryParams = this.auditoryNetwork.getParameterCount();
    const fusionParams = this.fusionNetwork.getParameterCount();
    const totalParams = visualParams + auditoryParams + fusionParams;

    // 估算内存使用（假设每个参数4字节）
    const memoryUsage = totalParams * 4;

    // 估算计算复杂度（FLOPs）
    const visualFlops = this.estimateNetworkFlops(this.visualNetwork);
    const auditoryFlops = this.estimateNetworkFlops(this.auditoryNetwork);
    const fusionFlops = this.estimateNetworkFlops(this.fusionNetwork);
    const totalFlops = visualFlops + auditoryFlops + fusionFlops;

    return {
      parameters: {
        visual: visualParams,
        auditory: auditoryParams,
        fusion: fusionParams,
        total: totalParams
      },
      memory: {
        bytes: memoryUsage,
        mb: memoryUsage / (1024 * 1024),
        gb: memoryUsage / (1024 * 1024 * 1024)
      },
      flops: {
        visual: visualFlops,
        auditory: auditoryFlops,
        fusion: fusionFlops,
        total: totalFlops
      },
      efficiency: {
        paramsPerFlop: totalParams / totalFlops,
        memoryPerFlop: memoryUsage / totalFlops
      }
    };
  }

  /**
   * 估算网络的FLOPs
   */
  private estimateNetworkFlops(network: NeuralNetwork): number {
    const layers = network['layers'] as NeuralLayer[];
    let totalFlops = 0;

    for (const layer of layers) {
      if (layer instanceof DenseLayer) {
        const inputSize = layer['config'].inputShape[0];
        const outputSize = layer['config'].outputShape[0];
        totalFlops += inputSize * outputSize * 2; // 乘法和加法
      } else if (layer instanceof Conv2DLayer) {
        const [inputH, inputW, inputC] = layer['config'].inputShape;
        const [outputH, outputW, outputC] = layer['config'].outputShape;
        const kernelSize = layer['kernelSize'] || 3;
        totalFlops += outputH * outputW * outputC * kernelSize * kernelSize * inputC * 2;
      } else if (layer instanceof LSTMLayer) {
        const hiddenSize = layer['hiddenSize'] || 64;
        const inputSize = layer['config'].inputShape[1];
        const sequenceLength = layer['sequenceLength'] || 10;
        // LSTM有4个门，每个门需要矩阵乘法
        totalFlops += sequenceLength * hiddenSize * (inputSize + hiddenSize) * 4 * 2;
      }
    }

    return totalFlops;
  }

  /**
   * 性能基准测试
   */
  public async runBenchmark(iterations: number = 100): Promise<any> {
    const results = {
      visual: { times: [], avgTime: 0, throughput: 0 },
      auditory: { times: [], avgTime: 0, throughput: 0 },
      fusion: { times: [], avgTime: 0, throughput: 0 }
    };

    // 创建测试数据
    const testVisualData: VisualPerceptionData = {
      modality: 'visual' as any,
      timestamp: Date.now(),
      confidence: 1.0,
      source: 'benchmark',
      data: {
        objects: [],
        lighting: {
          intensity: 1.0,
          direction: { x: 0, y: -1, z: 0 } as any,
          color: { r: 1, g: 1, b: 1 } as any,
          shadows: false
        },
        visibility: 1.0,
        fieldOfView: Math.PI / 3,
        viewDirection: { x: 0, y: 0, z: -1 } as any
      }
    };

    const testAuditoryData: AuditoryPerceptionData = {
      modality: 'auditory' as any,
      timestamp: Date.now(),
      confidence: 1.0,
      source: 'benchmark',
      data: {
        sounds: [],
        ambientNoise: 0.1,
        speechRecognition: []
      }
    };

    // 视觉处理基准测试
    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      await this.processVisualPerception(testVisualData);
      const endTime = performance.now();
      results.visual.times.push(endTime - startTime);
    }

    // 听觉处理基准测试
    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      await this.processAuditoryPerception(testAuditoryData);
      const endTime = performance.now();
      results.auditory.times.push(endTime - startTime);
    }

    // 融合处理基准测试
    const visualFeatures = await this.processVisualPerception(testVisualData);
    const auditoryFeatures = await this.processAuditoryPerception(testAuditoryData);

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      await this.fuseMultiModalPerception(visualFeatures, auditoryFeatures);
      const endTime = performance.now();
      results.fusion.times.push(endTime - startTime);
    }

    // 计算统计信息
    for (const [key, result] of Object.entries(results)) {
      const times = result.times;
      result.avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      result.throughput = 1000 / result.avgTime; // 每秒处理次数
    }

    this.emit('benchmarkCompleted', results);
    return results;
  }

  /**
   * 导出模型配置
   */
  public exportModelConfig(): any {
    return {
      version: '1.0',
      timestamp: Date.now(),
      networks: {
        visual: this.exportNetworkConfig(this.visualNetwork),
        auditory: this.exportNetworkConfig(this.auditoryNetwork),
        fusion: this.exportNetworkConfig(this.fusionNetwork)
      },
      hyperparameters: {
        batchSize: this.batchSize,
        autoSaveInterval: this.autoSaveInterval
      },
      performance: this.getPerformanceStats(),
      complexity: this.getModelComplexity()
    };
  }

  /**
   * 导出网络配置
   */
  private exportNetworkConfig(network: NeuralNetwork): any {
    const layers = network['layers'] as NeuralLayer[];

    return {
      layerCount: layers.length,
      layers: layers.map((layer, index) => ({
        index,
        type: layer.constructor.name,
        inputShape: layer['config'].inputShape,
        outputShape: layer['config'].outputShape,
        activation: layer['config'].activation,
        parameters: layer['config'].parameters,
        parameterCount: layer.getParameterCount()
      })),
      totalParameters: network.getParameterCount(),
      compiled: network['compiled']
    };
  }

  /**
   * 验证模型完整性
   */
  public validateModels(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // 检查网络是否已初始化
      if (!this.isInitialized) {
        errors.push('神经网络未初始化');
      }

      // 检查网络是否已编译
      if (!this.visualNetwork['compiled']) {
        errors.push('视觉网络未编译');
      }
      if (!this.auditoryNetwork['compiled']) {
        errors.push('听觉网络未编译');
      }
      if (!this.fusionNetwork['compiled']) {
        errors.push('融合网络未编译');
      }

      // 检查网络层数
      const visualLayers = this.visualNetwork['layers'] as NeuralLayer[];
      const auditoryLayers = this.auditoryNetwork['layers'] as NeuralLayer[];
      const fusionLayers = this.fusionNetwork['layers'] as NeuralLayer[];

      if (visualLayers.length === 0) {
        errors.push('视觉网络没有层');
      }
      if (auditoryLayers.length === 0) {
        errors.push('听觉网络没有层');
      }
      if (fusionLayers.length === 0) {
        errors.push('融合网络没有层');
      }

      // 检查参数数量
      if (this.visualNetwork.getParameterCount() === 0) {
        errors.push('视觉网络没有可训练参数');
      }
      if (this.auditoryNetwork.getParameterCount() === 0) {
        errors.push('听觉网络没有可训练参数');
      }
      if (this.fusionNetwork.getParameterCount() === 0) {
        errors.push('融合网络没有可训练参数');
      }

    } catch (error) {
      errors.push(`验证过程中发生错误: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}