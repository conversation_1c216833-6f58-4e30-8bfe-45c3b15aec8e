/**
 * 动画预加载器
 * 用于预加载和缓存动画资源，提高运行时性能
 */
import * as THREE from 'three';
import type { AnimationClip } from '../AnimationClip';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 预加载状态
 */
export enum PreloadState {
  /** 未开始 */
  PENDING = 'pending',
  /** 加载中 */
  LOADING = 'loading',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 失败 */
  FAILED = 'failed',
  /** 已缓存 */
  CACHED = 'cached'
}

/**
 * 预加载项
 */
export interface PreloadItem {
  /** 唯一标识 */
  id: string;
  /** 资源URL */
  url: string;
  /** 预加载状态 */
  state: PreloadState;
  /** 优先级（数字越小优先级越高） */
  priority: number;
  /** 动画片段 */
  clip?: AnimationClip;
  /** 错误信息 */
  error?: string;
  /** 加载开始时间 */
  startTime?: number;
  /** 加载完成时间 */
  endTime?: number;
  /** 文件大小（字节） */
  size?: number;
  /** 依赖项 */
  dependencies?: string[];
  /** 标签 */
  tags?: string[];
}

/**
 * 预加载配置
 */
export interface PreloaderConfig {
  /** 最大并发加载数 */
  maxConcurrent?: number;
  /** 缓存大小限制（MB） */
  maxCacheSize?: number;
  /** 是否启用压缩 */
  enableCompression?: boolean;
  /** 是否启用预测加载 */
  enablePredictiveLoading?: boolean;
  /** 预测加载阈值 */
  predictiveThreshold?: number;
  /** 缓存过期时间（毫秒） */
  cacheExpireTime?: number;
  /** 重试次数 */
  maxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 预加载统计信息
 */
export interface PreloadStats {
  /** 总项目数 */
  totalItems: number;
  /** 已完成项目数 */
  completedItems: number;
  /** 失败项目数 */
  failedItems: number;
  /** 缓存项目数 */
  cachedItems: number;
  /** 总加载时间（毫秒） */
  totalLoadTime: number;
  /** 平均加载时间（毫秒） */
  averageLoadTime: number;
  /** 缓存命中率 */
  cacheHitRate: number;
  /** 总缓存大小（字节） */
  totalCacheSize: number;
}

/**
 * 动画预加载器
 */
export class AnimationPreloader extends EventEmitter {
  /** 单例实例 */
  private static instance: AnimationPreloader | null = null;

  /** 配置 */
  private config: PreloaderConfig;
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PreloaderConfig = {
    maxConcurrent: 4,
    maxCacheSize: 100, // 100MB
    enableCompression: true,
    enablePredictiveLoading: true,
    predictiveThreshold: 0.8,
    cacheExpireTime: 300000, // 5分钟
    maxRetries: 3,
    retryDelay: 1000,
    debug: false
  };

  /** 预加载队列 */
  private queue: PreloadItem[] = [];
  /** 正在加载的项目 */
  private loading: Map<string, PreloadItem> = new Map();
  /** 缓存 */
  private cache: Map<string, PreloadItem> = new Map();
  /** 是否正在运行 */
  private isRunning: boolean = false;
  /** 加载器 */
  private loader: THREE.AnimationLoader = new THREE.AnimationLoader();
  /** 统计信息 */
  private stats: PreloadStats = {
    totalItems: 0,
    completedItems: 0,
    failedItems: 0,
    cachedItems: 0,
    totalLoadTime: 0,
    averageLoadTime: 0,
    cacheHitRate: 0,
    totalCacheSize: 0
  };

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<PreloaderConfig>) {
    super();
    this.config = { ...AnimationPreloader.DEFAULT_CONFIG, ...config };
  }

  /**
   * 获取单例实例
   * @param config 配置
   * @returns 预加载器实例
   */
  public static getInstance(config?: Partial<PreloaderConfig>): AnimationPreloader {
    if (!AnimationPreloader.instance) {
      AnimationPreloader.instance = new AnimationPreloader(config);
    }
    return AnimationPreloader.instance;
  }

  /**
   * 添加预加载项
   * @param item 预加载项
   */
  public addItem(item: Omit<PreloadItem, 'state'>): void {
    const preloadItem: PreloadItem = {
      ...item,
      state: PreloadState.PENDING
    };

    // 检查是否已在缓存中
    if (this.cache.has(item.id)) {
      preloadItem.state = PreloadState.CACHED;
      this.stats.cachedItems++;
    } else {
      this.queue.push(preloadItem);
      this.stats.totalItems++;
    }

    // 按优先级排序
    this.queue.sort((a, b) => a.priority - b.priority);

    if (this.config.debug) {
      console.log(`添加预加载项: ${item.id}, 优先级: ${item.priority}`);
    }

    // 如果正在运行，尝试开始加载
    if (this.isRunning) {
      this.processQueue();
    }
  }

  /**
   * 批量添加预加载项
   * @param items 预加载项数组
   */
  public addItems(items: Array<Omit<PreloadItem, 'state'>>): void {
    items.forEach(item => this.addItem(item));
  }

  /**
   * 移除预加载项
   * @param id 项目ID
   */
  public removeItem(id: string): void {
    // 从队列中移除
    const queueIndex = this.queue.findIndex(item => item.id === id);
    if (queueIndex !== -1) {
      this.queue.splice(queueIndex, 1);
      this.stats.totalItems--;
    }

    // 从正在加载中移除
    this.loading.delete(id);

    // 从缓存中移除
    const cachedItem = this.cache.get(id);
    if (cachedItem) {
      this.cache.delete(id);
      this.stats.cachedItems--;
      if (cachedItem.size) {
        this.stats.totalCacheSize -= cachedItem.size;
      }
    }

    if (this.config.debug) {
      console.log(`移除预加载项: ${id}`);
    }
  }

  /**
   * 开始预加载
   */
  public start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.processQueue();

    if (this.config.debug) {
      console.log('动画预加载器已启动');
    }
  }

  /**
   * 停止预加载
   */
  public stop(): void {
    this.isRunning = false;

    if (this.config.debug) {
      console.log('动画预加载器已停止');
    }
  }

  /**
   * 获取动画片段
   * @param id 项目ID
   * @returns 动画片段
   */
  public getClip(id: string): AnimationClip | null {
    const cachedItem = this.cache.get(id);
    if (cachedItem && cachedItem.clip) {
      // 更新缓存命中率
      this.updateCacheHitRate(true);
      return cachedItem.clip;
    }

    // 缓存未命中
    this.updateCacheHitRate(false);

    // 如果启用预测加载，添加到队列
    if (this.config.enablePredictiveLoading) {
      const queueItem = this.queue.find(item => item.id === id);
      if (!queueItem && !this.loading.has(id)) {
        // 这里可以实现预测加载逻辑
        // 例如：根据使用模式预测可能需要的动画
      }
    }

    return null;
  }

  /**
   * 检查项目是否已加载
   * @param id 项目ID
   * @returns 是否已加载
   */
  public isLoaded(id: string): boolean {
    return this.cache.has(id);
  }

  /**
   * 获取项目状态
   * @param id 项目ID
   * @returns 项目状态
   */
  public getItemState(id: string): PreloadState {
    const cachedItem = this.cache.get(id);
    if (cachedItem) {
      return PreloadState.CACHED;
    }

    const loadingItem = this.loading.get(id);
    if (loadingItem) {
      return loadingItem.state;
    }

    const queueItem = this.queue.find(item => item.id === id);
    if (queueItem) {
      return queueItem.state;
    }

    return PreloadState.PENDING;
  }

  /**
   * 获取统计信息
   * @returns 统计信息
   */
  public getStats(): PreloadStats {
    return { ...this.stats };
  }

  /**
   * 清除缓存
   * @param id 项目ID，如果不提供则清除所有缓存
   */
  public clearCache(id?: string): void {
    if (id) {
      const cachedItem = this.cache.get(id);
      if (cachedItem) {
        this.cache.delete(id);
        this.stats.cachedItems--;
        if (cachedItem.size) {
          this.stats.totalCacheSize -= cachedItem.size;
        }
      }
    } else {
      this.cache.clear();
      this.stats.cachedItems = 0;
      this.stats.totalCacheSize = 0;
    }

    if (this.config.debug) {
      console.log(`清除缓存: ${id || '全部'}`);
    }
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    while (this.isRunning && this.queue.length > 0 && this.loading.size < this.config.maxConcurrent!) {
      const item = this.queue.shift()!;
      
      // 检查依赖项
      if (item.dependencies && !this.checkDependencies(item.dependencies)) {
        // 依赖项未满足，重新加入队列末尾
        this.queue.push(item);
        continue;
      }

      this.loadItem(item);
    }
  }

  /**
   * 检查依赖项
   * @param dependencies 依赖项ID数组
   * @returns 是否所有依赖项都已加载
   */
  private checkDependencies(dependencies: string[]): boolean {
    return dependencies.every(dep => this.cache.has(dep));
  }

  /**
   * 加载项目
   * @param item 预加载项
   */
  private async loadItem(item: PreloadItem): Promise<void> {
    item.state = PreloadState.LOADING;
    item.startTime = Date.now();
    this.loading.set(item.id, item);

    if (this.config.debug) {
      console.log(`开始加载: ${item.id}`);
    }

    try {
      // 这里应该实现实际的加载逻辑
      // 由于AnimationClip的具体实现可能不同，这里使用模拟加载
      const clip = await this.loadClipFromUrl(item.url);
      
      item.clip = clip as any; // 类型转换
      item.state = PreloadState.COMPLETED;
      item.endTime = Date.now();
      
      // 添加到缓存
      this.cache.set(item.id, item);
      this.stats.completedItems++;
      this.stats.cachedItems++;
      
      // 更新统计信息
      if (item.startTime && item.endTime) {
        const loadTime = item.endTime - item.startTime;
        this.stats.totalLoadTime += loadTime;
        this.stats.averageLoadTime = this.stats.totalLoadTime / this.stats.completedItems;
      }

      if (item.size) {
        this.stats.totalCacheSize += item.size;
      }

      // 检查缓存大小限制
      this.manageCacheSize();

      this.emit('itemLoaded', item);

      if (this.config.debug) {
        console.log(`加载完成: ${item.id}`);
      }

    } catch (error) {
      item.state = PreloadState.FAILED;
      item.error = error instanceof Error ? error.message : String(error);
      this.stats.failedItems++;

      this.emit('itemFailed', item);

      if (this.config.debug) {
        console.error(`加载失败: ${item.id}`, error);
      }
    } finally {
      this.loading.delete(item.id);
      
      // 继续处理队列
      if (this.isRunning) {
        this.processQueue();
      }
    }
  }

  /**
   * 从URL加载动画片段
   * @param url 资源URL
   * @returns 动画片段
   */
  private async loadClipFromUrl(url: string): Promise<THREE.AnimationClip> {
    return new Promise((resolve, reject) => {
      this.loader.load(
        url,
        (clip) => resolve(clip),
        undefined,
        (error) => reject(error)
      );
    });
  }

  /**
   * 管理缓存大小
   */
  private manageCacheSize(): void {
    const maxSizeBytes = this.config.maxCacheSize! * 1024 * 1024; // 转换为字节

    if (this.stats.totalCacheSize > maxSizeBytes) {
      // 按最后使用时间排序，移除最旧的项目
      const items = Array.from(this.cache.values());
      items.sort((a, b) => (a.endTime || 0) - (b.endTime || 0));

      while (this.stats.totalCacheSize > maxSizeBytes && items.length > 0) {
        const item = items.shift()!;
        this.cache.delete(item.id);
        this.stats.cachedItems--;
        if (item.size) {
          this.stats.totalCacheSize -= item.size;
        }
      }
    }
  }

  /**
   * 更新缓存命中率
   * @param hit 是否命中
   */
  private updateCacheHitRate(hit: boolean): void {
    // 简单的移动平均算法
    const alpha = 0.1;
    this.stats.cacheHitRate = this.stats.cacheHitRate * (1 - alpha) + (hit ? 1 : 0) * alpha;
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<PreloaderConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): PreloaderConfig {
    return { ...this.config };
  }

  /**
   * 销毁预加载器
   */
  public destroy(): void {
    this.stop();
    this.queue = [];
    this.loading.clear();
    this.cache.clear();
    this.removeAllListeners();
  }
}
