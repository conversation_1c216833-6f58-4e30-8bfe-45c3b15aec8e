/**
 * AnimationRetargeter 测试文件
 * 验证修复后的功能是否正常工作
 */
import * as THREE from 'three';
import { AnimationRetargeter, RetargetConfig, RetargetEventType, BoneMapping } from './AnimationRetargeter';

describe('AnimationRetargeter', () => {
  let sourceSkeleton: THREE.Skeleton;
  let targetSkeleton: THREE.Skeleton;
  let retargeter: AnimationRetargeter;
  let config: RetargetConfig;

  beforeEach(() => {
    // 创建模拟的源骨骼
    const sourceBones = [
      new THREE.Bone(), // Hips
      new THREE.Bone(), // Spine
      new THREE.Bone(), // LeftArm
      new THREE.Bone(), // RightArm
    ];
    sourceBones[0].name = 'Hips';
    sourceBones[1].name = 'Spine';
    sourceBones[2].name = 'LeftArm';
    sourceBones[3].name = 'RightArm';

    // 设置骨骼层次结构
    sourceBones[0].add(sourceBones[1]); // Spine是Hips的子骨骼
    sourceBones[1].add(sourceBones[2]); // LeftArm是Spine的子骨骼
    sourceBones[1].add(sourceBones[3]); // RightArm是Spine的子骨骼

    sourceSkeleton = new THREE.Skeleton(sourceBones);

    // 创建模拟的目标骨骼
    const targetBones = [
      new THREE.Bone(), // Root
      new THREE.Bone(), // Torso
      new THREE.Bone(), // L_Arm
      new THREE.Bone(), // R_Arm
    ];
    targetBones[0].name = 'Root';
    targetBones[1].name = 'Torso';
    targetBones[2].name = 'L_Arm';
    targetBones[3].name = 'R_Arm';

    // 设置骨骼层次结构
    targetBones[0].add(targetBones[1]);
    targetBones[1].add(targetBones[2]);
    targetBones[1].add(targetBones[3]);

    targetSkeleton = new THREE.Skeleton(targetBones);

    // 创建骨骼映射配置
    const boneMapping: BoneMapping[] = [
      { source: 'Hips', target: 'Root' },
      { source: 'Spine', target: 'Torso' },
      { source: 'LeftArm', target: 'L_Arm' },
      { source: 'RightArm', target: 'R_Arm' }
    ];

    config = {
      boneMapping,
      preservePositionTracks: true,
      preserveScaleTracks: false,
      normalizeRotations: true,
      adjustRootHeight: true,
      adjustBoneLength: true,
      enableTPoseDetection: true,
      enableQualityAssessment: true,
      enableCache: true,
      retargetingMode: 'skeleton'
    };

    retargeter = new AnimationRetargeter(sourceSkeleton, targetSkeleton, config);
  });

  afterEach(() => {
    retargeter.clearCache();
  });

  describe('基础功能', () => {
    test('应该能够创建重定向器', () => {
      expect(retargeter).toBeDefined();
      expect(retargeter.getConfig()).toEqual(expect.objectContaining(config));
    });

    test('应该能够获取和设置配置', () => {
      const currentConfig = retargeter.getConfig();
      expect(currentConfig.boneMapping).toHaveLength(4);
      expect(currentConfig.enableTPoseDetection).toBe(true);
      expect(currentConfig.enableQualityAssessment).toBe(true);
    });
  });

  describe('T-Pose检测', () => {
    test('应该能够检测T-Pose', () => {
      const isTPose = retargeter.detectTPose(sourceSkeleton);
      expect(typeof isTPose).toBe('boolean');
    });

    test('应该在检测到T-Pose时触发事件', () => {
      let eventTriggered = false;
      const listener = () => {
        eventTriggered = true;
      };

      retargeter.addEventListener(RetargetEventType.TPOSE_DETECTED, listener);
      retargeter.detectTPose(sourceSkeleton);

      // 注意：由于T-Pose检测的复杂性，这里主要测试事件机制
      retargeter.removeEventListener(RetargetEventType.TPOSE_DETECTED, listener);
    });
  });

  describe('高级骨骼映射', () => {
    test('应该能够执行高级骨骼映射', () => {
      const sourceBones = sourceSkeleton.bones;
      const targetBones = targetSkeleton.bones;
      
      const mappings = retargeter.advancedBoneMapping(sourceBones, targetBones);
      
      expect(Array.isArray(mappings)).toBe(true);
      expect(mappings.length).toBeGreaterThan(0);
      
      // 检查映射结构
      mappings.forEach(mapping => {
        expect(mapping).toHaveProperty('source');
        expect(mapping).toHaveProperty('target');
        expect(typeof mapping.source).toBe('string');
        expect(typeof mapping.target).toBe('string');
      });
    });

    test('应该能够找到精确名称匹配', () => {
      // 创建具有相同名称的骨骼
      const sourceBones = [new THREE.Bone()];
      sourceBones[0].name = 'TestBone';
      
      const targetBones = [new THREE.Bone()];
      targetBones[0].name = 'TestBone';
      
      const mappings = retargeter.advancedBoneMapping(sourceBones, targetBones);
      
      expect(mappings).toHaveLength(1);
      expect(mappings[0].source).toBe('TestBone');
      expect(mappings[0].target).toBe('TestBone');
    });
  });

  describe('动画重定向', () => {
    test('应该能够重定向动画片段', () => {
      // 创建测试动画片段
      const times = [0, 1];
      const rotationValues = [0, 0, 0, 1, 0, 0, 0, 1]; // 两个四元数
      
      const rotationTrack = new THREE.QuaternionKeyframeTrack(
        'Hips.quaternion',
        times,
        rotationValues
      );
      
      const sourceClip = new THREE.AnimationClip('testClip', 1, [rotationTrack]);
      
      const retargetedClip = retargeter.retarget(sourceClip);
      
      expect(retargetedClip).toBeDefined();
      expect(retargetedClip.name).toBe('testClip');
      expect(retargetedClip.tracks.length).toBeGreaterThan(0);
    });

    test('应该在重定向时触发事件', () => {
      const events: string[] = [];
      
      retargeter.addEventListener(RetargetEventType.RETARGET_START, () => {
        events.push('start');
      });
      
      retargeter.addEventListener(RetargetEventType.RETARGET_COMPLETE, () => {
        events.push('complete');
      });
      
      // 创建简单的测试动画
      const sourceClip = new THREE.AnimationClip('test', 1, []);
      retargeter.retarget(sourceClip);
      
      expect(events).toContain('start');
      expect(events).toContain('complete');
    });
  });

  describe('批量重定向', () => {
    test('应该能够批量重定向多个动画片段', () => {
      const clips = [
        new THREE.AnimationClip('clip1', 1, []),
        new THREE.AnimationClip('clip2', 2, []),
        new THREE.AnimationClip('clip3', 3, [])
      ];
      
      const results = retargeter.batchRetarget(clips, {
        useCache: true,
        parallel: false
      });
      
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.name).toBe(clips[index].name);
      });
    });

    test('应该能够报告批量处理进度', () => {
      const clips = [
        new THREE.AnimationClip('clip1', 1, []),
        new THREE.AnimationClip('clip2', 2, [])
      ];
      
      const progressReports: number[] = [];
      
      retargeter.batchRetarget(clips, {
        onProgress: (progress) => {
          progressReports.push(progress);
        }
      });
      
      expect(progressReports.length).toBeGreaterThan(0);
      expect(progressReports[progressReports.length - 1]).toBe(1); // 最后应该是100%
    });
  });

  describe('质量评估', () => {
    test('应该能够评估重定向质量', () => {
      const sourceClip = new THREE.AnimationClip('source', 1, [
        new THREE.QuaternionKeyframeTrack('Hips.quaternion', [0, 1], [0, 0, 0, 1, 0, 0, 0, 1])
      ]);
      
      const retargetedClip = retargeter.retarget(sourceClip);
      const assessment = retargeter.assessRetargetingQuality(sourceClip, retargetedClip);
      
      expect(assessment).toBeDefined();
      expect(typeof assessment.overallScore).toBe('number');
      expect(assessment.overallScore).toBeGreaterThanOrEqual(0);
      expect(assessment.overallScore).toBeLessThanOrEqual(1);
      
      expect(typeof assessment.boneMappingQuality).toBe('number');
      expect(typeof assessment.proportionConsistency).toBe('number');
      expect(typeof assessment.animationFidelity).toBe('number');
      
      expect(assessment.detailedReport).toBeDefined();
      expect(Array.isArray(assessment.detailedReport.unmappedBones)).toBe(true);
      expect(Array.isArray(assessment.detailedReport.scaleMismatches)).toBe(true);
      expect(Array.isArray(assessment.detailedReport.warnings)).toBe(true);
    });

    test('应该在质量评估完成时触发事件', () => {
      let assessmentResult: any = null;
      
      retargeter.addEventListener(RetargetEventType.QUALITY_ASSESSED, (data) => {
        assessmentResult = data;
      });
      
      const sourceClip = new THREE.AnimationClip('test', 1, []);
      const retargetedClip = retargeter.retarget(sourceClip);
      retargeter.assessRetargetingQuality(sourceClip, retargetedClip);
      
      expect(assessmentResult).toBeDefined();
    });
  });

  describe('缓存管理', () => {
    test('应该能够管理缓存', () => {
      // 执行一些操作来填充缓存
      const sourceClip = new THREE.AnimationClip('test', 1, []);
      retargeter.retarget(sourceClip);
      
      const stats = retargeter.getCacheStats();
      expect(typeof stats.retargetCacheSize).toBe('number');
      expect(typeof stats.boneLengthCacheSize).toBe('number');
      expect(typeof stats.tPoseCacheSize).toBe('number');
      expect(typeof stats.hierarchyCacheSize).toBe('number');
      
      // 清理缓存
      retargeter.clearCache();
      
      const statsAfterClear = retargeter.getCacheStats();
      expect(statsAfterClear.retargetCacheSize).toBe(0);
      expect(statsAfterClear.boneLengthCacheSize).toBe(0);
      expect(statsAfterClear.tPoseCacheSize).toBe(0);
      expect(statsAfterClear.hierarchyCacheSize).toBe(0);
    });

    test('应该能够使用缓存提高性能', () => {
      const sourceClip = new THREE.AnimationClip('test', 1, []);
      
      // 第一次重定向
      const start1 = performance.now();
      const result1 = retargeter.retarget(sourceClip);
      const time1 = performance.now() - start1;
      
      // 第二次重定向（应该使用缓存）
      const start2 = performance.now();
      const result2 = retargeter.retarget(sourceClip);
      const time2 = performance.now() - start2;
      
      expect(result1.name).toBe(result2.name);
      // 注意：由于测试环境的差异，这里不严格比较时间
    });
  });

  describe('自动骨骼映射', () => {
    test('应该能够自动创建骨骼映射', () => {
      const initialMappingCount = retargeter.getConfig().boneMapping.length;
      
      retargeter.autoCreateBoneMapping();
      
      const finalMappingCount = retargeter.getConfig().boneMapping.length;
      expect(finalMappingCount).toBeGreaterThanOrEqual(initialMappingCount);
    });
  });

  describe('错误处理', () => {
    test('应该能够处理重定向错误', () => {
      let errorCaught = false;
      
      retargeter.addEventListener(RetargetEventType.RETARGET_ERROR, () => {
        errorCaught = true;
      });
      
      // 创建一个可能导致错误的动画片段
      const invalidClip = new THREE.AnimationClip('invalid', -1, []); // 负持续时间
      
      try {
        retargeter.retarget(invalidClip);
      } catch (error) {
        // 错误被正确抛出
      }
    });
  });
});
