/**
 * 动画质量控制器
 * 用于根据性能和设备能力动态调整动画质量
 */
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 质量等级
 */
export enum QualityLevel {
  /** 低质量 */
  LOW = 'low',
  /** 中等质量 */
  MEDIUM = 'medium',
  /** 高质量 */
  HIGH = 'high',
  /** 超高质量 */
  ULTRA = 'ultra',
  /** 自动 */
  AUTO = 'auto'
}

/**
 * 设备性能等级
 */
export enum DevicePerformance {
  /** 低性能 */
  LOW = 'low',
  /** 中等性能 */
  MEDIUM = 'medium',
  /** 高性能 */
  HIGH = 'high'
}

/**
 * 质量设置
 */
export interface QualitySettings {
  /** 动画帧率 */
  animationFrameRate: number;
  /** 骨骼LOD等级 */
  skeletonLOD: number;
  /** 混合层数限制 */
  maxBlendLayers: number;
  /** 关键帧精度 */
  keyframePrecision: number;
  /** 是否启用动画压缩 */
  enableCompression: boolean;
  /** 是否启用动画缓存 */
  enableCaching: boolean;
  /** 最大同时播放动画数 */
  maxConcurrentAnimations: number;
  /** 混合质量 */
  blendQuality: number;
  /** 是否启用高级混合 */
  enableAdvancedBlending: boolean;
  /** 是否启用物理动画 */
  enablePhysicsAnimation: boolean;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 帧率 */
  fps: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** 内存使用率 */
  memoryUsage: number;
  /** GPU使用率 */
  gpuUsage: number;
  /** 动画更新时间 */
  animationUpdateTime: number;
  /** 渲染时间 */
  renderTime: number;
}

/**
 * 质量控制配置
 */
export interface QualityControlConfig {
  /** 目标帧率 */
  targetFPS?: number;
  /** 最低可接受帧率 */
  minAcceptableFPS?: number;
  /** 性能检查间隔（毫秒） */
  performanceCheckInterval?: number;
  /** 质量调整阈值 */
  qualityAdjustmentThreshold?: number;
  /** 是否启用自动质量调整 */
  enableAutoAdjustment?: boolean;
  /** 是否启用设备检测 */
  enableDeviceDetection?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 动画质量控制器
 */
export class AnimationQualityController extends EventEmitter {
  /** 单例实例 */
  private static instance: AnimationQualityController | null = null;

  /** 配置 */
  private config: QualityControlConfig;
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: QualityControlConfig = {
    targetFPS: 60,
    minAcceptableFPS: 30,
    performanceCheckInterval: 2000,
    qualityAdjustmentThreshold: 0.8,
    enableAutoAdjustment: true,
    enableDeviceDetection: true,
    debug: false
  };

  /** 当前质量等级 */
  private currentQuality: QualityLevel = QualityLevel.AUTO;
  /** 设备性能等级 */
  private devicePerformance: DevicePerformance = DevicePerformance.MEDIUM;
  /** 质量设置映射 */
  private qualitySettings: Map<QualityLevel, QualitySettings> = new Map();
  /** 性能监控定时器 */
  private performanceTimer: NodeJS.Timeout | null = null;
  /** 性能历史记录 */
  private performanceHistory: PerformanceMetrics[] = [];
  /** 最大历史记录数 */
  private readonly MAX_HISTORY = 10;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<QualityControlConfig>) {
    super();
    this.config = { ...AnimationQualityController.DEFAULT_CONFIG, ...config };
    this.initializeQualitySettings();
    this.detectDevicePerformance();
  }

  /**
   * 获取单例实例
   * @param config 配置
   * @returns 质量控制器实例
   */
  public static getInstance(config?: Partial<QualityControlConfig>): AnimationQualityController {
    if (!AnimationQualityController.instance) {
      AnimationQualityController.instance = new AnimationQualityController(config);
    }
    return AnimationQualityController.instance;
  }

  /**
   * 初始化质量设置
   */
  private initializeQualitySettings(): void {
    // 低质量设置
    this.qualitySettings.set(QualityLevel.LOW, {
      animationFrameRate: 30,
      skeletonLOD: 2,
      maxBlendLayers: 2,
      keyframePrecision: 2,
      enableCompression: true,
      enableCaching: true,
      maxConcurrentAnimations: 5,
      blendQuality: 0.5,
      enableAdvancedBlending: false,
      enablePhysicsAnimation: false
    });

    // 中等质量设置
    this.qualitySettings.set(QualityLevel.MEDIUM, {
      animationFrameRate: 45,
      skeletonLOD: 1,
      maxBlendLayers: 4,
      keyframePrecision: 3,
      enableCompression: true,
      enableCaching: true,
      maxConcurrentAnimations: 8,
      blendQuality: 0.7,
      enableAdvancedBlending: true,
      enablePhysicsAnimation: false
    });

    // 高质量设置
    this.qualitySettings.set(QualityLevel.HIGH, {
      animationFrameRate: 60,
      skeletonLOD: 0,
      maxBlendLayers: 6,
      keyframePrecision: 4,
      enableCompression: false,
      enableCaching: true,
      maxConcurrentAnimations: 12,
      blendQuality: 0.9,
      enableAdvancedBlending: true,
      enablePhysicsAnimation: true
    });

    // 超高质量设置
    this.qualitySettings.set(QualityLevel.ULTRA, {
      animationFrameRate: 120,
      skeletonLOD: 0,
      maxBlendLayers: 8,
      keyframePrecision: 5,
      enableCompression: false,
      enableCaching: true,
      maxConcurrentAnimations: 16,
      blendQuality: 1.0,
      enableAdvancedBlending: true,
      enablePhysicsAnimation: true
    });
  }

  /**
   * 检测设备性能
   */
  private detectDevicePerformance(): void {
    if (!this.config.enableDeviceDetection) {
      return;
    }

    let score = 0;

    // 检测CPU核心数
    const cores = navigator.hardwareConcurrency || 4;
    if (cores >= 8) score += 3;
    else if (cores >= 4) score += 2;
    else score += 1;

    // 检测内存
    if ((navigator as any).deviceMemory) {
      const memory = (navigator as any).deviceMemory;
      if (memory >= 8) score += 3;
      else if (memory >= 4) score += 2;
      else score += 1;
    } else {
      score += 2; // 默认中等
    }

    // 检测GPU（通过WebGL上下文）
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext | null;
      if (gl) {
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
          const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) as string;
          if (renderer.includes('NVIDIA') || renderer.includes('AMD') || renderer.includes('Intel')) {
            score += 2;
          } else {
            score += 1;
          }
        } else {
          score += 2; // 默认中等
        }
      }
    } catch (error) {
      score += 1; // 低性能
    }

    // 根据分数确定设备性能等级
    if (score >= 7) {
      this.devicePerformance = DevicePerformance.HIGH;
    } else if (score >= 5) {
      this.devicePerformance = DevicePerformance.MEDIUM;
    } else {
      this.devicePerformance = DevicePerformance.LOW;
    }

    // 根据设备性能设置初始质量等级
    if (this.currentQuality === QualityLevel.AUTO) {
      switch (this.devicePerformance) {
        case DevicePerformance.HIGH:
          this.setQuality(QualityLevel.HIGH);
          break;
        case DevicePerformance.MEDIUM:
          this.setQuality(QualityLevel.MEDIUM);
          break;
        case DevicePerformance.LOW:
          this.setQuality(QualityLevel.LOW);
          break;
      }
    }

    if (this.config.debug) {
      console.log(`设备性能检测: ${this.devicePerformance}, 分数: ${score}`);
    }
  }

  /**
   * 设置质量等级
   * @param quality 质量等级
   */
  public setQuality(quality: QualityLevel): void {
    if (quality === this.currentQuality) return;

    const oldQuality = this.currentQuality;
    this.currentQuality = quality;

    const settings = this.getQualitySettings();
    this.emit('qualityChanged', {
      oldQuality,
      newQuality: quality,
      settings
    });

    if (this.config.debug) {
      console.log(`质量等级已更改: ${oldQuality} -> ${quality}`, settings);
    }
  }

  /**
   * 获取当前质量等级
   * @returns 质量等级
   */
  public getQuality(): QualityLevel {
    return this.currentQuality;
  }

  /**
   * 获取质量设置
   * @param quality 质量等级，如果不提供则使用当前质量
   * @returns 质量设置
   */
  public getQualitySettings(quality?: QualityLevel): QualitySettings {
    const targetQuality = quality || this.currentQuality;
    
    if (targetQuality === QualityLevel.AUTO) {
      // 自动模式下根据设备性能选择
      switch (this.devicePerformance) {
        case DevicePerformance.HIGH:
          return this.qualitySettings.get(QualityLevel.HIGH)!;
        case DevicePerformance.MEDIUM:
          return this.qualitySettings.get(QualityLevel.MEDIUM)!;
        case DevicePerformance.LOW:
          return this.qualitySettings.get(QualityLevel.LOW)!;
      }
    }

    return this.qualitySettings.get(targetQuality) || this.qualitySettings.get(QualityLevel.MEDIUM)!;
  }

  /**
   * 自定义质量设置
   * @param quality 质量等级
   * @param settings 质量设置
   */
  public setCustomQualitySettings(quality: QualityLevel, settings: QualitySettings): void {
    this.qualitySettings.set(quality, settings);
    
    if (quality === this.currentQuality) {
      this.emit('qualityChanged', {
        oldQuality: quality,
        newQuality: quality,
        settings
      });
    }

    if (this.config.debug) {
      console.log(`自定义质量设置已更新: ${quality}`, settings);
    }
  }

  /**
   * 启动性能监控
   */
  public startPerformanceMonitoring(): void {
    if (this.performanceTimer) return;

    this.performanceTimer = setInterval(() => {
      this.checkPerformance();
    }, this.config.performanceCheckInterval!);

    if (this.config.debug) {
      console.log('性能监控已启动');
    }
  }

  /**
   * 停止性能监控
   */
  public stopPerformanceMonitoring(): void {
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;

      if (this.config.debug) {
        console.log('性能监控已停止');
      }
    }
  }

  /**
   * 更新性能指标
   * @param metrics 性能指标
   */
  public updatePerformanceMetrics(metrics: Partial<PerformanceMetrics>): void {
    // 创建完整的性能指标对象
    const fullMetrics: PerformanceMetrics = {
      fps: 60,
      cpuUsage: 0,
      memoryUsage: 0,
      gpuUsage: 0,
      animationUpdateTime: 0,
      renderTime: 0,
      ...metrics
    };

    // 添加到历史记录
    this.performanceHistory.push(fullMetrics);
    if (this.performanceHistory.length > this.MAX_HISTORY) {
      this.performanceHistory.shift();
    }

    // 如果启用自动调整，检查是否需要调整质量
    if (this.config.enableAutoAdjustment) {
      this.autoAdjustQuality(fullMetrics);
    }

    this.emit('performanceUpdate', fullMetrics);
  }

  /**
   * 检查性能
   */
  private checkPerformance(): void {
    // 这里可以实现实际的性能检测逻辑
    // 由于浏览器API限制，这里使用模拟数据
    const metrics: PerformanceMetrics = {
      fps: this.estimateFPS(),
      cpuUsage: this.estimateCPUUsage(),
      memoryUsage: this.getMemoryUsage(),
      gpuUsage: 0, // GPU使用率难以直接获取
      animationUpdateTime: 0,
      renderTime: 0
    };

    this.updatePerformanceMetrics(metrics);
  }

  /**
   * 估算帧率
   * @returns 估算的帧率
   */
  private estimateFPS(): number {
    // 这里应该实现实际的FPS计算
    // 简化实现，返回一个基于性能的估算值
    return 60; // 占位符
  }

  /**
   * 估算CPU使用率
   * @returns 估算的CPU使用率
   */
  private estimateCPUUsage(): number {
    // 这里应该实现实际的CPU使用率计算
    // 浏览器中难以直接获取，返回估算值
    return Math.random() * 100; // 占位符
  }

  /**
   * 获取内存使用情况
   * @returns 内存使用率
   */
  private getMemoryUsage(): number {
    if ((performance as any).memory) {
      const memory = (performance as any).memory;
      return (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
    }
    return 0;
  }

  /**
   * 自动调整质量
   * @param metrics 性能指标
   */
  private autoAdjustQuality(metrics: PerformanceMetrics): void {
    if (this.currentQuality === QualityLevel.AUTO) return;

    const targetFPS = this.config.targetFPS!;
    const minFPS = this.config.minAcceptableFPS!;
    const threshold = this.config.qualityAdjustmentThreshold!;

    // 计算平均性能
    const avgFPS = this.getAveragePerformance('fps');
    const avgCPU = this.getAveragePerformance('cpuUsage');
    const avgMemory = this.getAveragePerformance('memoryUsage');

    // 判断是否需要降低质量
    if (avgFPS < minFPS || avgCPU > 80 || avgMemory > 80) {
      this.downgradeQuality();
    }
    // 判断是否可以提高质量
    else if (avgFPS > targetFPS * threshold && avgCPU < 60 && avgMemory < 60) {
      this.upgradeQuality();
    }
  }

  /**
   * 获取平均性能指标
   * @param metric 指标名称
   * @returns 平均值
   */
  private getAveragePerformance(metric: keyof PerformanceMetrics): number {
    if (this.performanceHistory.length === 0) return 0;

    const sum = this.performanceHistory.reduce((acc, curr) => acc + curr[metric], 0);
    return sum / this.performanceHistory.length;
  }

  /**
   * 降低质量等级
   */
  private downgradeQuality(): void {
    switch (this.currentQuality) {
      case QualityLevel.ULTRA:
        this.setQuality(QualityLevel.HIGH);
        break;
      case QualityLevel.HIGH:
        this.setQuality(QualityLevel.MEDIUM);
        break;
      case QualityLevel.MEDIUM:
        this.setQuality(QualityLevel.LOW);
        break;
      // LOW质量已是最低，不再降级
    }
  }

  /**
   * 提高质量等级
   */
  private upgradeQuality(): void {
    switch (this.currentQuality) {
      case QualityLevel.LOW:
        this.setQuality(QualityLevel.MEDIUM);
        break;
      case QualityLevel.MEDIUM:
        this.setQuality(QualityLevel.HIGH);
        break;
      case QualityLevel.HIGH:
        this.setQuality(QualityLevel.ULTRA);
        break;
      // ULTRA质量已是最高，不再升级
    }
  }

  /**
   * 获取设备性能等级
   * @returns 设备性能等级
   */
  public getDevicePerformance(): DevicePerformance {
    return this.devicePerformance;
  }

  /**
   * 获取性能历史记录
   * @returns 性能历史记录
   */
  public getPerformanceHistory(): PerformanceMetrics[] {
    return [...this.performanceHistory];
  }

  /**
   * 清除性能历史记录
   */
  public clearPerformanceHistory(): void {
    this.performanceHistory = [];
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<QualityControlConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): QualityControlConfig {
    return { ...this.config };
  }

  /**
   * 销毁质量控制器
   */
  public destroy(): void {
    this.stopPerformanceMonitoring();
    this.clearPerformanceHistory();
    this.removeAllListeners();
  }
}
