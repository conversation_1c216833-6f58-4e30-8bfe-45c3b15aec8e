# AI动画合成系统使用指南

## 概述

AI动画合成系统是DL引擎中用于基于AI技术生成和合成动画的核心模块。该系统支持身体动画、面部动画和组合动画的智能生成，具备批处理、缓存管理、错误重试等企业级功能。

## 功能特性

### 核心功能
- **多类型动画生成**：支持身体动画、面部动画和组合动画
- **AI模型集成**：支持本地和远程AI模型
- **批处理支持**：提高处理效率，支持并行生成
- **智能缓存**：结果缓存，支持过期时间和大小限制
- **错误重试**：自动重试机制，提高成功率
- **进度报告**：实时报告生成进度
- **事件系统**：完整的事件通知机制

### 性能优化
- **批处理队列**：自动批量处理请求
- **缓存管理**：智能缓存清理和大小控制
- **资源清理**：完善的资源释放机制
- **异步处理**：非阻塞式动画生成

## 快速开始

### 1. 创建AI动画合成系统

```typescript
import { AIAnimationSynthesisSystem } from '../animation/AIAnimationSynthesis';
import { World } from '../core/World';

// 创建世界
const world = new World();

// 创建AI动画合成系统
const aiSystem = new AIAnimationSynthesisSystem(world, {
  debug: true,
  useLocalModel: true,
  batchSize: 4,
  maxCacheSize: 100,
  enableProgressReporting: true
});

// 添加系统到世界
world.addSystem(aiSystem);
```

### 2. 为实体创建AI动画合成组件

```typescript
import { Entity } from '../core/Entity';

// 创建实体
const character = new Entity();
world.addEntity(character);

// 创建AI动画合成组件
const aiComponent = aiSystem.createAIAnimationSynthesis(character);
```

### 3. 生成动画

```typescript
// 生成身体动画
const bodyRequestId = aiSystem.generateBodyAnimation(
  character,
  '开心地跳跃',
  3.0,
  {
    loop: true,
    style: '卡通',
    intensity: 0.8
  }
);

// 生成面部动画
const facialRequestId = aiSystem.generateFacialAnimation(
  character,
  '微笑表情',
  2.0,
  {
    loop: false,
    intensity: 0.6
  }
);

// 生成组合动画
const combinedRequestId = aiSystem.generateCombinedAnimation(
  character,
  '开心地说话',
  5.0,
  {
    loop: true,
    style: '自然'
  }
);
```

## 高级配置

### 系统配置选项

```typescript
interface AIAnimationSynthesisConfig {
  debug?: boolean;                    // 是否启用调试
  modelUrl?: string;                  // 模型URL
  useLocalModel?: boolean;            // 是否使用本地模型
  batchSize?: number;                 // 批处理大小
  sampleRate?: number;                // 采样率
  maxContextLength?: number;          // 最大上下文长度
  maxCacheSize?: number;              // 缓存大小限制
  cacheExpireTime?: number;           // 缓存过期时间（毫秒）
  maxRetries?: number;                // 最大重试次数
  retryDelay?: number;                // 重试延迟（毫秒）
  enableProgressReporting?: boolean;  // 是否启用进度报告
}
```

### 批处理配置

```typescript
// 启用批处理，提高处理效率
const aiSystem = new AIAnimationSynthesisSystem(world, {
  batchSize: 8,           // 批处理大小
  useLocalModel: true,    // 使用本地模型
  debug: true
});
```

### 缓存配置

```typescript
// 配置缓存策略
const aiSystem = new AIAnimationSynthesisSystem(world, {
  maxCacheSize: 200,        // 最大缓存200个结果
  cacheExpireTime: 600000,  // 缓存10分钟后过期
  debug: true
});
```

## 事件处理

### 监听生成事件

```typescript
// 监听生成完成事件
aiSystem.addEventListener('generationComplete', (data) => {
  const { result } = data;
  console.log(`动画生成完成: ${result.id}`);
  
  if (result.success && result.clip) {
    // 使用生成的动画片段
    console.log(`生成时间: ${result.generationTime}ms`);
  }
});

// 监听生成错误事件
aiSystem.addEventListener('generationError', (data) => {
  const { result } = data;
  console.error(`动画生成失败: ${result.id}`, result.error);
});

// 监听生成进度事件
aiSystem.addEventListener('generationProgress', (data) => {
  const { id, progress, stage } = data;
  console.log(`动画 ${id} 生成进度: ${(progress * 100).toFixed(1)}% (${stage})`);
});
```

### 监听模型事件

```typescript
// 监听模型加载事件
aiSystem.addEventListener('modelLoaded', (data) => {
  if (data.success) {
    console.log('AI模型加载成功');
  } else {
    console.error('AI模型加载失败:', data.error);
  }
});

// 监听模型加载进度
aiSystem.addEventListener('modelLoadProgress', (data) => {
  const { progress } = data;
  console.log(`模型加载进度: ${(progress * 100).toFixed(1)}%`);
});
```

## 结果管理

### 获取生成结果

```typescript
// 获取生成结果
const result = aiSystem.getResult(character, requestId);

if (result && result.success) {
  const { clip, generationTime } = result;
  console.log(`动画生成成功，耗时: ${generationTime}ms`);
  
  // 使用动画片段
  if (clip) {
    // 应用到角色
    applyAnimationToCharacter(character, clip);
  }
} else if (result) {
  console.error('动画生成失败:', result.error);
}
```

### 取消请求

```typescript
// 取消生成请求
const canceled = aiSystem.cancelRequest(character, requestId);

if (canceled) {
  console.log('请求已取消');
} else {
  console.log('请求无法取消（可能已在处理中）');
}
```

### 缓存管理

```typescript
// 清除特定结果的缓存
aiComponent.clearCache(requestId);

// 清除所有缓存
aiComponent.clearCache();
```

## 最佳实践

### 1. 合理配置批处理

```typescript
// 根据硬件性能调整批处理大小
const aiSystem = new AIAnimationSynthesisSystem(world, {
  batchSize: navigator.hardwareConcurrency || 4,  // 基于CPU核心数
  useLocalModel: true
});
```

### 2. 优化缓存策略

```typescript
// 根据应用需求配置缓存
const aiSystem = new AIAnimationSynthesisSystem(world, {
  maxCacheSize: 100,        // 适中的缓存大小
  cacheExpireTime: 300000,  // 5分钟过期时间
});
```

### 3. 错误处理

```typescript
// 配置重试策略
const aiSystem = new AIAnimationSynthesisSystem(world, {
  maxRetries: 3,      // 最多重试3次
  retryDelay: 1000,   // 重试间隔1秒
});

// 监听错误并处理
aiSystem.addEventListener('generationError', (data) => {
  const { result } = data;
  
  // 记录错误日志
  console.error(`动画生成失败: ${result.id}`, result.error);
  
  // 可以实现自定义的错误恢复逻辑
  handleGenerationError(result);
});
```

### 4. 资源清理

```typescript
// 在组件销毁时清理资源
character.onDestroy(() => {
  aiSystem.removeAIAnimationSynthesis(character);
});

// 在应用退出时清理系统
window.addEventListener('beforeunload', () => {
  world.removeSystem(aiSystem);
});
```

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型路径是否正确
   - 确认网络连接（远程模型）
   - 查看控制台错误信息

2. **生成速度慢**
   - 增加批处理大小
   - 使用本地模型
   - 检查硬件性能

3. **内存占用过高**
   - 减少缓存大小
   - 缩短缓存过期时间
   - 及时清理不需要的结果

4. **生成质量不佳**
   - 优化提示文本
   - 调整生成参数
   - 使用更高质量的AI模型

通过以上配置和最佳实践，您可以充分利用AI动画合成系统的强大功能，为您的应用创建高质量的动画内容。
