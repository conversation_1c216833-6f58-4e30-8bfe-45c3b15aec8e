/**
 * 虚拟化身控制系统
 * 
 * 提供在虚拟场景中控制虚拟化身的移动、交互、动作等功能
 * 集成输入系统，支持键盘、鼠标、手柄等多种输入方式
 */

import { System } from '../core/System';
import { World } from '../core/World';
import { Entity } from '../core/Entity';
import { Transform } from '../scene/Transform';
import { EventEmitter } from 'events';
import { Vector3 } from '../math/Vector3';
import { InputSystem } from '../input/InputSystem';
import * as THREE from 'three';

/**
 * 移动模式枚举
 */
export enum MovementMode {
  /** 行走模式 */
  WALK = 'walk',
  /** 跑步模式 */
  RUN = 'run',
  /** 飞行模式 */
  FLY = 'fly',
  /** 传送模式 */
  TELEPORT = 'teleport'
}

/**
 * 交互类型枚举
 */
export enum InteractionType {
  /** 点击交互 */
  CLICK = 'click',
  /** 触摸交互 */
  TOUCH = 'touch',
  /** 语音交互 */
  VOICE = 'voice',
  /** 手势交互 */
  GESTURE = 'gesture'
}

/**
 * 控制配置接口
 */
export interface ControlConfig {
  /** 移动速度 */
  moveSpeed: number;
  /** 旋转速度 */
  rotationSpeed: number;
  /** 跳跃力度 */
  jumpForce: number;
  /** 移动模式 */
  movementMode: MovementMode;
  /** 是否启用重力 */
  enableGravity: boolean;
  /** 是否启用碰撞检测 */
  enableCollision: boolean;
  /** 是否启用平滑移动 */
  enableSmoothMovement: boolean;
  /** 平滑系数 */
  smoothFactor: number;
}

/**
 * 输入映射配置
 */
export interface InputMapping {
  /** 前进键 */
  moveForward: string[];
  /** 后退键 */
  moveBackward: string[];
  /** 左移键 */
  moveLeft: string[];
  /** 右移键 */
  moveRight: string[];
  /** 跳跃键 */
  jump: string[];
  /** 跑步键 */
  run: string[];
  /** 交互键 */
  interact: string[];
  /** 菜单键 */
  menu: string[];
}

/**
 * 虚拟化身控制系统配置
 */
export interface AvatarControlSystemConfig {
  /** 默认控制配置 */
  defaultControlConfig: ControlConfig;
  /** 默认输入映射 */
  defaultInputMapping: InputMapping;
  /** 最大控制距离 */
  maxControlDistance: number;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 虚拟化身控制系统
 */
export class AvatarControlSystem extends System {
  /** 系统名称 */
  public static readonly NAME = 'AvatarControlSystem';

  /** 配置 */
  private config: AvatarControlSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 受控的虚拟化身 */
  private controlledAvatars: Map<string, {
    entity: Entity;
    config: ControlConfig;
    inputMapping: InputMapping;
    currentVelocity: Vector3;
    targetVelocity: Vector3;
    isGrounded: boolean;
    lastPosition: Vector3;
  }> = new Map();

  /** 输入系统引用 */
  private inputSystem: InputSystem | null = null;

  /** 当前激活的虚拟化身ID */
  private activeAvatarId: string | null = null;

  /** 输入状态 */
  private inputState: {
    moveForward: boolean;
    moveBackward: boolean;
    moveLeft: boolean;
    moveRight: boolean;
    jump: boolean;
    run: boolean;
    interact: boolean;
    mouseX: number;
    mouseY: number;
  } = {
    moveForward: false,
    moveBackward: false,
    moveLeft: false,
    moveRight: false,
    jump: false,
    run: false,
    interact: false,
    mouseX: 0,
    mouseY: 0
  };

  /**
   * 构造函数
   */
  constructor(world: World, config: Partial<AvatarControlSystemConfig> = {}) {
    super(0);
    this.setWorld(world);

    this.config = {
      defaultControlConfig: {
        moveSpeed: 5.0,
        rotationSpeed: 2.0,
        jumpForce: 10.0,
        movementMode: MovementMode.WALK,
        enableGravity: true,
        enableCollision: true,
        enableSmoothMovement: true,
        smoothFactor: 0.1
      },
      defaultInputMapping: {
        moveForward: ['KeyW', 'ArrowUp'],
        moveBackward: ['KeyS', 'ArrowDown'],
        moveLeft: ['KeyA', 'ArrowLeft'],
        moveRight: ['KeyD', 'ArrowRight'],
        jump: ['Space'],
        run: ['ShiftLeft'],
        interact: ['KeyE'],
        menu: ['Escape']
      },
      maxControlDistance: 100.0,
      debug: false,
      ...config
    };

    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    // 获取输入系统
    this.inputSystem = this.world?.getSystem(InputSystem) as InputSystem;
    
    if (this.inputSystem) {
      this.setupInputHandlers();
    } else {
      console.warn('输入系统未找到，虚拟化身控制功能可能受限');
    }

    if (this.config.debug) {
      console.log('虚拟化身控制系统已初始化');
    }
  }

  /**
   * 设置输入处理器
   */
  private setupInputHandlers(): void {
    if (!this.inputSystem) return;

    // 键盘事件处理
    this.inputSystem.on('keydown', (event: KeyboardEvent) => {
      this.handleKeyDown(event.code);
    });

    this.inputSystem.on('keyup', (event: KeyboardEvent) => {
      this.handleKeyUp(event.code);
    });

    // 鼠标事件处理
    this.inputSystem.on('mousemove', (event: MouseEvent) => {
      this.handleMouseMove(event.movementX, event.movementY);
    });

    this.inputSystem.on('mousedown', (event: MouseEvent) => {
      this.handleMouseDown(event.button);
    });

    this.inputSystem.on('mouseup', (event: MouseEvent) => {
      this.handleMouseUp(event.button);
    });
  }

  /**
   * 处理按键按下
   */
  private handleKeyDown(keyCode: string): void {
    if (!this.activeAvatarId) return;

    const avatar = this.controlledAvatars.get(this.activeAvatarId);
    if (!avatar) return;

    const mapping = avatar.inputMapping;

    if (mapping.moveForward.includes(keyCode)) {
      this.inputState.moveForward = true;
    }
    if (mapping.moveBackward.includes(keyCode)) {
      this.inputState.moveBackward = true;
    }
    if (mapping.moveLeft.includes(keyCode)) {
      this.inputState.moveLeft = true;
    }
    if (mapping.moveRight.includes(keyCode)) {
      this.inputState.moveRight = true;
    }
    if (mapping.jump.includes(keyCode)) {
      this.inputState.jump = true;
    }
    if (mapping.run.includes(keyCode)) {
      this.inputState.run = true;
    }
    if (mapping.interact.includes(keyCode)) {
      this.inputState.interact = true;
      this.handleInteraction();
    }
    if (mapping.menu.includes(keyCode)) {
      this.handleMenuToggle();
    }
  }

  /**
   * 处理按键释放
   */
  private handleKeyUp(keyCode: string): void {
    if (!this.activeAvatarId) return;

    const avatar = this.controlledAvatars.get(this.activeAvatarId);
    if (!avatar) return;

    const mapping = avatar.inputMapping;

    if (mapping.moveForward.includes(keyCode)) {
      this.inputState.moveForward = false;
    }
    if (mapping.moveBackward.includes(keyCode)) {
      this.inputState.moveBackward = false;
    }
    if (mapping.moveLeft.includes(keyCode)) {
      this.inputState.moveLeft = false;
    }
    if (mapping.moveRight.includes(keyCode)) {
      this.inputState.moveRight = false;
    }
    if (mapping.jump.includes(keyCode)) {
      this.inputState.jump = false;
    }
    if (mapping.run.includes(keyCode)) {
      this.inputState.run = false;
    }
    if (mapping.interact.includes(keyCode)) {
      this.inputState.interact = false;
    }
  }

  /**
   * 处理鼠标移动
   */
  private handleMouseMove(deltaX: number, deltaY: number): void {
    if (!this.activeAvatarId) return;

    this.inputState.mouseX += deltaX;
    this.inputState.mouseY += deltaY;

    // 应用鼠标旋转
    this.applyMouseRotation(deltaX, deltaY);
  }

  /**
   * 处理鼠标按下
   */
  private handleMouseDown(button: number): void {
    if (button === 0) { // 左键
      this.handleInteraction();
    }
  }

  /**
   * 处理鼠标释放
   */
  private handleMouseUp(button: number): void {
    // 可以在这里处理鼠标释放事件
  }

  /**
   * 应用鼠标旋转
   */
  private applyMouseRotation(deltaX: number, deltaY: number): void {
    if (!this.activeAvatarId) return;

    const avatar = this.controlledAvatars.get(this.activeAvatarId);
    if (!avatar) return;

    const transform = avatar.entity.getTransform();
    const rotationSpeed = avatar.config.rotationSpeed;

    // 水平旋转（Y轴）
    const currentRotation = transform.getRotation();
    const newRotationY = currentRotation.y + (deltaX * rotationSpeed * 0.01);
    
    transform.setRotation(currentRotation.x, newRotationY, currentRotation.z);

    this.eventEmitter.emit('avatarRotated', {
      avatarId: this.activeAvatarId,
      rotation: { x: currentRotation.x, y: newRotationY, z: currentRotation.z }
    });
  }

  /**
   * 处理交互
   */
  private handleInteraction(): void {
    if (!this.activeAvatarId) return;

    this.eventEmitter.emit('avatarInteract', {
      avatarId: this.activeAvatarId,
      interactionType: InteractionType.CLICK
    });

    if (this.config.debug) {
      console.log(`虚拟化身 ${this.activeAvatarId} 执行交互`);
    }
  }

  /**
   * 处理菜单切换
   */
  private handleMenuToggle(): void {
    this.eventEmitter.emit('menuToggle', {
      avatarId: this.activeAvatarId
    });

    if (this.config.debug) {
      console.log('菜单切换');
    }
  }

  /**
   * 添加受控虚拟化身
   */
  public addControlledAvatar(
    avatarId: string,
    entity: Entity,
    config?: Partial<ControlConfig>,
    inputMapping?: Partial<InputMapping>
  ): void {
    const finalConfig = { ...this.config.defaultControlConfig, ...config };
    const finalInputMapping = { ...this.config.defaultInputMapping, ...inputMapping };

    this.controlledAvatars.set(avatarId, {
      entity,
      config: finalConfig,
      inputMapping: finalInputMapping,
      currentVelocity: new Vector3(0, 0, 0),
      targetVelocity: new Vector3(0, 0, 0),
      isGrounded: true,
      lastPosition: this.convertThreeVector3ToVector3(entity.getTransform().getPosition())
    });

    // 如果没有激活的虚拟化身，设置为激活
    if (!this.activeAvatarId) {
      this.setActiveAvatar(avatarId);
    }

    this.eventEmitter.emit('avatarAdded', { avatarId });

    if (this.config.debug) {
      console.log(`虚拟化身 ${avatarId} 已添加到控制系统`);
    }
  }

  /**
   * 移除受控虚拟化身
   */
  public removeControlledAvatar(avatarId: string): boolean {
    if (!this.controlledAvatars.has(avatarId)) {
      return false;
    }

    this.controlledAvatars.delete(avatarId);

    // 如果移除的是激活的虚拟化身，切换到其他虚拟化身
    if (this.activeAvatarId === avatarId) {
      const remainingAvatars = Array.from(this.controlledAvatars.keys());
      this.activeAvatarId = remainingAvatars.length > 0 ? remainingAvatars[0] : null;
    }

    this.eventEmitter.emit('avatarRemoved', { avatarId });

    if (this.config.debug) {
      console.log(`虚拟化身 ${avatarId} 已从控制系统移除`);
    }

    return true;
  }

  /**
   * 设置激活的虚拟化身
   */
  public setActiveAvatar(avatarId: string): boolean {
    if (!this.controlledAvatars.has(avatarId)) {
      return false;
    }

    const previousAvatarId = this.activeAvatarId;
    this.activeAvatarId = avatarId;

    this.eventEmitter.emit('activeAvatarChanged', {
      previousAvatarId,
      newAvatarId: avatarId
    });

    if (this.config.debug) {
      console.log(`激活虚拟化身: ${avatarId}`);
    }

    return true;
  }

  /**
   * 获取激活的虚拟化身ID
   */
  public getActiveAvatarId(): string | null {
    return this.activeAvatarId;
  }

  /**
   * 获取受控虚拟化身列表
   */
  public getControlledAvatars(): string[] {
    return Array.from(this.controlledAvatars.keys());
  }

  /**
   * 更新虚拟化身控制配置
   */
  public updateAvatarConfig(avatarId: string, config: Partial<ControlConfig>): boolean {
    const avatar = this.controlledAvatars.get(avatarId);
    if (!avatar) {
      return false;
    }

    Object.assign(avatar.config, config);

    this.eventEmitter.emit('avatarConfigUpdated', { avatarId, config });

    if (this.config.debug) {
      console.log(`虚拟化身 ${avatarId} 配置已更新`);
    }

    return true;
  }

  /**
   * 更新输入映射
   */
  public updateInputMapping(avatarId: string, inputMapping: Partial<InputMapping>): boolean {
    const avatar = this.controlledAvatars.get(avatarId);
    if (!avatar) {
      return false;
    }

    Object.assign(avatar.inputMapping, inputMapping);

    this.eventEmitter.emit('inputMappingUpdated', { avatarId, inputMapping });

    if (this.config.debug) {
      console.log(`虚拟化身 ${avatarId} 输入映射已更新`);
    }

    return true;
  }

  /**
   * 直接移动虚拟化身
   */
  public moveAvatar(avatarId: string, direction: Vector3, speed?: number): boolean {
    const avatar = this.controlledAvatars.get(avatarId);
    if (!avatar) {
      return false;
    }

    const moveSpeed = speed || avatar.config.moveSpeed;
    const transform = avatar.entity.getTransform();
    const currentPosition = transform.getPosition();

    const newPosition = new Vector3(
      currentPosition.x + direction.x * moveSpeed,
      currentPosition.y + direction.y * moveSpeed,
      currentPosition.z + direction.z * moveSpeed
    );

    transform.setPosition(newPosition.x, newPosition.y, newPosition.z);

    this.eventEmitter.emit('avatarMoved', {
      avatarId,
      position: newPosition,
      direction
    });

    return true;
  }

  /**
   * 直接旋转虚拟化身
   */
  public rotateAvatar(avatarId: string, rotation: Vector3): boolean {
    const avatar = this.controlledAvatars.get(avatarId);
    if (!avatar) {
      return false;
    }

    const transform = avatar.entity.getTransform();
    transform.setRotation(rotation.x, rotation.y, rotation.z);

    this.eventEmitter.emit('avatarRotated', {
      avatarId,
      rotation
    });

    return true;
  }

  /**
   * 传送虚拟化身
   */
  public teleportAvatar(avatarId: string, position: Vector3, rotation?: Vector3): boolean {
    const avatar = this.controlledAvatars.get(avatarId);
    if (!avatar) {
      return false;
    }

    const transform = avatar.entity.getTransform();
    transform.setPosition(position.x, position.y, position.z);

    if (rotation) {
      transform.setRotation(rotation.x, rotation.y, rotation.z);
    }

    // 重置速度
    avatar.currentVelocity = new Vector3(0, 0, 0);
    avatar.targetVelocity = new Vector3(0, 0, 0);

    this.eventEmitter.emit('avatarTeleported', {
      avatarId,
      position,
      rotation
    });

    if (this.config.debug) {
      console.log(`虚拟化身 ${avatarId} 已传送到 ${position.x}, ${position.y}, ${position.z}`);
    }

    return true;
  }

  /**
   * 系统更新
   */
  public update(deltaTime: number): void {
    if (!this.activeAvatarId) return;

    const avatar = this.controlledAvatars.get(this.activeAvatarId);
    if (!avatar) return;

    // 计算目标速度
    this.calculateTargetVelocity(avatar);

    // 应用平滑移动
    if (avatar.config.enableSmoothMovement) {
      this.applySmoothMovement(avatar, deltaTime);
    } else {
      avatar.currentVelocity = avatar.targetVelocity.clone();
    }

    // 应用移动
    this.applyMovement(avatar, deltaTime);

    // 应用重力
    if (avatar.config.enableGravity) {
      this.applyGravity(avatar, deltaTime);
    }

    // 碰撞检测
    if (avatar.config.enableCollision) {
      this.checkCollisions(avatar);
    }
  }

  /**
   * 计算目标速度
   */
  private calculateTargetVelocity(avatar: any): void {
    const config = avatar.config;
    let moveSpeed = config.moveSpeed;

    // 跑步模式速度加倍
    if (this.inputState.run) {
      moveSpeed *= 2.0;
    }

    // 计算移动方向
    let moveX = 0;
    let moveZ = 0;

    if (this.inputState.moveForward) moveZ -= 1;
    if (this.inputState.moveBackward) moveZ += 1;
    if (this.inputState.moveLeft) moveX -= 1;
    if (this.inputState.moveRight) moveX += 1;

    // 归一化移动向量
    const moveLength = Math.sqrt(moveX * moveX + moveZ * moveZ);
    if (moveLength > 0) {
      moveX /= moveLength;
      moveZ /= moveLength;
    }

    // 应用移动速度
    avatar.targetVelocity.x = moveX * moveSpeed;
    avatar.targetVelocity.z = moveZ * moveSpeed;

    // 跳跃
    if (this.inputState.jump && avatar.isGrounded) {
      avatar.targetVelocity.y = config.jumpForce;
      avatar.isGrounded = false;
    }
  }

  /**
   * 应用平滑移动
   */
  private applySmoothMovement(avatar: any, deltaTime: number): void {
    const smoothFactor = avatar.config.smoothFactor;

    avatar.currentVelocity.x = this.lerp(
      avatar.currentVelocity.x,
      avatar.targetVelocity.x,
      smoothFactor
    );

    avatar.currentVelocity.z = this.lerp(
      avatar.currentVelocity.z,
      avatar.targetVelocity.z,
      smoothFactor
    );

    // Y轴速度不进行平滑处理（重力和跳跃）
    avatar.currentVelocity.y = avatar.targetVelocity.y;
  }

  /**
   * 应用移动
   */
  private applyMovement(avatar: any, deltaTime: number): void {
    const transform = avatar.entity.getTransform();
    const currentPosition = transform.getPosition();

    const newPosition = new Vector3(
      currentPosition.x + avatar.currentVelocity.x * deltaTime,
      currentPosition.y + avatar.currentVelocity.y * deltaTime,
      currentPosition.z + avatar.currentVelocity.z * deltaTime
    );

    transform.setPosition(newPosition.x, newPosition.y, newPosition.z);

    // 检查是否移动了
    const moved = !newPosition.equals(avatar.lastPosition);
    if (moved) {
      this.eventEmitter.emit('avatarMoved', {
        avatarId: this.activeAvatarId,
        position: newPosition,
        velocity: avatar.currentVelocity
      });

      avatar.lastPosition = newPosition.clone();
    }
  }

  /**
   * 应用重力
   */
  private applyGravity(avatar: any, deltaTime: number): void {
    if (!avatar.isGrounded) {
      const gravity = -9.81; // 重力加速度
      avatar.targetVelocity.y += gravity * deltaTime;
    }
  }

  /**
   * 检查碰撞
   */
  private checkCollisions(avatar: any): void {
    // 简化的地面检测
    const transform = avatar.entity.getTransform();
    const position = transform.getPosition();

    if (position.y <= 0) {
      transform.setPosition(position.x, 0, position.z);
      avatar.currentVelocity.y = 0;
      avatar.targetVelocity.y = 0;
      avatar.isGrounded = true;
    }
  }

  /**
   * 线性插值
   */
  private lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  /**
   * 转换THREE.Vector3到自定义Vector3
   */
  private convertThreeVector3ToVector3(threeVector: THREE.Vector3): Vector3 {
    return new Vector3(threeVector.x, threeVector.y, threeVector.z);
  }

  /**
   * 转换自定义Vector3到THREE.Vector3
   */
  private convertVector3ToThreeVector3(vector: Vector3): THREE.Vector3 {
    return new THREE.Vector3(vector.x, vector.y, vector.z);
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener?: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 系统销毁
   */
  public destroy(): void {
    this.controlledAvatars.clear();
    this.activeAvatarId = null;
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('虚拟化身控制系统已销毁');
    }
  }
}
