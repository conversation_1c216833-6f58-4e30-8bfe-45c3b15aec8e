/**
 * 自动骨骼绑定系统
 * 为虚拟化身自动添加标准人体骨骼结构
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';

/**
 * 虚拟化身数据接口
 */
export interface AvatarData {
  /** 身体几何数据 */
  bodyData: {
    geometry: THREE.BufferGeometry;
    material?: THREE.Material;
    texture?: THREE.Texture;
  };
  /** 面部几何数据 */
  faceData?: {
    geometry: THREE.BufferGeometry;
    material?: THREE.Material;
    texture?: THREE.Texture;
  };
  /** 头发几何数据 */
  hairData?: {
    geometry: THREE.BufferGeometry;
    material?: THREE.Material;
    texture?: THREE.Texture;
  };
  /** 服装几何数据 */
  clothingData?: {
    geometry: THREE.BufferGeometry;
    material?: THREE.Material;
    texture?: THREE.Texture;
  };
  /** 元数据 */
  metadata: {
    name: string;
    gender: 'male' | 'female' | 'unisex';
    height: number;
    weight: number;
    age?: number;
    ethnicity?: string;
  };
}

/**
 * 身体部位信息
 */
export interface BodyPartInfo {
  /** 中心点 */
  center: THREE.Vector3;
  /** 边界框 */
  boundingBox: THREE.Box3;
  /** 顶点索引 */
  vertexIndices: number[];
  /** 置信度 */
  confidence: number;
  /** 对称性 */
  symmetry: number;
}

/**
 * 几何分析结果
 */
export interface GeometryAnalysis {
  /** 身体部位映射 */
  bodyParts: Map<string, BodyPartInfo>;
  /** 身体比例 */
  proportions: {
    headToBodyRatio: number;
    armToBodyRatio: number;
    legToBodyRatio: number;
    shoulderWidth: number;
    hipWidth: number;
    torsoLength: number;
  };
  /** 关键点 */
  landmarks: THREE.Vector3[];
  /** 整体对称性 */
  symmetry: number;
}

/**
 * 骨骼数据
 */
export interface SkeletonData {
  /** 骨骼结构 */
  skeleton: THREE.Skeleton;
  /** 蒙皮网格 */
  skinnedMesh: THREE.SkinnedMesh;
  /** 蒙皮权重 */
  weights: Float32Array;
  /** 骨骼映射 */
  boneMapping: Map<string, number>;
  /** 质量评分 */
  qualityScore: number;
}

/**
 * 自动骨骼绑定系统
 */
export class AutoSkeletonRiggingSystem extends System {
  public static readonly TYPE = 'AutoSkeletonRiggingSystem';

  /** 标准人体骨骼名称 */
  private static readonly STANDARD_BONE_NAMES = [
    'Hips',
    'Spine', 'Spine1', 'Spine2',
    'Neck', 'Head',
    'LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand',
    'RightShoulder', 'RightArm', 'RightForeArm', 'RightHand',
    'LeftUpLeg', 'LeftLeg', 'LeftFoot',
    'RightUpLeg', 'RightLeg', 'RightFoot'
  ];

  /** 骨骼层次结构 */
  private static readonly BONE_HIERARCHY = {
    'Hips': {
      position: [0, 1, 0],
      children: {
        'Spine': {
          position: [0, 0.1, 0],
          children: {
            'Spine1': {
              position: [0, 0.15, 0],
              children: {
                'Spine2': {
                  position: [0, 0.15, 0],
                  children: {
                    'Neck': {
                      position: [0, 0.2, 0],
                      children: {
                        'Head': {
                          position: [0, 0.15, 0]
                        }
                      }
                    },
                    'LeftShoulder': {
                      position: [-0.15, 0.15, 0],
                      children: {
                        'LeftArm': {
                          position: [-0.25, 0, 0],
                          children: {
                            'LeftForeArm': {
                              position: [-0.25, 0, 0],
                              children: {
                                'LeftHand': {
                                  position: [-0.2, 0, 0]
                                }
                              }
                            }
                          }
                        }
                      }
                    },
                    'RightShoulder': {
                      position: [0.15, 0.15, 0],
                      children: {
                        'RightArm': {
                          position: [0.25, 0, 0],
                          children: {
                            'RightForeArm': {
                              position: [0.25, 0, 0],
                              children: {
                                'RightHand': {
                                  position: [0.2, 0, 0]
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        'LeftUpLeg': {
          position: [-0.1, -0.05, 0],
          children: {
            'LeftLeg': {
              position: [0, -0.4, 0],
              children: {
                'LeftFoot': {
                  position: [0, -0.4, 0]
                }
              }
            }
          }
        },
        'RightUpLeg': {
          position: [0.1, -0.05, 0],
          children: {
            'RightLeg': {
              position: [0, -0.4, 0],
              children: {
                'RightFoot': {
                  position: [0, -0.4, 0]
                }
              }
            }
          }
        }
      }
    }
  };

  constructor() {
    super(0); // 使用默认优先级
  }

  /**
   * 创建系统实例
   * @returns 新的系统实例
   */
  protected createInstance(): System {
    return new AutoSkeletonRiggingSystem();
  }

  /**
   * 自动为虚拟化身添加骨骼
   * @param avatarData 虚拟化身数据
   * @returns 骨骼数据
   */
  public async autoRigAvatar(avatarData: AvatarData): Promise<SkeletonData> {
    console.log('开始自动骨骼绑定...', avatarData.metadata);

    try {
      // 1. 几何分析 - 识别身体部位
      const geometryAnalysis = await this.analyzeAvatarGeometry(avatarData);
      console.log('几何分析完成', geometryAnalysis);

      // 2. 骨骼生成 - 创建标准人体骨骼
      const skeleton = this.generateHumanoidSkeleton(geometryAnalysis);
      console.log('骨骼生成完成', skeleton);

      // 3. 权重计算 - 自动蒙皮权重分配
      const weights = await this.calculateSkinWeights(avatarData.bodyData.geometry, skeleton);
      console.log('权重计算完成', weights.length);

      // 4. 质量验证 - 检测和修复绑定问题
      const validatedSkeleton = this.validateAndFixRigging(skeleton, weights);
      console.log('质量验证完成');

      // 5. 创建骨骼网格
      const skinnedMesh = this.createSkinnedMesh(
        avatarData.bodyData.geometry,
        validatedSkeleton,
        weights
      );
      console.log('骨骼网格创建完成');

      const result: SkeletonData = {
        skeleton: validatedSkeleton,
        skinnedMesh,
        weights,
        boneMapping: this.generateBoneMapping(validatedSkeleton),
        qualityScore: this.assessRiggingQuality(validatedSkeleton, weights)
      };

      console.log('自动骨骼绑定完成', {
        boneCount: validatedSkeleton.bones.length,
        qualityScore: result.qualityScore
      });

      return result;
    } catch (error) {
      console.error('自动骨骼绑定失败:', error);
      throw error;
    }
  }

  /**
   * 分析虚拟化身几何结构
   * @param avatarData 虚拟化身数据
   * @returns 几何分析结果
   */
  private async analyzeAvatarGeometry(avatarData: AvatarData): Promise<GeometryAnalysis> {
    const analysis: GeometryAnalysis = {
      bodyParts: new Map<string, BodyPartInfo>(),
      proportions: {
        headToBodyRatio: 0,
        armToBodyRatio: 0,
        legToBodyRatio: 0,
        shoulderWidth: 0,
        hipWidth: 0,
        torsoLength: 0
      },
      landmarks: [],
      symmetry: 0
    };

    // 基于顶点分布识别身体部位
    const geometry = avatarData.bodyData.geometry;
    const positions = geometry.attributes.position.array as Float32Array;
    const vertices: THREE.Vector3[] = [];

    // 转换顶点数组为Vector3数组
    for (let i = 0; i < positions.length; i += 3) {
      vertices.push(new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]));
    }

    // 计算整体边界框
    const boundingBox = new THREE.Box3().setFromPoints(vertices);
    const center = boundingBox.getCenter(new THREE.Vector3());
    const size = boundingBox.getSize(new THREE.Vector3());

    // 头部检测 - 基于Y轴最高部分
    const headRegion = this.detectHeadRegion(vertices, boundingBox);
    analysis.bodyParts.set('head', headRegion);

    // 躯干检测 - 基于中心区域
    const torsoRegion = this.detectTorsoRegion(vertices, boundingBox);
    analysis.bodyParts.set('torso', torsoRegion);

    // 四肢检测
    const armRegions = this.detectArmRegions(vertices, boundingBox);
    analysis.bodyParts.set('leftArm', armRegions.left);
    analysis.bodyParts.set('rightArm', armRegions.right);

    const legRegions = this.detectLegRegions(vertices, boundingBox);
    analysis.bodyParts.set('leftLeg', legRegions.left);
    analysis.bodyParts.set('rightLeg', legRegions.right);

    // 计算身体比例
    analysis.proportions = this.calculateBodyProportions(analysis.bodyParts, size);

    // 计算对称性
    analysis.symmetry = this.calculateSymmetry(vertices, center);

    // 生成关键点
    analysis.landmarks = this.generateLandmarks(analysis.bodyParts);

    return analysis;
  }

  /**
   * 检测头部区域
   * @param vertices 顶点数组
   * @param boundingBox 边界框
   * @returns 头部信息
   */
  private detectHeadRegion(vertices: THREE.Vector3[], boundingBox: THREE.Box3): BodyPartInfo {
    const size = boundingBox.getSize(new THREE.Vector3());
    const headThreshold = boundingBox.max.y - size.y * 0.15; // 头部占总高度的15%

    const headVertices: number[] = [];
    const headPoints: THREE.Vector3[] = [];

    vertices.forEach((vertex, index) => {
      if (vertex.y > headThreshold) {
        headVertices.push(index);
        headPoints.push(vertex);
      }
    });

    const headBoundingBox = new THREE.Box3().setFromPoints(headPoints);
    const headCenter = headBoundingBox.getCenter(new THREE.Vector3());

    return {
      center: headCenter,
      boundingBox: headBoundingBox,
      vertexIndices: headVertices,
      confidence: headVertices.length > 0 ? 0.9 : 0.1,
      symmetry: this.calculateRegionSymmetry(headPoints, headCenter)
    };
  }

  /**
   * 检测躯干区域
   * @param vertices 顶点数组
   * @param boundingBox 边界框
   * @returns 躯干信息
   */
  private detectTorsoRegion(vertices: THREE.Vector3[], boundingBox: THREE.Box3): BodyPartInfo {
    const size = boundingBox.getSize(new THREE.Vector3());
    const center = boundingBox.getCenter(new THREE.Vector3());

    // 躯干区域：Y轴中间部分，X轴中心区域
    const torsoYMin = boundingBox.min.y + size.y * 0.3;
    const torsoYMax = boundingBox.max.y - size.y * 0.15;
    const torsoXRange = size.x * 0.3;

    const torsoVertices: number[] = [];
    const torsoPoints: THREE.Vector3[] = [];

    vertices.forEach((vertex, index) => {
      if (vertex.y >= torsoYMin && vertex.y <= torsoYMax &&
          Math.abs(vertex.x - center.x) <= torsoXRange) {
        torsoVertices.push(index);
        torsoPoints.push(vertex);
      }
    });

    const torsoBoundingBox = new THREE.Box3().setFromPoints(torsoPoints);
    const torsoCenter = torsoBoundingBox.getCenter(new THREE.Vector3());

    return {
      center: torsoCenter,
      boundingBox: torsoBoundingBox,
      vertexIndices: torsoVertices,
      confidence: torsoVertices.length > 0 ? 0.85 : 0.1,
      symmetry: this.calculateRegionSymmetry(torsoPoints, torsoCenter)
    };
  }

  /**
   * 检测手臂区域
   * @param vertices 顶点数组
   * @param boundingBox 边界框
   * @returns 左右手臂信息
   */
  private detectArmRegions(vertices: THREE.Vector3[], boundingBox: THREE.Box3): {
    left: BodyPartInfo;
    right: BodyPartInfo;
  } {
    const size = boundingBox.getSize(new THREE.Vector3());
    const center = boundingBox.getCenter(new THREE.Vector3());

    // 手臂区域：Y轴上半部分，X轴两侧
    const armYMin = boundingBox.min.y + size.y * 0.4;
    const armYMax = boundingBox.max.y - size.y * 0.15;
    const armXThreshold = size.x * 0.2;

    const leftArmVertices: number[] = [];
    const leftArmPoints: THREE.Vector3[] = [];
    const rightArmVertices: number[] = [];
    const rightArmPoints: THREE.Vector3[] = [];

    vertices.forEach((vertex, index) => {
      if (vertex.y >= armYMin && vertex.y <= armYMax) {
        if (vertex.x < center.x - armXThreshold) {
          // 左臂
          leftArmVertices.push(index);
          leftArmPoints.push(vertex);
        } else if (vertex.x > center.x + armXThreshold) {
          // 右臂
          rightArmVertices.push(index);
          rightArmPoints.push(vertex);
        }
      }
    });

    const leftArmBoundingBox = new THREE.Box3().setFromPoints(leftArmPoints);
    const leftArmCenter = leftArmBoundingBox.getCenter(new THREE.Vector3());

    const rightArmBoundingBox = new THREE.Box3().setFromPoints(rightArmPoints);
    const rightArmCenter = rightArmBoundingBox.getCenter(new THREE.Vector3());

    return {
      left: {
        center: leftArmCenter,
        boundingBox: leftArmBoundingBox,
        vertexIndices: leftArmVertices,
        confidence: leftArmVertices.length > 0 ? 0.8 : 0.1,
        symmetry: this.calculateRegionSymmetry(leftArmPoints, leftArmCenter)
      },
      right: {
        center: rightArmCenter,
        boundingBox: rightArmBoundingBox,
        vertexIndices: rightArmVertices,
        confidence: rightArmVertices.length > 0 ? 0.8 : 0.1,
        symmetry: this.calculateRegionSymmetry(rightArmPoints, rightArmCenter)
      }
    };
  }

  /**
   * 检测腿部区域
   * @param vertices 顶点数组
   * @param boundingBox 边界框
   * @returns 左右腿信息
   */
  private detectLegRegions(vertices: THREE.Vector3[], boundingBox: THREE.Box3): {
    left: BodyPartInfo;
    right: BodyPartInfo;
  } {
    const size = boundingBox.getSize(new THREE.Vector3());
    const center = boundingBox.getCenter(new THREE.Vector3());

    // 腿部区域：Y轴下半部分，X轴两侧
    const legYMin = boundingBox.min.y;
    const legYMax = boundingBox.min.y + size.y * 0.6;
    const legXThreshold = size.x * 0.1;

    const leftLegVertices: number[] = [];
    const leftLegPoints: THREE.Vector3[] = [];
    const rightLegVertices: number[] = [];
    const rightLegPoints: THREE.Vector3[] = [];

    vertices.forEach((vertex, index) => {
      if (vertex.y >= legYMin && vertex.y <= legYMax) {
        if (vertex.x < center.x - legXThreshold) {
          // 左腿
          leftLegVertices.push(index);
          leftLegPoints.push(vertex);
        } else if (vertex.x > center.x + legXThreshold) {
          // 右腿
          rightLegVertices.push(index);
          rightLegPoints.push(vertex);
        }
      }
    });

    const leftLegBoundingBox = new THREE.Box3().setFromPoints(leftLegPoints);
    const leftLegCenter = leftLegBoundingBox.getCenter(new THREE.Vector3());

    const rightLegBoundingBox = new THREE.Box3().setFromPoints(rightLegPoints);
    const rightLegCenter = rightLegBoundingBox.getCenter(new THREE.Vector3());

    return {
      left: {
        center: leftLegCenter,
        boundingBox: leftLegBoundingBox,
        vertexIndices: leftLegVertices,
        confidence: leftLegVertices.length > 0 ? 0.8 : 0.1,
        symmetry: this.calculateRegionSymmetry(leftLegPoints, leftLegCenter)
      },
      right: {
        center: rightLegCenter,
        boundingBox: rightLegBoundingBox,
        vertexIndices: rightLegVertices,
        confidence: rightLegVertices.length > 0 ? 0.8 : 0.1,
        symmetry: this.calculateRegionSymmetry(rightLegPoints, rightLegCenter)
      }
    };
  }

  /**
   * 计算身体比例
   * @param bodyParts 身体部位映射
   * @param totalSize 总体尺寸
   * @returns 身体比例
   */
  private calculateBodyProportions(bodyParts: Map<string, BodyPartInfo>, totalSize: THREE.Vector3) {
    const head = bodyParts.get('head');
    const torso = bodyParts.get('torso');
    const leftArm = bodyParts.get('leftArm');
    const rightArm = bodyParts.get('rightArm');
    const leftLeg = bodyParts.get('leftLeg');
    const rightLeg = bodyParts.get('rightLeg');

    const headHeight = head ? head.boundingBox.getSize(new THREE.Vector3()).y : 0;
    const torsoHeight = torso ? torso.boundingBox.getSize(new THREE.Vector3()).y : 0;
    const armLength = leftArm ? leftArm.boundingBox.getSize(new THREE.Vector3()).x : 0;
    const legLength = leftLeg ? leftLeg.boundingBox.getSize(new THREE.Vector3()).y : 0;

    return {
      headToBodyRatio: headHeight / totalSize.y,
      armToBodyRatio: armLength / totalSize.x,
      legToBodyRatio: legLength / totalSize.y,
      shoulderWidth: torso ? torso.boundingBox.getSize(new THREE.Vector3()).x : 0,
      hipWidth: torso ? torso.boundingBox.getSize(new THREE.Vector3()).x * 0.8 : 0,
      torsoLength: torsoHeight
    };
  }

  /**
   * 计算对称性
   * @param vertices 顶点数组
   * @param center 中心点
   * @returns 对称性评分 (0-1)
   */
  private calculateSymmetry(vertices: THREE.Vector3[], center: THREE.Vector3): number {
    let symmetryScore = 0;
    let validPairs = 0;

    // 对于每个顶点，寻找其在X轴对称的对应点
    for (const vertex of vertices) {
      const mirrorX = center.x * 2 - vertex.x;
      const mirrorPoint = new THREE.Vector3(mirrorX, vertex.y, vertex.z);

      // 寻找最近的对应点
      let minDistance = Infinity;
      for (const otherVertex of vertices) {
        const distance = mirrorPoint.distanceTo(otherVertex);
        if (distance < minDistance) {
          minDistance = distance;
        }
      }

      // 如果找到足够近的对应点，增加对称性评分
      if (minDistance < 0.1) { // 阈值可调整
        symmetryScore += 1;
      }
      validPairs++;
    }

    return validPairs > 0 ? symmetryScore / validPairs : 0;
  }

  /**
   * 计算区域对称性
   * @param points 区域点集
   * @param center 区域中心
   * @returns 对称性评分
   */
  private calculateRegionSymmetry(points: THREE.Vector3[], center: THREE.Vector3): number {
    if (points.length === 0) return 0;

    let symmetryScore = 0;
    for (const point of points) {
      const distanceFromCenter = point.distanceTo(center);
      // 基于点到中心的距离分布计算对称性
      symmetryScore += 1 / (1 + distanceFromCenter);
    }

    return symmetryScore / points.length;
  }

  /**
   * 生成关键点
   * @param bodyParts 身体部位映射
   * @returns 关键点数组
   */
  private generateLandmarks(bodyParts: Map<string, BodyPartInfo>): THREE.Vector3[] {
    const landmarks: THREE.Vector3[] = [];

    // 添加各部位的中心点作为关键点
    for (const [partName, partInfo] of bodyParts.entries()) {
      landmarks.push(partInfo.center.clone());
    }

    return landmarks;
  }

  /**
   * 生成标准人体骨骼结构
   * @param analysis 几何分析结果
   * @returns 骨骼结构
   */
  private generateHumanoidSkeleton(analysis: GeometryAnalysis): THREE.Skeleton {
    const bones: THREE.Bone[] = [];
    const boneMap = new Map<string, THREE.Bone>();

    // 根据分析结果调整骨骼位置
    const scaleFactor = this.calculateScaleFactor(analysis);

    // 递归创建骨骼层次结构
    const createBoneHierarchy = (
      boneData: any,
      boneName: string,
      parent?: THREE.Bone
    ): THREE.Bone => {
      const bone = new THREE.Bone();
      bone.name = boneName;

      // 设置骨骼位置（根据分析结果调整）
      const position = this.adjustBonePosition(
        boneData.position,
        boneName,
        analysis,
        scaleFactor
      );
      bone.position.set(position[0], position[1], position[2]);

      // 添加到映射和数组
      boneMap.set(boneName, bone);
      bones.push(bone);

      // 设置父子关系
      if (parent) {
        parent.add(bone);
      }

      // 递归创建子骨骼
      if (boneData.children) {
        for (const [childName, childData] of Object.entries(boneData.children)) {
          createBoneHierarchy(childData, childName, bone);
        }
      }

      return bone;
    };

    // 创建根骨骼
    const rootBone = createBoneHierarchy(
      AutoSkeletonRiggingSystem.BONE_HIERARCHY.Hips,
      'Hips'
    );

    // 创建骨骼结构
    const skeleton = new THREE.Skeleton(bones);

    // 设置骨骼的初始变换
    this.initializeBoneTransforms(skeleton, analysis);

    return skeleton;
  }

  /**
   * 计算缩放因子
   * @param analysis 几何分析结果
   * @returns 缩放因子
   */
  private calculateScaleFactor(analysis: GeometryAnalysis): number {
    // 基于身体比例计算合适的缩放因子
    const avgRatio = (
      analysis.proportions.headToBodyRatio +
      analysis.proportions.armToBodyRatio +
      analysis.proportions.legToBodyRatio
    ) / 3;

    // 标准人体比例参考值
    const standardRatio = 0.3;
    return avgRatio / standardRatio;
  }

  /**
   * 调整骨骼位置
   * @param basePosition 基础位置
   * @param boneName 骨骼名称
   * @param analysis 几何分析结果
   * @param scaleFactor 缩放因子
   * @returns 调整后的位置
   */
  private adjustBonePosition(
    basePosition: number[],
    boneName: string,
    analysis: GeometryAnalysis,
    scaleFactor: number
  ): number[] {
    const adjustedPosition = [
      basePosition[0] * scaleFactor,
      basePosition[1] * scaleFactor,
      basePosition[2] * scaleFactor
    ];

    // 根据身体部位分析结果微调位置
    const bodyParts = analysis.bodyParts;

    switch (boneName) {
      case 'Hips':
        const torso = bodyParts.get('torso');
        if (torso) {
          adjustedPosition[1] = torso.center.y;
        }
        break;
      case 'Head':
        const head = bodyParts.get('head');
        if (head) {
          adjustedPosition[1] = head.center.y - adjustedPosition[1];
        }
        break;
      case 'LeftShoulder':
      case 'RightShoulder':
        const shoulderSide = boneName.startsWith('Left') ? 'leftArm' : 'rightArm';
        const arm = bodyParts.get(shoulderSide);
        if (arm) {
          adjustedPosition[0] = boneName.startsWith('Left') ?
            -Math.abs(adjustedPosition[0]) : Math.abs(adjustedPosition[0]);
        }
        break;
      case 'LeftUpLeg':
      case 'RightUpLeg':
        const legSide = boneName.startsWith('Left') ? 'leftLeg' : 'rightLeg';
        const leg = bodyParts.get(legSide);
        if (leg) {
          adjustedPosition[0] = boneName.startsWith('Left') ?
            -Math.abs(adjustedPosition[0]) : Math.abs(adjustedPosition[0]);
        }
        break;
    }

    return adjustedPosition;
  }

  /**
   * 初始化骨骼变换
   * @param skeleton 骨骼结构
   * @param analysis 几何分析结果
   */
  private initializeBoneTransforms(skeleton: THREE.Skeleton, analysis: GeometryAnalysis): void {
    // 更新骨骼矩阵
    skeleton.bones.forEach(bone => {
      bone.updateMatrixWorld(true);
    });

    // 计算绑定矩阵
    skeleton.calculateInverses();
  }

  /**
   * 计算蒙皮权重
   * @param geometry 几何体
   * @param skeleton 骨骼结构
   * @returns 权重数组
   */
  private async calculateSkinWeights(
    geometry: THREE.BufferGeometry,
    skeleton: THREE.Skeleton
  ): Promise<Float32Array> {
    const positions = geometry.attributes.position.array as Float32Array;
    const vertexCount = positions.length / 3;
    const boneCount = skeleton.bones.length;

    // 创建权重数组 (每个顶点最多4个骨骼影响)
    const weights = new Float32Array(vertexCount * 4);
    const indices = new Float32Array(vertexCount * 4);

    // 为每个顶点计算权重
    for (let i = 0; i < vertexCount; i++) {
      const vertex = new THREE.Vector3(
        positions[i * 3],
        positions[i * 3 + 1],
        positions[i * 3 + 2]
      );

      // 计算顶点到各骨骼的距离和影响权重
      const boneWeights: { boneIndex: number; weight: number }[] = [];

      skeleton.bones.forEach((bone, boneIndex) => {
        const bonePosition = new THREE.Vector3();
        bone.getWorldPosition(bonePosition);

        const distance = vertex.distanceTo(bonePosition);
        const weight = this.calculateBoneWeight(vertex, bone, distance);

        if (weight > 0.01) { // 只保留有意义的权重
          boneWeights.push({ boneIndex, weight });
        }
      });

      // 排序并选择前4个最大权重
      boneWeights.sort((a, b) => b.weight - a.weight);
      const topWeights = boneWeights.slice(0, 4);

      // 归一化权重
      const totalWeight = topWeights.reduce((sum, item) => sum + item.weight, 0);

      // 设置权重和索引
      for (let j = 0; j < 4; j++) {
        const baseIndex = i * 4 + j;
        if (j < topWeights.length && totalWeight > 0) {
          weights[baseIndex] = topWeights[j].weight / totalWeight;
          indices[baseIndex] = topWeights[j].boneIndex;
        } else {
          weights[baseIndex] = 0;
          indices[baseIndex] = 0;
        }
      }
    }

    // 将索引数据添加到几何体
    geometry.setAttribute('skinIndex', new THREE.Uint16BufferAttribute(indices, 4));
    geometry.setAttribute('skinWeight', new THREE.Float32BufferAttribute(weights, 4));

    return weights;
  }

  /**
   * 计算骨骼权重
   * @param vertex 顶点位置
   * @param bone 骨骼
   * @param distance 距离
   * @returns 权重值
   */
  private calculateBoneWeight(vertex: THREE.Vector3, bone: THREE.Bone, distance: number): number {
    // 基于距离的权重计算，使用指数衰减
    const maxInfluenceDistance = 0.5; // 最大影响距离

    if (distance > maxInfluenceDistance) {
      return 0;
    }

    // 指数衰减函数
    const weight = Math.exp(-distance * 3);

    // 根据骨骼类型调整权重
    const boneTypeMultiplier = this.getBoneTypeMultiplier(bone.name);

    return weight * boneTypeMultiplier;
  }

  /**
   * 获取骨骼类型权重乘数
   * @param boneName 骨骼名称
   * @returns 权重乘数
   */
  private getBoneTypeMultiplier(boneName: string): number {
    // 主要骨骼有更高的影响权重
    if (boneName.includes('Spine') || boneName === 'Hips') {
      return 1.2;
    }
    if (boneName.includes('Shoulder')) {
      return 1.1;
    }
    if (boneName.includes('Head') || boneName.includes('Neck')) {
      return 1.0;
    }
    if (boneName.includes('Arm') || boneName.includes('Leg')) {
      return 0.9;
    }
    if (boneName.includes('Hand') || boneName.includes('Foot')) {
      return 0.8;
    }

    return 1.0;
  }

  /**
   * 验证和修复绑定
   * @param skeleton 骨骼结构
   * @param weights 权重数组
   * @returns 验证后的骨骼结构
   */
  private validateAndFixRigging(skeleton: THREE.Skeleton, weights: Float32Array): THREE.Skeleton {
    // 检查骨骼层次结构
    this.validateBoneHierarchy(skeleton);

    // 检查权重分布
    this.validateWeightDistribution(weights);

    // 修复权重问题
    this.fixWeightIssues(weights);

    return skeleton;
  }

  /**
   * 验证骨骼层次结构
   * @param skeleton 骨骼结构
   */
  private validateBoneHierarchy(skeleton: THREE.Skeleton): void {
    const requiredBones = AutoSkeletonRiggingSystem.STANDARD_BONE_NAMES;
    const existingBones = skeleton.bones.map(bone => bone.name);

    // 检查必需骨骼是否存在
    for (const requiredBone of requiredBones) {
      if (!existingBones.includes(requiredBone)) {
        console.warn(`缺少必需骨骼: ${requiredBone}`);
      }
    }

    // 检查骨骼连接
    skeleton.bones.forEach(bone => {
      if (bone.parent && !(bone.parent instanceof THREE.Bone)) {
        console.warn(`骨骼 ${bone.name} 的父级不是骨骼对象`);
      }
    });
  }

  /**
   * 验证权重分布
   * @param weights 权重数组
   */
  private validateWeightDistribution(weights: Float32Array): void {
    const vertexCount = weights.length / 4;

    for (let i = 0; i < vertexCount; i++) {
      const baseIndex = i * 4;
      let totalWeight = 0;

      for (let j = 0; j < 4; j++) {
        totalWeight += weights[baseIndex + j];
      }

      // 检查权重总和是否接近1
      if (Math.abs(totalWeight - 1.0) > 0.01) {
        console.warn(`顶点 ${i} 的权重总和异常: ${totalWeight}`);
      }
    }
  }

  /**
   * 修复权重问题
   * @param weights 权重数组
   */
  private fixWeightIssues(weights: Float32Array): void {
    const vertexCount = weights.length / 4;

    for (let i = 0; i < vertexCount; i++) {
      const baseIndex = i * 4;
      let totalWeight = 0;

      // 计算总权重
      for (let j = 0; j < 4; j++) {
        totalWeight += weights[baseIndex + j];
      }

      // 如果总权重为0，设置默认权重
      if (totalWeight === 0) {
        weights[baseIndex] = 1.0; // 分配给第一个骨骼
      } else if (Math.abs(totalWeight - 1.0) > 0.01) {
        // 归一化权重
        for (let j = 0; j < 4; j++) {
          weights[baseIndex + j] /= totalWeight;
        }
      }
    }
  }

  /**
   * 创建蒙皮网格
   * @param geometry 几何体
   * @param skeleton 骨骼结构
   * @param weights 权重数组
   * @returns 蒙皮网格
   */
  private createSkinnedMesh(
    geometry: THREE.BufferGeometry,
    skeleton: THREE.Skeleton,
    weights: Float32Array
  ): THREE.SkinnedMesh {
    // 创建默认材质
    const material = new THREE.MeshStandardMaterial({
      color: 0xffffff
    });

    // 创建蒙皮网格
    const skinnedMesh = new THREE.SkinnedMesh(geometry, material);

    // 绑定骨骼
    skinnedMesh.bind(skeleton);

    // 设置骨骼根节点
    if (skeleton.bones.length > 0) {
      skinnedMesh.add(skeleton.bones[0]);
    }

    return skinnedMesh;
  }

  /**
   * 生成骨骼映射
   * @param skeleton 骨骼结构
   * @returns 骨骼映射
   */
  private generateBoneMapping(skeleton: THREE.Skeleton): Map<string, number> {
    const boneMapping = new Map<string, number>();

    skeleton.bones.forEach((bone, index) => {
      boneMapping.set(bone.name, index);
    });

    return boneMapping;
  }

  /**
   * 评估绑定质量
   * @param skeleton 骨骼结构
   * @param weights 权重数组
   * @returns 质量评分 (0-1)
   */
  private assessRiggingQuality(skeleton: THREE.Skeleton, weights: Float32Array): number {
    let qualityScore = 0;
    let factors = 0;

    // 1. 骨骼完整性评分
    const boneCompletenessScore = this.assessBoneCompleteness(skeleton);
    qualityScore += boneCompletenessScore * 0.3;
    factors += 0.3;

    // 2. 权重分布评分
    const weightDistributionScore = this.assessWeightDistribution(weights);
    qualityScore += weightDistributionScore * 0.4;
    factors += 0.4;

    // 3. 骨骼层次结构评分
    const hierarchyScore = this.assessBoneHierarchy(skeleton);
    qualityScore += hierarchyScore * 0.3;
    factors += 0.3;

    return factors > 0 ? qualityScore / factors : 0;
  }

  /**
   * 评估骨骼完整性
   * @param skeleton 骨骼结构
   * @returns 完整性评分
   */
  private assessBoneCompleteness(skeleton: THREE.Skeleton): number {
    const requiredBones = AutoSkeletonRiggingSystem.STANDARD_BONE_NAMES;
    const existingBones = skeleton.bones.map(bone => bone.name);

    let foundCount = 0;
    for (const requiredBone of requiredBones) {
      if (existingBones.includes(requiredBone)) {
        foundCount++;
      }
    }

    return foundCount / requiredBones.length;
  }

  /**
   * 评估权重分布
   * @param weights 权重数组
   * @returns 分布评分
   */
  private assessWeightDistribution(weights: Float32Array): number {
    const vertexCount = weights.length / 4;
    let validVertices = 0;

    for (let i = 0; i < vertexCount; i++) {
      const baseIndex = i * 4;
      let totalWeight = 0;

      for (let j = 0; j < 4; j++) {
        totalWeight += weights[baseIndex + j];
      }

      // 检查权重是否合理
      if (totalWeight > 0.95 && totalWeight < 1.05) {
        validVertices++;
      }
    }

    return vertexCount > 0 ? validVertices / vertexCount : 0;
  }

  /**
   * 评估骨骼层次结构
   * @param skeleton 骨骼结构
   * @returns 层次结构评分
   */
  private assessBoneHierarchy(skeleton: THREE.Skeleton): number {
    let validConnections = 0;
    let totalConnections = 0;

    skeleton.bones.forEach(bone => {
      if (bone.children.length > 0) {
        totalConnections++;
        // 检查子骨骼是否都是Bone类型
        const validChildren = bone.children.filter(child => child instanceof THREE.Bone);
        if (validChildren.length === bone.children.length) {
          validConnections++;
        }
      }
    });

    return totalConnections > 0 ? validConnections / totalConnections : 1;
  }
}
