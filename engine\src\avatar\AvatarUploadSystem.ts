/**
 * 虚拟化身上传系统
 * 
 * 支持将保存的虚拟化身文件上传并加载到场景中成为数字人
 * 包括文件解析、数据验证、场景集成、数字人创建等功能
 */

import { System } from '../core/System';
import { World } from '../core/World';
import { Entity } from '../core/Entity';
import { EventEmitter } from 'events';
import { AvatarData } from './AvatarCustomizationSystem';
import { Vector3 } from '../math/Vector3';

/**
 * 上传文件信息接口
 */
export interface UploadFileInfo {
  /** 文件名 */
  fileName: string;
  /** 文件大小（字节） */
  fileSize: number;
  /** 文件类型 */
  fileType: string;
  /** 文件内容 */
  fileContent: ArrayBuffer | string;
  /** 上传时间 */
  uploadTime: Date;
  /** 文件哈希值 */
  fileHash?: string;
}

/**
 * 上传配置接口
 */
export interface UploadConfig {
  /** 目标场景ID */
  targetSceneId: string;
  /** 生成位置 */
  spawnPosition?: Vector3;
  /** 生成旋转 */
  spawnRotation?: Vector3;
  /** 生成缩放 */
  spawnScale?: Vector3;
  /** 是否自动激活 */
  autoActivate?: boolean;
  /** 数字人名称 */
  digitalHumanName?: string;
  /** 数字人配置 */
  digitalHumanConfig?: {
    enableAI?: boolean;
    enableVoice?: boolean;
    enableInteraction?: boolean;
    knowledgeBaseId?: string;
    personality?: string;
  };
  /** 验证选项 */
  validationOptions?: {
    strictMode?: boolean;
    allowPartialData?: boolean;
    skipChecksums?: boolean;
  };
}

/**
 * 上传结果接口
 */
export interface UploadResult {
  /** 上传是否成功 */
  success: boolean;
  /** 上传ID */
  uploadId: string;
  /** 解析的虚拟化身数据 */
  avatarData?: AvatarData;
  /** 创建的数字人实体 */
  digitalHumanEntity?: Entity;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 错误信息 */
  error?: string;
  /** 警告信息 */
  warnings?: string[];
  /** 验证结果 */
  validationResult?: {
    isValid: boolean;
    issues: string[];
    score: number; // 0-1
  };
}

/**
 * 虚拟化身上传系统配置
 */
export interface AvatarUploadSystemConfig {
  /** 支持的文件格式 */
  supportedFormats: string[];
  /** 最大文件大小（MB） */
  maxFileSize: number;
  /** 上传目录 */
  uploadDirectory: string;
  /** 最大并发上传数 */
  maxConcurrentUploads: number;
  /** 上传超时时间（毫秒） */
  uploadTimeout: number;
  /** 是否启用文件验证 */
  enableValidation: boolean;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 虚拟化身上传系统
 */
export class AvatarUploadSystem extends System {
  /** 系统名称 */
  public static readonly NAME = 'AvatarUploadSystem';

  /** 配置 */
  private config: AvatarUploadSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 上传队列 */
  private uploadQueue: Map<string, {
    fileInfo: UploadFileInfo;
    config: UploadConfig;
    resolve: (result: UploadResult) => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = new Map();

  /** 正在处理的上传任务 */
  private processingUploads: Set<string> = new Set();

  /** 上传历史记录 */
  private uploadHistory: Map<string, UploadResult[]> = new Map();

  /** 已创建的数字人 */
  private digitalHumans: Map<string, {
    entity: Entity;
    avatarData: AvatarData;
    config: UploadConfig;
    createdAt: Date;
  }> = new Map();

  /**
   * 构造函数
   */
  constructor(world: World, config: Partial<AvatarUploadSystemConfig> = {}) {
    super(0);
    this.setWorld(world);

    this.config = {
      supportedFormats: ['json', 'bin', 'gltf', 'fbx'],
      maxFileSize: 50, // 50MB
      uploadDirectory: './uploads/avatars',
      maxConcurrentUploads: 3,
      uploadTimeout: 60000, // 60秒
      enableValidation: true,
      debug: false,
      ...config
    };

    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    // 创建上传目录
    this.ensureUploadDirectory();

    // 启动上传队列处理器
    this.startUploadQueueProcessor();

    if (this.config.debug) {
      console.log('虚拟化身上传系统已初始化');
    }
  }

  /**
   * 确保上传目录存在
   */
  private ensureUploadDirectory(): void {
    try {
      const fs = require('fs');
      
      if (!fs.existsSync(this.config.uploadDirectory)) {
        fs.mkdirSync(this.config.uploadDirectory, { recursive: true });
      }
    } catch (error) {
      console.warn('无法创建上传目录:', error);
    }
  }

  /**
   * 启动上传队列处理器
   */
  private startUploadQueueProcessor(): void {
    setInterval(() => {
      this.processUploadQueue();
    }, 100); // 每100ms处理一次队列
  }

  /**
   * 处理上传队列
   */
  private async processUploadQueue(): Promise<void> {
    // 检查超时的任务
    const now = Date.now();
    for (const [uploadId, task] of this.uploadQueue) {
      if (now - task.timestamp > this.config.uploadTimeout) {
        this.uploadQueue.delete(uploadId);
        task.reject(new Error('上传超时'));
      }
    }

    // 处理新的上传任务
    if (this.processingUploads.size < this.config.maxConcurrentUploads) {
      for (const [uploadId, task] of this.uploadQueue) {
        if (!this.processingUploads.has(uploadId)) {
          this.processingUploads.add(uploadId);
          this.uploadQueue.delete(uploadId);

          try {
            const result = await this.performUpload(task.fileInfo, task.config);
            task.resolve(result);
          } catch (error) {
            task.reject(error);
          } finally {
            this.processingUploads.delete(uploadId);
          }

          // 达到并发限制，退出循环
          if (this.processingUploads.size >= this.config.maxConcurrentUploads) {
            break;
          }
        }
      }
    }
  }

  /**
   * 上传虚拟化身文件
   */
  public async uploadAvatarFile(
    fileInfo: UploadFileInfo,
    config: UploadConfig
  ): Promise<UploadResult> {
    const uploadId = this.generateUploadId(fileInfo.fileName);

    // 验证文件
    const validationResult = this.validateFile(fileInfo);
    if (!validationResult.isValid) {
      return {
        success: false,
        uploadId,
        processingTime: 0,
        error: `文件验证失败: ${validationResult.issues.join(', ')}`,
        validationResult
      };
    }

    return new Promise((resolve, reject) => {
      this.uploadQueue.set(uploadId, {
        fileInfo,
        config,
        resolve,
        reject,
        timestamp: Date.now()
      });

      this.eventEmitter.emit('uploadQueued', { uploadId, fileName: fileInfo.fileName });
    });
  }

  /**
   * 执行上传处理
   */
  private async performUpload(
    fileInfo: UploadFileInfo,
    config: UploadConfig
  ): Promise<UploadResult> {
    const startTime = Date.now();
    const uploadId = this.generateUploadId(fileInfo.fileName);

    try {
      // 1. 解析文件内容
      this.eventEmitter.emit('uploadProgress', { uploadId, stage: 'parsing', progress: 10 });
      const avatarData = await this.parseAvatarFile(fileInfo);

      // 2. 验证数据完整性
      this.eventEmitter.emit('uploadProgress', { uploadId, stage: 'validating', progress: 30 });
      const validationResult = await this.validateAvatarData(avatarData, config.validationOptions);

      // 3. 创建数字人实体
      this.eventEmitter.emit('uploadProgress', { uploadId, stage: 'creating', progress: 60 });
      const digitalHumanEntity = await this.createDigitalHuman(avatarData, config);

      // 4. 集成到场景
      this.eventEmitter.emit('uploadProgress', { uploadId, stage: 'integrating', progress: 80 });
      await this.integrateToScene(digitalHumanEntity, config);

      // 5. 完成处理
      this.eventEmitter.emit('uploadProgress', { uploadId, stage: 'completed', progress: 100 });

      const processingTime = Date.now() - startTime;
      const result: UploadResult = {
        success: true,
        uploadId,
        avatarData,
        digitalHumanEntity,
        processingTime,
        validationResult,
        warnings: validationResult.issues.length > 0 ? validationResult.issues : undefined
      };

      // 记录数字人信息
      this.digitalHumans.set(uploadId, {
        entity: digitalHumanEntity,
        avatarData,
        config,
        createdAt: new Date()
      });

      // 记录上传历史
      this.recordUploadHistory(config.targetSceneId, result);

      // 触发事件
      this.eventEmitter.emit('uploadCompleted', result);

      if (this.config.debug) {
        console.log(`虚拟化身上传完成: ${fileInfo.fileName}, 耗时: ${processingTime}ms`);
      }

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorResult: UploadResult = {
        success: false,
        uploadId,
        processingTime,
        error: error.message
      };

      this.eventEmitter.emit('uploadFailed', { uploadId, error });

      if (this.config.debug) {
        console.error(`虚拟化身上传失败: ${fileInfo.fileName}`, error);
      }

      return errorResult;
    }
  }

  /**
   * 验证文件
   */
  private validateFile(fileInfo: UploadFileInfo): {
    isValid: boolean;
    issues: string[];
    score: number;
  } {
    const issues: string[] = [];

    // 检查文件大小
    const fileSizeInMB = fileInfo.fileSize / (1024 * 1024);
    if (fileSizeInMB > this.config.maxFileSize) {
      issues.push(`文件大小 ${fileSizeInMB.toFixed(2)}MB 超过限制 ${this.config.maxFileSize}MB`);
    }

    // 检查文件格式
    const fileExtension = fileInfo.fileName.split('.').pop()?.toLowerCase();
    if (!fileExtension || !this.config.supportedFormats.includes(fileExtension)) {
      issues.push(`不支持的文件格式: ${fileExtension}`);
    }

    // 检查文件内容
    if (!fileInfo.fileContent ||
        (typeof fileInfo.fileContent === 'string' && fileInfo.fileContent.length === 0) ||
        (fileInfo.fileContent instanceof ArrayBuffer && fileInfo.fileContent.byteLength === 0)) {
      issues.push('文件内容为空');
    }

    const isValid = issues.length === 0;
    const score = isValid ? 1.0 : Math.max(0, 1 - issues.length * 0.2);

    return { isValid, issues, score };
  }

  /**
   * 解析虚拟化身文件
   */
  private async parseAvatarFile(fileInfo: UploadFileInfo): Promise<AvatarData> {
    const fileExtension = fileInfo.fileName.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'json':
        return this.parseJsonFile(fileInfo);
      case 'bin':
        return this.parseBinaryFile(fileInfo);
      case 'gltf':
        return this.parseGltfFile(fileInfo);
      case 'fbx':
        return this.parseFbxFile(fileInfo);
      default:
        throw new Error(`不支持的文件格式: ${fileExtension}`);
    }
  }

  /**
   * 解析JSON文件
   */
  private async parseJsonFile(fileInfo: UploadFileInfo): Promise<AvatarData> {
    try {
      const jsonContent = typeof fileInfo.fileContent === 'string' 
        ? fileInfo.fileContent 
        : new TextDecoder().decode(fileInfo.fileContent);
      
      const data = JSON.parse(jsonContent);
      
      // 验证必要字段
      if (!data.id) {
        data.id = `uploaded_${Date.now()}`;
      }
      
      if (!data.createdAt) {
        data.createdAt = new Date();
      }
      
      if (!data.updatedAt) {
        data.updatedAt = new Date();
      }

      return data as AvatarData;
    } catch (error) {
      throw new Error(`JSON文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析二进制文件
   */
  private async parseBinaryFile(fileInfo: UploadFileInfo): Promise<AvatarData> {
    try {
      // 简化的二进制解析，实际应该根据具体格式实现
      const content = typeof fileInfo.fileContent === 'string'
        ? fileInfo.fileContent
        : new TextDecoder().decode(fileInfo.fileContent);
      return JSON.parse(content) as AvatarData;
    } catch (error) {
      throw new Error(`二进制文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析GLTF文件
   */
  private async parseGltfFile(fileInfo: UploadFileInfo): Promise<AvatarData> {
    try {
      // 这里应该实现GLTF格式的解析
      // 暂时返回模拟数据
      return {
        id: `gltf_${Date.now()}`,
        userId: 'uploaded_user',
        createdAt: new Date(),
        updatedAt: new Date(),
        // 可以添加GLTF特有的数据字段
        gltfData: fileInfo.fileContent
      } as AvatarData;
    } catch (error) {
      throw new Error(`GLTF文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析FBX文件
   */
  private async parseFbxFile(fileInfo: UploadFileInfo): Promise<AvatarData> {
    try {
      // 这里应该实现FBX格式的解析
      // 暂时返回模拟数据
      return {
        id: `fbx_${Date.now()}`,
        userId: 'uploaded_user',
        createdAt: new Date(),
        updatedAt: new Date(),
        // 可以添加FBX特有的数据字段
        fbxData: fileInfo.fileContent
      } as AvatarData;
    } catch (error) {
      throw new Error(`FBX文件解析失败: ${error.message}`);
    }
  }

  /**
   * 验证虚拟化身数据
   */
  private async validateAvatarData(
    avatarData: AvatarData,
    options?: UploadConfig['validationOptions']
  ): Promise<{
    isValid: boolean;
    issues: string[];
    score: number;
  }> {
    const issues: string[] = [];
    const strictMode = options?.strictMode ?? true;

    // 验证必要字段
    if (!avatarData.id) {
      issues.push('缺少虚拟化身ID');
    }

    if (!avatarData.createdAt) {
      issues.push('缺少创建时间');
    }

    if (!avatarData.updatedAt) {
      issues.push('缺少更新时间');
    }

    // 严格模式下的额外验证
    if (strictMode) {
      if (!avatarData.userId) {
        issues.push('缺少用户ID');
      }

      // 可以添加更多验证规则
      if (avatarData.id.length < 3) {
        issues.push('虚拟化身ID过短');
      }
    }

    const isValid = issues.length === 0 || (options?.allowPartialData && issues.length <= 2);
    const score = Math.max(0, 1 - issues.length * 0.1);

    return { isValid, issues, score };
  }

  /**
   * 创建数字人实体
   */
  private async createDigitalHuman(
    avatarData: AvatarData,
    config: UploadConfig
  ): Promise<Entity> {
    // 创建数字人实体
    const entityName = config.digitalHumanName || `DigitalHuman_${avatarData.id}`;
    const entity = this.world.createEntity(entityName);

    // 设置变换
    const transform = entity.getTransform();
    const position = config.spawnPosition || new Vector3(0, 0, 0);
    const rotation = config.spawnRotation || new Vector3(0, 0, 0);
    const scale = config.spawnScale || new Vector3(1, 1, 1);

    transform.setPosition(position.x, position.y, position.z);
    transform.setRotation(rotation.x, rotation.y, rotation.z);
    transform.setScale(scale.x, scale.y, scale.z);

    // 添加虚拟化身组件
    // 这里应该添加实际的虚拟化身组件
    // entity.addComponent(new AvatarComponent(avatarData));

    // 配置数字人功能
    if (config.digitalHumanConfig?.enableAI) {
      // 添加AI组件
      // entity.addComponent(new AIComponent());
    }

    if (config.digitalHumanConfig?.enableVoice) {
      // 添加语音组件
      // entity.addComponent(new VoiceComponent());
    }

    if (config.digitalHumanConfig?.enableInteraction) {
      // 添加交互组件
      // entity.addComponent(new InteractionComponent());
    }

    if (this.config.debug) {
      console.log(`数字人实体已创建: ${entityName}`);
    }

    return entity;
  }

  /**
   * 集成到场景
   */
  private async integrateToScene(
    digitalHumanEntity: Entity,
    config: UploadConfig
  ): Promise<void> {
    try {
      // 获取场景加载系统
      // const sceneLoader = this.world?.getSystem(AvatarSceneLoader);

      // if (sceneLoader) {
      //   // 检查目标场景是否已加载
      //   const isSceneLoaded = (sceneLoader as any).isSceneLoaded?.(config.targetSceneId);

      //   if (!isSceneLoaded) {
      //     console.warn(`目标场景 ${config.targetSceneId} 未加载，数字人将添加到当前世界`);
      //   }
      // }

      // 将实体添加到世界（场景）
      // 实体已经在createDigitalHuman中通过world.createEntity创建并添加

      // 如果需要自动激活
      if (config.autoActivate) {
        // 激活数字人
        // digitalHumanEntity.setActive(true);
      }

      if (this.config.debug) {
        console.log(`数字人已集成到场景: ${config.targetSceneId}`);
      }

    } catch (error) {
      throw new Error(`场景集成失败: ${error.message}`);
    }
  }

  /**
   * 生成上传ID
   */
  private generateUploadId(fileName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9]/g, '_');
    return `upload_${cleanFileName}_${timestamp}_${random}`;
  }

  /**
   * 记录上传历史
   */
  private recordUploadHistory(sceneId: string, result: UploadResult): void {
    if (!this.uploadHistory.has(sceneId)) {
      this.uploadHistory.set(sceneId, []);
    }

    const history = this.uploadHistory.get(sceneId)!;
    history.push(result);

    // 限制历史记录数量
    if (history.length > 20) {
      history.shift();
    }
  }

  /**
   * 获取上传历史
   */
  public getUploadHistory(sceneId: string): UploadResult[] {
    return this.uploadHistory.get(sceneId) || [];
  }

  /**
   * 获取数字人列表
   */
  public getDigitalHumans(): Array<{
    uploadId: string;
    entity: Entity;
    avatarData: AvatarData;
    config: UploadConfig;
    createdAt: Date;
  }> {
    const digitalHumans: Array<{
      uploadId: string;
      entity: Entity;
      avatarData: AvatarData;
      config: UploadConfig;
      createdAt: Date;
    }> = [];

    for (const [uploadId, info] of this.digitalHumans) {
      digitalHumans.push({
        uploadId,
        entity: info.entity,
        avatarData: info.avatarData,
        config: info.config,
        createdAt: info.createdAt
      });
    }

    return digitalHumans;
  }

  /**
   * 删除数字人
   */
  public async removeDigitalHuman(uploadId: string): Promise<boolean> {
    try {
      const digitalHuman = this.digitalHumans.get(uploadId);
      if (!digitalHuman) {
        return false;
      }

      // 销毁实体
      this.world?.removeEntity(digitalHuman.entity);

      // 从记录中移除
      this.digitalHumans.delete(uploadId);

      this.eventEmitter.emit('digitalHumanRemoved', { uploadId });

      if (this.config.debug) {
        console.log(`数字人已删除: ${uploadId}`);
      }

      return true;

    } catch (error) {
      if (this.config.debug) {
        console.error(`删除数字人失败: ${uploadId}`, error);
      }
      return false;
    }
  }

  /**
   * 获取上传统计信息
   */
  public getUploadStatistics(): {
    totalUploads: number;
    successfulUploads: number;
    failedUploads: number;
    activeDigitalHumans: number;
    averageProcessingTime: number;
    supportedFormats: string[];
  } {
    let totalUploads = 0;
    let successfulUploads = 0;
    let failedUploads = 0;
    let totalProcessingTime = 0;

    for (const history of this.uploadHistory.values()) {
      for (const result of history) {
        totalUploads++;
        totalProcessingTime += result.processingTime;

        if (result.success) {
          successfulUploads++;
        } else {
          failedUploads++;
        }
      }
    }

    return {
      totalUploads,
      successfulUploads,
      failedUploads,
      activeDigitalHumans: this.digitalHumans.size,
      averageProcessingTime: totalUploads > 0 ? totalProcessingTime / totalUploads : 0,
      supportedFormats: this.config.supportedFormats
    };
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener?: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 创建实例
   */
  public createInstance(): AvatarUploadSystem {
    return new AvatarUploadSystem(this.getWorld()!, this.config);
  }

  /**
   * 系统更新
   */
  public update(_deltaTime: number): void {
    // 上传系统通常不需要每帧更新
    // 可以在这里添加定期清理或状态检查逻辑
  }

  /**
   * 系统销毁
   */
  public destroy(): void {
    // 清理所有数字人
    for (const uploadId of this.digitalHumans.keys()) {
      this.removeDigitalHuman(uploadId);
    }

    this.uploadQueue.clear();
    this.processingUploads.clear();
    this.uploadHistory.clear();
    this.digitalHumans.clear();
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('虚拟化身上传系统已销毁');
    }
  }
}
