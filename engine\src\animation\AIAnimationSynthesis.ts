/**
 * AI动画合成系统
 * 用于基于AI技术生成和合成动画
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { AnimationClip, LoopMode } from './AnimationClip';
import { FacialAnimationClip, FacialAnimationKeyframe } from './FacialAnimationEditor';
import { FacialExpressionType, VisemeType } from './FacialAnimation';
import { IAIAnimationModel, AnimationGenerationRequest as AIRequest, AnimationGenerationResult as AIResult } from './ai/index';

/**
 * AI动画合成配置
 */
export interface AIAnimationSynthesisConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 模型URL */
  modelUrl?: string;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 批处理大小 */
  batchSize?: number;
  /** 采样率 */
  sampleRate?: number;
  /** 最大上下文长度 */
  maxContextLength?: number;
  /** 缓存大小限制 */
  maxCacheSize?: number;
  /** 缓存过期时间（毫秒） */
  cacheExpireTime?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否启用进度报告 */
  enableProgressReporting?: boolean;
}

/**
 * 动画生成请求
 */
export interface AnimationGenerationRequest {
  /** 请求ID */
  id: string;
  /** 提示文本 */
  prompt: string;
  /** 动画类型 */
  type: 'body' | 'facial' | 'combined';
  /** 持续时间（秒） */
  duration: number;
  /** 是否循环 */
  loop: boolean;
  /** 参考动画 */
  referenceClip?: AnimationClip | FacialAnimationClip;
  /** 风格 */
  style?: string;
  /** 强度 */
  intensity?: number;
  /** 随机种子 */
  seed?: number;
  /** 用户数据 */
  userData?: any;
}

/**
 * 动画生成结果
 */
export interface AnimationGenerationResult {
  /** 请求ID */
  id: string;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 生成的动画片段 */
  clip?: AnimationClip | FacialAnimationClip;
  /** 生成时间（毫秒） */
  generationTime?: number;
  /** 用户数据 */
  userData?: any;
}

/**
 * 缓存项
 */
interface CacheItem {
  result: AnimationGenerationResult;
  timestamp: number;
}

/**
 * AI动画合成组件
 */
export class AIAnimationSynthesisComponent extends Component {
  /** 组件类型 */
  static readonly type = 'AIAnimationSynthesis';

  /** 请求队列 */
  private requestQueue: AnimationGenerationRequest[] = [];
  /** 批处理队列 */
  private batchQueue: AnimationGenerationRequest[] = [];
  /** 结果缓存 */
  private resultCache: Map<string, CacheItem> = new Map();
  /** 是否正在处理 */
  private isProcessing: boolean = false;
  /** 是否正在批处理 */
  private isBatchProcessing: boolean = false;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 模型是否已加载 */
  private modelLoaded: boolean = false;
  /** 配置 */
  private componentConfig: AIAnimationSynthesisConfig;
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AIAnimationSynthesisConfig = {
    debug: false,
    useLocalModel: true,
    batchSize: 4,
    sampleRate: 30,
    maxContextLength: 1024,
    maxCacheSize: 100,
    cacheExpireTime: 300000, // 5分钟
    maxRetries: 3,
    retryDelay: 1000,
    enableProgressReporting: true
  };

  /** AI模型 */
  private aiModel: IAIAnimationModel | null = null;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config?: Partial<AIAnimationSynthesisConfig>) {
    super(AIAnimationSynthesisComponent.type);
    this.setEntity(entity);
    this.componentConfig = { ...AIAnimationSynthesisComponent.DEFAULT_CONFIG, ...config };
  }

  /**
   * 创建组件实例
   * @returns 组件实例
   */
  public createInstance(): AIAnimationSynthesisComponent {
    return new AIAnimationSynthesisComponent(this.entity!, this.componentConfig);
  }

  /**
   * 请求生成动画
   * @param request 生成请求
   * @returns 请求ID
   */
  public requestAnimation(request: Omit<AnimationGenerationRequest, 'id'>): string {
    // 生成请求ID
    const id = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // 创建完整请求
    const fullRequest: AnimationGenerationRequest = {
      id,
      ...request
    };

    // 检查是否启用批处理
    if (this.componentConfig.batchSize! > 1) {
      this.batchQueue.push(fullRequest);

      // 如果批处理队列满了，开始处理
      if (this.batchQueue.length >= this.componentConfig.batchSize!) {
        this.processBatch();
      }
    } else {
      // 添加到普通队列
      this.requestQueue.push(fullRequest);
    }

    // 触发事件
    this.eventEmitter.emit('requestAdded', { request: fullRequest });

    return id;
  }

  /**
   * 取消请求
   * @param id 请求ID
   * @returns 是否成功取消
   */
  public cancelRequest(id: string): boolean {
    const initialLength = this.requestQueue.length;
    this.requestQueue = this.requestQueue.filter(req => req.id !== id);

    const canceled = this.requestQueue.length < initialLength;
    if (canceled) {
      this.eventEmitter.emit('requestCanceled', { id });
    }

    return canceled;
  }

  /**
   * 获取请求结果
   * @param id 请求ID
   * @returns 生成结果，如果不存在则返回null
   */
  public getResult(id: string): AnimationGenerationResult | null {
    const cacheItem = this.resultCache.get(id);
    if (!cacheItem) return null;

    // 检查缓存是否过期
    if (Date.now() - cacheItem.timestamp > this.componentConfig.cacheExpireTime!) {
      this.resultCache.delete(id);
      return null;
    }

    return cacheItem.result;
  }

  /**
   * 清除结果缓存
   * @param id 请求ID，如果不提供则清除所有缓存
   */
  public clearCache(id?: string): void {
    if (id) {
      this.resultCache.delete(id);
    } else {
      this.resultCache.clear();
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [id, cacheItem] of this.resultCache.entries()) {
      if (now - cacheItem.timestamp > this.componentConfig.cacheExpireTime!) {
        this.resultCache.delete(id);
      }
    }
  }

  /**
   * 检查缓存大小并清理
   */
  private manageCacheSize(): void {
    if (this.resultCache.size > this.componentConfig.maxCacheSize!) {
      // 删除最旧的缓存项
      const entries = Array.from(this.resultCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toDelete = entries.slice(0, entries.length - this.componentConfig.maxCacheSize!);
      for (const [id] of toDelete) {
        this.resultCache.delete(id);
      }
    }
  }

  /**
   * 设置缓存结果
   * @param id 请求ID
   * @param result 结果
   */
  private setCacheResult(id: string, result: AnimationGenerationResult): void {
    const cacheItem: CacheItem = {
      result,
      timestamp: Date.now()
    };

    this.resultCache.set(id, cacheItem);
    this.manageCacheSize();
  }

  /**
   * 批处理请求
   */
  private async processBatch(): Promise<void> {
    if (this.isBatchProcessing || this.batchQueue.length === 0) return;

    this.isBatchProcessing = true;
    const batch = this.batchQueue.splice(0, this.componentConfig.batchSize!);

    try {
      // 并行处理批次中的所有请求
      const promises = batch.map(request => this.processRequest(request));
      await Promise.all(promises);
    } catch (error) {
      if (this.componentConfig.debug) {
        console.error('批处理失败:', error);
      }
    }

    this.isBatchProcessing = false;

    // 如果还有待处理的批次，继续处理
    if (this.batchQueue.length >= this.componentConfig.batchSize!) {
      this.processBatch();
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 处理单个请求
   * @param request 请求
   * @param retryCount 重试次数
   */
  private async processRequest(request: AnimationGenerationRequest, retryCount: number = 0): Promise<void> {
    try {
      // 处理请求
      const startTime = Date.now();

      // 报告进度
      if (this.componentConfig.enableProgressReporting) {
        this.eventEmitter.emit('generationProgress', {
          id: request.id,
          progress: 0.1,
          stage: 'starting'
        });
      }

      // 根据请求类型生成动画
      let clip: AnimationClip | FacialAnimationClip | undefined;

      switch (request.type) {
        case 'body':
          clip = await this.generateBodyAnimation(request);
          break;
        case 'facial':
          clip = await this.generateFacialAnimation(request);
          break;
        case 'combined':
          clip = await this.generateCombinedAnimation(request);
          break;
      }

      const endTime = Date.now();

      // 报告完成进度
      if (this.componentConfig.enableProgressReporting) {
        this.eventEmitter.emit('generationProgress', {
          id: request.id,
          progress: 1.0,
          stage: 'completed'
        });
      }

      // 创建结果
      const result: AnimationGenerationResult = {
        id: request.id,
        success: !!clip,
        clip,
        generationTime: endTime - startTime,
        userData: request.userData
      };

      // 缓存结果
      this.setCacheResult(request.id, result);

      // 触发事件
      this.eventEmitter.emit('generationComplete', { result });

    } catch (error) {
      // 检查是否需要重试
      if (retryCount < this.componentConfig.maxRetries!) {
        if (this.componentConfig.debug) {
          console.warn(`请求 ${request.id} 失败，正在重试 (${retryCount + 1}/${this.componentConfig.maxRetries})...`);
        }

        // 延迟后重试
        setTimeout(() => {
          this.processRequest(request, retryCount + 1);
        }, this.componentConfig.retryDelay!);
        return;
      }

      // 创建错误结果
      const result: AnimationGenerationResult = {
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userData: request.userData
      };

      // 缓存结果
      this.setCacheResult(request.id, result);

      // 触发事件
      this.eventEmitter.emit('generationError', { result });
    }
  }

  /**
   * 处理请求队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.requestQueue.length === 0) return;

    this.isProcessing = true;

    // 获取下一个请求
    const request = this.requestQueue.shift()!;
    await this.processRequest(request);

    this.isProcessing = false;

    // 继续处理队列
    if (this.requestQueue.length > 0) {
      this.processQueue();
    }
  }

  /**
   * 设置AI模型
   * @param model AI模型
   */
  public setAIModel(model: IAIAnimationModel): void {
    this.aiModel = model;
    this.modelLoaded = true;
  }

  /**
   * 生成身体动画
   * @param request 生成请求
   * @returns 生成的动画片段
   */
  private async generateBodyAnimation(request: AnimationGenerationRequest): Promise<AnimationClip | undefined> {
    // 如果有AI模型，使用AI模型生成
    if (this.aiModel) {
      // 转换请求格式
      const aiRequest: AIRequest = {
        id: request.id,
        prompt: request.prompt,
        type: 'body',
        duration: request.duration,
        loop: request.loop,
        style: request.style,
        intensity: request.intensity,
        seed: request.seed,
        userData: request.userData
      };

      // 调用AI模型生成
      const result = await this.aiModel.generateBodyAnimation(aiRequest);

      // 返回生成的动画片段
      return result.clip as AnimationClip;
    } else {
      // 如果没有AI模型，使用简单的模拟生成
      const clip = new AnimationClip({
        name: request.prompt,
        duration: request.duration,
        loopMode: request.loop ? LoopMode.REPEAT : LoopMode.NONE
      });

      // 模拟AI处理时间
      await new Promise(resolve => setTimeout(resolve, 500));

      return clip;
    }
  }

  /**
   * 生成面部动画
   * @param request 生成请求
   * @returns 生成的面部动画片段
   */
  private async generateFacialAnimation(request: AnimationGenerationRequest): Promise<FacialAnimationClip | undefined> {
    // 如果有AI模型，使用AI模型生成
    if (this.aiModel) {
      // 转换请求格式
      const aiRequest: AIRequest = {
        id: request.id,
        prompt: request.prompt,
        type: 'facial',
        duration: request.duration,
        loop: request.loop,
        style: request.style,
        intensity: request.intensity,
        seed: request.seed,
        userData: request.userData
      };

      // 调用AI模型生成
      const result = await this.aiModel.generateFacialAnimation(aiRequest);

      // 返回生成的动画片段
      return result.clip as FacialAnimationClip;
    } else {
      // 如果没有AI模型，使用简单的模拟生成
      const clip: FacialAnimationClip = {
        name: request.prompt,
        duration: request.duration,
        loop: request.loop,
        keyframes: []
      };

      // 添加一些关键帧
      const frameCount = Math.max(2, Math.floor(request.duration * 5)); // 每秒约5帧

      for (let i = 0; i < frameCount; i++) {
        const time = (i / (frameCount - 1)) * request.duration;
        const expressionValues = Object.values(FacialExpressionType);
        const visemeValues = Object.values(VisemeType);
        const expressionIndex = Math.floor(Math.random() * expressionValues.length);
        const visemeIndex = Math.floor(Math.random() * visemeValues.length);

        const keyframe: FacialAnimationKeyframe = {
          time,
          expression: expressionValues[expressionIndex],
          expressionWeight: Math.random(),
          viseme: visemeValues[visemeIndex],
          visemeWeight: Math.random()
        };

        clip.keyframes.push(keyframe);
      }

      // 模拟AI处理时间
      await new Promise(resolve => setTimeout(resolve, 500));

      return clip;
    }
  }

  /**
   * 生成组合动画
   * @param request 生成请求
   * @returns 生成的动画片段
   */
  private async generateCombinedAnimation(request: AnimationGenerationRequest): Promise<AnimationClip | FacialAnimationClip | undefined> {
    // 如果有AI模型，使用AI模型生成
    if (this.aiModel) {
      // 转换请求格式
      const aiRequest: AIRequest = {
        id: request.id,
        prompt: request.prompt,
        type: 'combined',
        duration: request.duration,
        loop: request.loop,
        style: request.style,
        intensity: request.intensity,
        seed: request.seed,
        userData: request.userData
      };

      // 调用AI模型生成
      const result = await this.aiModel.generateCombinedAnimation(aiRequest);

      // 返回生成的动画片段
      return result.clip;
    } else {
      // 如果没有AI模型，生成身体和面部动画的组合
      const bodyClip = await this.generateBodyAnimation(request);
      const facialClip = await this.generateFacialAnimation(request);

      // 优先返回身体动画，如果没有则返回面部动画
      return bodyClip || facialClip;
    }
  }

  /**
   * 更新组件
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 清理过期缓存
    this.cleanExpiredCache();

    // 如果有请求且未在处理，则开始处理
    if (this.requestQueue.length > 0 && !this.isProcessing && this.modelLoaded) {
      this.processQueue();
    }

    // 检查批处理队列
    if (this.batchQueue.length > 0 && !this.isBatchProcessing && this.modelLoaded) {
      // 如果批处理队列有足够的请求或者等待时间过长，开始处理
      const oldestRequest = this.batchQueue[0];
      const waitTime = Date.now() - parseInt(oldestRequest.id.split('_')[1]);

      if (this.batchQueue.length >= this.componentConfig.batchSize! || waitTime > 5000) {
        this.processBatch();
      }
    }
  }

  /**
   * 销毁组件
   */
  public onDestroy(): void {
    // 清理资源
    this.requestQueue = [];
    this.batchQueue = [];
    this.resultCache.clear();
    this.eventEmitter.removeAllListeners();
    this.aiModel = null;
    this.modelLoaded = false;

    super.onDestroy();
  }
}

// 导出系统类
export { AIAnimationSynthesisSystem } from './AIAnimationSynthesisSystem';
