/**
 * 社交感知系统
 * 
 * 专门处理社交相关的感知，包括关系识别、情感理解、群体动态分析等。
 * 支持复杂的社交推理和预测。
 */

import { EventEmitter } from 'events';
import * as THREE from 'three';

/**
 * 关系类型
 */
export enum RelationshipType {
  FRIEND = 'friend',
  ENEMY = 'enemy',
  NEUTRAL = 'neutral',
  FAMILY = 'family',
  COLLEAGUE = 'colleague',
  STRANGER = 'stranger',
  AUTHORITY = 'authority',
  SUBORDINATE = 'subordinate'
}

/**
 * 社交角色
 */
export enum SocialRole {
  LEADER = 'leader',
  FOLLOWER = 'follower',
  MEDIATOR = 'mediator',
  OBSERVER = 'observer',
  PARTICIPANT = 'participant',
  OUTSIDER = 'outsider'
}

/**
 * 交互类型
 */
export enum InteractionType {
  CONVERSATION = 'conversation',
  COOPERATION = 'cooperation',
  COMPETITION = 'competition',
  CONFLICT = 'conflict',
  NEGOTIATION = 'negotiation',
  TEACHING = 'teaching',
  LEARNING = 'learning',
  PLAY = 'play'
}

/**
 * 社交实体详细信息
 */
export interface SocialEntityDetail {
  id: string;
  name: string;
  type: string;
  position: THREE.Vector3;
  
  // 关系信息
  relationship: RelationshipType;
  relationshipStrength: number; // -1 到 1
  trustLevel: number;           // 0 到 1
  familiarity: number;          // 0 到 1
  
  // 状态信息
  emotionalState: EmotionalState;
  currentActivity: string;
  attentionTarget: string;
  socialRole: SocialRole;
  
  // 能力信息
  communicationStyle: string;
  personalityTraits: PersonalityTraits;
  socialSkills: SocialSkills;
  
  // 历史信息
  interactionHistory: InteractionRecord[];
  lastInteraction: number;
  totalInteractions: number;
}

/**
 * 情感状态
 */
export interface EmotionalState {
  primaryEmotion: string;
  intensity: number;
  valence: number;    // 正负情感倾向
  arousal: number;    // 激活程度
  dominance: number;  // 支配性
  stability: number;  // 情感稳定性
}

/**
 * 性格特征
 */
export interface PersonalityTraits {
  openness: number;        // 开放性
  conscientiousness: number; // 尽责性
  extraversion: number;    // 外向性
  agreeableness: number;   // 宜人性
  neuroticism: number;     // 神经质
}

/**
 * 社交技能
 */
export interface SocialSkills {
  empathy: number;         // 共情能力
  persuasion: number;      // 说服力
  leadership: number;      // 领导力
  cooperation: number;     // 合作能力
  communication: number;   // 沟通能力
  conflictResolution: number; // 冲突解决能力
}

/**
 * 交互记录
 */
export interface InteractionRecord {
  timestamp: number;
  type: InteractionType;
  participants: string[];
  duration: number;
  outcome: string;
  emotionalImpact: number;
  satisfactionLevel: number;
  context: string;
}

/**
 * 群体信息
 */
export interface GroupInfo {
  id: string;
  name: string;
  members: string[];
  leader: string;
  type: string;
  cohesion: number;        // 凝聚力
  productivity: number;    // 生产力
  conflictLevel: number;   // 冲突水平
  communicationPattern: string;
  decisionMakingStyle: string;
  norms: SocialNorm[];
}

/**
 * 社交规范
 */
export interface SocialNorm {
  id: string;
  description: string;
  importance: number;
  compliance: number;
  consequences: string[];
}

/**
 * 社交上下文
 */
export interface SocialContext {
  setting: string;         // 环境设置
  formality: number;       // 正式程度
  privacy: number;         // 隐私程度
  timeConstraints: number; // 时间限制
  culturalContext: string; // 文化背景
  socialExpectations: string[];
}

/**
 * 社交事件
 */
export interface SocialEvent {
  id: string;
  type: string;
  timestamp: number;
  participants: string[];
  location: THREE.Vector3;
  description: string;
  significance: number;
  emotionalImpact: Map<string, number>;
  consequences: string[];
}

/**
 * 社交推理结果
 */
export interface SocialInference {
  type: string;
  confidence: number;
  reasoning: string;
  evidence: any[];
  implications: string[];
  recommendations: string[];
}

/**
 * 社交网络分析结果
 */
export interface SocialNetworkAnalysis {
  centralityScores: Map<string, number>;
  clusteringCoefficient: number;
  networkDensity: number;
  influentialNodes: string[];
  communityStructure: string[][];
  bridgeNodes: string[];
}

/**
 * 情感传播模型
 */
export interface EmotionContagion {
  sourceEntity: string;
  targetEntities: string[];
  emotionType: string;
  intensity: number;
  propagationRate: number;
  decayRate: number;
  timestamp: number;
}

/**
 * 社交学习记录
 */
export interface SocialLearningRecord {
  learnerId: string;
  teacherId: string;
  skillType: string;
  learningMethod: string;
  progress: number;
  effectiveness: number;
  timestamp: number;
}

/**
 * 冲突解决策略
 */
export interface ConflictResolutionStrategy {
  conflictId: string;
  participants: string[];
  conflictType: string;
  severity: number;
  suggestedActions: string[];
  mediator?: string;
  expectedOutcome: string;
  timeline: number;
}

/**
 * 社交影响力评估
 */
export interface SocialInfluenceAssessment {
  entityId: string;
  influenceScore: number;
  influenceType: 'positive' | 'negative' | 'neutral';
  affectedEntities: string[];
  influenceMethods: string[];
  temporalPattern: number[];
}

/**
 * 社交感知系统配置
 */
export interface SocialPerceptionConfig {
  perceptionRange: number;
  memoryDecayRate: number;
  updateFrequency: number;
  enableEmotionContagion: boolean;
  enableSocialLearning: boolean;
  enableConflictDetection: boolean;
  enableInfluenceAnalysis: boolean;
  enableNetworkAnalysis: boolean;
  maxHistorySize: number;
  trustUpdateRate: number;
  relationshipThreshold: number;
}

/**
 * 社交感知系统
 */
export class SocialPerceptionSystem {
  private entities: Map<string, SocialEntityDetail> = new Map();
  private groups: Map<string, GroupInfo> = new Map();
  private interactionHistory: InteractionRecord[] = [];
  private socialEvents: SocialEvent[] = [];
  private eventEmitter = new EventEmitter();

  // 新增功能组件
  private emotionContagions: EmotionContagion[] = [];
  private socialLearningRecords: SocialLearningRecord[] = [];
  private conflictResolutionStrategies: Map<string, ConflictResolutionStrategy> = new Map();
  private socialInfluenceMap: Map<string, SocialInfluenceAssessment> = new Map();
  private networkAnalysisCache: SocialNetworkAnalysis | null = null;
  private lastNetworkAnalysisTime = 0;

  private observerEntityId: string;
  private config: SocialPerceptionConfig;
  private lastUpdateTime = 0;

  // 性能统计
  private stats = {
    totalEntitiesTracked: 0,
    totalInteractions: 0,
    totalGroups: 0,
    averageGroupSize: 0,
    conflictCount: 0,
    cooperationCount: 0,
    emotionContagionEvents: 0,
    socialLearningEvents: 0,
    networkAnalysisCount: 0
  };

  constructor(observerEntityId: string, config?: Partial<SocialPerceptionConfig>) {
    this.observerEntityId = observerEntityId;

    // 默认配置
    this.config = {
      perceptionRange: 50,
      memoryDecayRate: 0.01,
      updateFrequency: 1000,
      enableEmotionContagion: true,
      enableSocialLearning: true,
      enableConflictDetection: true,
      enableInfluenceAnalysis: true,
      enableNetworkAnalysis: true,
      maxHistorySize: 1000,
      trustUpdateRate: 0.1,
      relationshipThreshold: 0.3,
      ...config
    };

    this.initializeStats();
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): void {
    this.stats = {
      totalEntitiesTracked: 0,
      totalInteractions: 0,
      totalGroups: 0,
      averageGroupSize: 0,
      conflictCount: 0,
      cooperationCount: 0,
      emotionContagionEvents: 0,
      socialLearningEvents: 0,
      networkAnalysisCount: 0
    };
  }

  /**
   * 更新社交感知
   */
  public update(deltaTime: number, worldData: any): void {
    const now = Date.now();

    if (now - this.lastUpdateTime < this.config.updateFrequency) {
      return;
    }

    this.lastUpdateTime = now;

    // 感知附近的实体
    this.perceiveNearbyEntities(worldData);

    // 分析群体动态
    this.analyzeGroupDynamics();

    // 更新关系强度
    this.updateRelationships(deltaTime);

    // 检测社交事件
    this.detectSocialEvents();

    // 新增功能处理
    if (this.config.enableEmotionContagion) {
      this.processEmotionContagion(deltaTime);
    }

    if (this.config.enableSocialLearning) {
      this.processSocialLearning();
    }

    if (this.config.enableConflictDetection) {
      this.detectAndResolveConflicts();
    }

    if (this.config.enableInfluenceAnalysis) {
      this.analyzeSocialInfluence();
    }

    if (this.config.enableNetworkAnalysis && now - this.lastNetworkAnalysisTime > 10000) {
      this.performNetworkAnalysis();
      this.lastNetworkAnalysisTime = now;
    }

    // 进行社交推理
    const inferences = this.performSocialInference();

    // 清理历史数据
    this.cleanupHistory();

    // 更新统计
    this.updateStats();

    // 触发事件
    this.eventEmitter.emit('socialPerceptionUpdated', {
      entities: this.entities,
      groups: this.groups,
      inferences,
      stats: this.stats
    });
  }

  /**
   * 感知附近的实体
   */
  private perceiveNearbyEntities(worldData: any): void {
    if (!worldData.entities) return;
    
    const observerPosition = this.getObserverPosition(worldData);
    
    for (const entity of worldData.entities) {
      if (entity.id === this.observerEntityId) continue;
      
      const distance = entity.position.distanceTo(observerPosition);
      
      if (distance <= this.config.perceptionRange) {
        this.updateEntityPerception(entity, distance);
      }
    }
  }

  /**
   * 更新实体感知
   */
  private updateEntityPerception(entity: any, distance: number): void {
    let socialEntity = this.entities.get(entity.id);
    
    if (!socialEntity) {
      socialEntity = this.createNewSocialEntity(entity);
      this.entities.set(entity.id, socialEntity);
    }
    
    // 更新位置和基本信息
    socialEntity.position = entity.position.clone();
    socialEntity.currentActivity = entity.activity || 'unknown';
    socialEntity.attentionTarget = entity.attentionTarget || '';
    
    // 更新情感状态
    if (entity.emotionalState) {
      socialEntity.emotionalState = { ...entity.emotionalState };
    }
    
    // 增加熟悉度
    const familiarityIncrease = (1 - distance / this.config.perceptionRange) * 0.01;
    socialEntity.familiarity = Math.min(1, socialEntity.familiarity + familiarityIncrease);
    
    // 更新最后交互时间
    socialEntity.lastInteraction = Date.now();
  }

  /**
   * 创建新的社交实体
   */
  private createNewSocialEntity(entity: any): SocialEntityDetail {
    return {
      id: entity.id,
      name: entity.name || `Entity_${entity.id}`,
      type: entity.type || 'unknown',
      position: entity.position.clone(),
      
      relationship: RelationshipType.STRANGER,
      relationshipStrength: 0,
      trustLevel: 0.5,
      familiarity: 0,
      
      emotionalState: {
        primaryEmotion: 'neutral',
        intensity: 0.5,
        valence: 0,
        arousal: 0.5,
        dominance: 0.5,
        stability: 0.8
      },
      
      currentActivity: 'unknown',
      attentionTarget: '',
      socialRole: SocialRole.OBSERVER,
      
      communicationStyle: 'neutral',
      personalityTraits: {
        openness: 0.5,
        conscientiousness: 0.5,
        extraversion: 0.5,
        agreeableness: 0.5,
        neuroticism: 0.5
      },
      
      socialSkills: {
        empathy: 0.5,
        persuasion: 0.5,
        leadership: 0.5,
        cooperation: 0.5,
        communication: 0.5,
        conflictResolution: 0.5
      },
      
      interactionHistory: [],
      lastInteraction: Date.now(),
      totalInteractions: 0
    };
  }

  /**
   * 分析群体动态
   */
  private analyzeGroupDynamics(): void {
    // 检测群体形成
    const clusters = this.detectEntityClusters();
    
    for (const cluster of clusters) {
      if (cluster.length >= 2) {
        const groupId = this.generateGroupId(cluster);
        let group = this.groups.get(groupId);
        
        if (!group) {
          group = this.createNewGroup(groupId, cluster);
          this.groups.set(groupId, group);
        } else {
          this.updateGroupInfo(group, cluster);
        }
      }
    }
  }

  /**
   * 检测实体聚类
   */
  private detectEntityClusters(): string[][] {
    const clusters: string[][] = [];
    const processed = new Set<string>();
    const clusterDistance = 10; // 聚类距离阈值
    
    for (const [entityId, entity] of this.entities) {
      if (processed.has(entityId)) continue;
      
      const cluster = [entityId];
      processed.add(entityId);
      
      // 查找附近的实体
      for (const [otherId, otherEntity] of this.entities) {
        if (processed.has(otherId)) continue;
        
        const distance = entity.position.distanceTo(otherEntity.position);
        if (distance <= clusterDistance) {
          cluster.push(otherId);
          processed.add(otherId);
        }
      }
      
      clusters.push(cluster);
    }
    
    return clusters;
  }

  /**
   * 创建新群体
   */
  private createNewGroup(groupId: string, members: string[]): GroupInfo {
    return {
      id: groupId,
      name: `Group_${groupId}`,
      members,
      leader: this.identifyGroupLeader(members),
      type: 'informal',
      cohesion: 0.5,
      productivity: 0.5,
      conflictLevel: 0,
      communicationPattern: 'open',
      decisionMakingStyle: 'consensus',
      norms: []
    };
  }

  /**
   * 识别群体领导者
   */
  private identifyGroupLeader(members: string[]): string {
    let leader = members[0];
    let maxLeadership = 0;
    
    for (const memberId of members) {
      const entity = this.entities.get(memberId);
      if (entity && entity.socialSkills.leadership > maxLeadership) {
        maxLeadership = entity.socialSkills.leadership;
        leader = memberId;
      }
    }
    
    return leader;
  }

  /**
   * 更新群体信息
   */
  private updateGroupInfo(group: GroupInfo, currentMembers: string[]): void {
    group.members = currentMembers;
    
    // 重新计算群体指标
    group.cohesion = this.calculateGroupCohesion(currentMembers);
    group.conflictLevel = this.calculateConflictLevel(currentMembers);
  }

  /**
   * 计算群体凝聚力
   */
  private calculateGroupCohesion(members: string[]): number {
    if (members.length < 2) return 0;
    
    let totalRelationshipStrength = 0;
    let pairCount = 0;
    
    for (let i = 0; i < members.length; i++) {
      for (let j = i + 1; j < members.length; j++) {
        const entity1 = this.entities.get(members[i]);
        const entity2 = this.entities.get(members[j]);
        
        if (entity1 && entity2) {
          totalRelationshipStrength += Math.abs(entity1.relationshipStrength);
          pairCount++;
        }
      }
    }
    
    return pairCount > 0 ? totalRelationshipStrength / pairCount : 0;
  }

  /**
   * 计算冲突水平
   */
  private calculateConflictLevel(members: string[]): number {
    let conflictCount = 0;
    let totalPairs = 0;
    
    for (let i = 0; i < members.length; i++) {
      for (let j = i + 1; j < members.length; j++) {
        const entity1 = this.entities.get(members[i]);
        const entity2 = this.entities.get(members[j]);
        
        if (entity1 && entity2) {
          if (entity1.relationshipStrength < -0.3 || entity2.relationshipStrength < -0.3) {
            conflictCount++;
          }
          totalPairs++;
        }
      }
    }
    
    return totalPairs > 0 ? conflictCount / totalPairs : 0;
  }

  /**
   * 更新关系强度
   */
  private updateRelationships(deltaTime: number): void {
    for (const [, entity] of this.entities) {
      // 关系随时间衰减
      const decayAmount = this.config.memoryDecayRate * deltaTime;
      entity.relationshipStrength *= (1 - decayAmount);
      entity.familiarity *= (1 - decayAmount * 0.5);
      entity.trustLevel *= (1 - decayAmount * 0.3);
    }
  }

  /**
   * 检测社交事件
   */
  private detectSocialEvents(): void {
    // 检测新的交互
    this.detectNewInteractions();
    
    // 检测关系变化
    this.detectRelationshipChanges();
    
    // 检测群体事件
    this.detectGroupEvents();
  }

  /**
   * 检测新交互
   */
  private detectNewInteractions(): void {
    // 简化的交互检测逻辑
    const interactionDistance = 5;
    
    for (const [entityId, entity] of this.entities) {
      for (const [otherId, otherEntity] of this.entities) {
        if (entityId >= otherId) continue;
        
        const distance = entity.position.distanceTo(otherEntity.position);
        
        if (distance <= interactionDistance) {
          this.recordInteraction(entityId, otherId, InteractionType.CONVERSATION);
        }
      }
    }
  }

  /**
   * 记录交互
   */
  private recordInteraction(entity1Id: string, entity2Id: string, type: InteractionType): void {
    const interaction: InteractionRecord = {
      timestamp: Date.now(),
      type,
      participants: [entity1Id, entity2Id],
      duration: 1000, // 1秒
      outcome: 'neutral',
      emotionalImpact: 0,
      satisfactionLevel: 0.5,
      context: 'proximity'
    };
    
    this.interactionHistory.push(interaction);
    
    // 更新实体的交互历史
    const entity1 = this.entities.get(entity1Id);
    const entity2 = this.entities.get(entity2Id);
    
    if (entity1) {
      entity1.interactionHistory.push(interaction);
      entity1.totalInteractions++;
    }
    
    if (entity2) {
      entity2.interactionHistory.push(interaction);
      entity2.totalInteractions++;
    }
  }

  /**
   * 检测关系变化
   */
  private detectRelationshipChanges(): void {
    // 基于交互历史更新关系
    for (const interaction of this.interactionHistory.slice(-10)) { // 最近10次交互
      for (const participantId of interaction.participants) {
        const entity = this.entities.get(participantId);
        if (entity) {
          // 根据交互类型调整关系强度
          const adjustment = this.calculateRelationshipAdjustment(interaction);
          entity.relationshipStrength += adjustment;
          entity.relationshipStrength = Math.max(-1, Math.min(1, entity.relationshipStrength));
        }
      }
    }
  }

  /**
   * 计算关系调整值
   */
  private calculateRelationshipAdjustment(interaction: InteractionRecord): number {
    switch (interaction.type) {
      case InteractionType.COOPERATION:
        return 0.1;
      case InteractionType.CONFLICT:
        return -0.2;
      case InteractionType.CONVERSATION:
        return 0.05;
      default:
        return 0;
    }
  }

  /**
   * 检测群体事件
   */
  private detectGroupEvents(): void {
    // 检测群体形成、解散、冲突等事件
    for (const [groupId, group] of this.groups) {
      // 检测群体冲突
      if (group.conflictLevel > 0.7) {
        this.createSocialEvent('group_conflict', [groupId], 'high_conflict_detected');
      }

      // 检测群体解散
      if (group.members.length < 2) {
        this.createSocialEvent('group_dissolution', [groupId], 'group_disbanded');
        this.groups.delete(groupId);
      }

      // 检测新领导者出现
      const currentLeader = this.identifyGroupLeader(group.members);
      if (currentLeader !== group.leader) {
        this.createSocialEvent('leadership_change', [group.leader, currentLeader], 'new_leader_emerged');
        group.leader = currentLeader;
      }
    }
  }

  /**
   * 创建社交事件
   */
  private createSocialEvent(type: string, participants: string[], description: string): void {
    const event: SocialEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      timestamp: Date.now(),
      participants,
      location: new THREE.Vector3(0, 0, 0), // 可以根据参与者位置计算
      description,
      significance: this.calculateEventSignificance(type),
      emotionalImpact: new Map(),
      consequences: []
    };

    this.socialEvents.push(event);

    // 限制事件历史大小
    if (this.socialEvents.length > this.config.maxHistorySize) {
      this.socialEvents.shift();
    }
  }

  /**
   * 计算事件重要性
   */
  private calculateEventSignificance(eventType: string): number {
    const significanceMap: { [key: string]: number } = {
      'group_conflict': 0.8,
      'group_dissolution': 0.7,
      'leadership_change': 0.6,
      'cooperation_success': 0.5,
      'new_relationship': 0.4,
      'interaction': 0.2
    };

    return significanceMap[eventType] || 0.3;
  }

  /**
   * 进行社交推理
   */
  private performSocialInference(): SocialInference[] {
    const inferences: SocialInference[] = [];
    
    // 推理关系发展趋势
    inferences.push(...this.inferRelationshipTrends());
    
    // 推理群体动态
    inferences.push(...this.inferGroupDynamics());
    
    // 推理社交机会
    inferences.push(...this.inferSocialOpportunities());
    
    return inferences;
  }

  /**
   * 推理关系发展趋势
   */
  private inferRelationshipTrends(): SocialInference[] {
    const inferences: SocialInference[] = [];
    
    for (const [entityId, entity] of this.entities) {
      if (entity.totalInteractions > 5) {
        const recentInteractions = entity.interactionHistory.slice(-5);
        const trend = this.calculateRelationshipTrend(recentInteractions);
        
        if (Math.abs(trend) > 0.1) {
          inferences.push({
            type: 'relationship_trend',
            confidence: 0.7,
            reasoning: `与 ${entityId} 的关系呈现${trend > 0 ? '改善' : '恶化'}趋势`,
            evidence: recentInteractions,
            implications: [trend > 0 ? '可能建立更深层次的合作' : '需要注意潜在冲突'],
            recommendations: [trend > 0 ? '继续保持积极互动' : '考虑修复关系的策略']
          });
        }
      }
    }
    
    return inferences;
  }

  /**
   * 计算关系趋势
   */
  private calculateRelationshipTrend(interactions: InteractionRecord[]): number {
    if (interactions.length < 2) return 0;
    
    let trend = 0;
    for (let i = 1; i < interactions.length; i++) {
      const current = this.getInteractionValue(interactions[i]);
      const previous = this.getInteractionValue(interactions[i - 1]);
      trend += current - previous;
    }
    
    return trend / (interactions.length - 1);
  }

  /**
   * 获取交互价值
   */
  private getInteractionValue(interaction: InteractionRecord): number {
    switch (interaction.type) {
      case InteractionType.COOPERATION:
        return 1;
      case InteractionType.CONFLICT:
        return -1;
      case InteractionType.CONVERSATION:
        return 0.5;
      default:
        return 0;
    }
  }

  /**
   * 推理群体动态
   */
  private inferGroupDynamics(): SocialInference[] {
    const inferences: SocialInference[] = [];
    
    for (const [groupId, group] of this.groups) {
      if (group.conflictLevel > 0.5) {
        inferences.push({
          type: 'group_conflict',
          confidence: 0.8,
          reasoning: `群体 ${groupId} 存在高冲突水平`,
          evidence: [group],
          implications: ['群体效率可能下降', '可能导致群体分裂'],
          recommendations: ['介入调解冲突', '重新分配角色']
        });
      }
      
      if (group.cohesion > 0.8) {
        inferences.push({
          type: 'strong_group_cohesion',
          confidence: 0.9,
          reasoning: `群体 ${groupId} 具有很强的凝聚力`,
          evidence: [group],
          implications: ['群体合作效率高', '决策一致性强'],
          recommendations: ['利用群体优势完成复杂任务']
        });
      }
    }
    
    return inferences;
  }

  /**
   * 推理社交机会
   */
  private inferSocialOpportunities(): SocialInference[] {
    const inferences: SocialInference[] = [];
    
    // 识别潜在的合作机会
    for (const [entityId, entity] of this.entities) {
      if (entity.relationshipStrength > 0.5 && entity.familiarity > 0.7) {
        inferences.push({
          type: 'cooperation_opportunity',
          confidence: 0.6,
          reasoning: `与 ${entityId} 存在良好的合作基础`,
          evidence: [entity],
          implications: ['可以开展深度合作', '建立长期伙伴关系'],
          recommendations: ['提出合作提案', '分享资源和信息']
        });
      }
    }
    
    return inferences;
  }

  /**
   * 获取观察者位置
   */
  private getObserverPosition(worldData: any): THREE.Vector3 {
    const observer = worldData.entities?.find((e: any) => e.id === this.observerEntityId);
    return observer?.position || new THREE.Vector3(0, 0, 0);
  }

  /**
   * 生成群体ID
   */
  private generateGroupId(members: string[]): string {
    return members.sort().join('_');
  }

  /**
   * 获取社交实体
   */
  public getSocialEntity(entityId: string): SocialEntityDetail | undefined {
    return this.entities.get(entityId);
  }

  /**
   * 获取所有社交实体
   */
  public getAllSocialEntities(): Map<string, SocialEntityDetail> {
    return new Map(this.entities);
  }

  /**
   * 获取群体信息
   */
  public getGroup(groupId: string): GroupInfo | undefined {
    return this.groups.get(groupId);
  }

  /**
   * 获取所有群体
   */
  public getAllGroups(): Map<string, GroupInfo> {
    return new Map(this.groups);
  }

  /**
   * 获取交互历史
   */
  public getInteractionHistory(limit?: number): InteractionRecord[] {
    if (limit) {
      return this.interactionHistory.slice(-limit);
    }
    return [...this.interactionHistory];
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 处理情感传播
   */
  private processEmotionContagion(deltaTime: number): void {
    // 更新现有的情感传播
    this.emotionContagions = this.emotionContagions.filter(contagion => {
      contagion.intensity *= (1 - contagion.decayRate * deltaTime);
      return contagion.intensity > 0.1; // 移除强度过低的传播
    });

    // 检测新的情感传播
    for (const [sourceId, sourceEntity] of this.entities) {
      if (sourceEntity.emotionalState.intensity > 0.7) {
        const nearbyEntities = this.findNearbyEntities(sourceId, 15);

        for (const targetId of nearbyEntities) {
          const targetEntity = this.entities.get(targetId);
          if (targetEntity && sourceEntity.relationshipStrength > 0.3) {
            this.createEmotionContagion(sourceId, targetId, sourceEntity.emotionalState.primaryEmotion);
          }
        }
      }
    }

    this.stats.emotionContagionEvents = this.emotionContagions.length;
  }

  /**
   * 创建情感传播
   */
  private createEmotionContagion(sourceId: string, targetId: string, emotionType: string): void {
    const sourceEntity = this.entities.get(sourceId);
    if (!sourceEntity) return;

    const contagion: EmotionContagion = {
      sourceEntity: sourceId,
      targetEntities: [targetId],
      emotionType,
      intensity: sourceEntity.emotionalState.intensity * 0.7,
      propagationRate: 0.1,
      decayRate: 0.05,
      timestamp: Date.now()
    };

    this.emotionContagions.push(contagion);

    // 应用情感影响
    const targetEntity = this.entities.get(targetId);
    if (targetEntity) {
      targetEntity.emotionalState.primaryEmotion = emotionType;
      targetEntity.emotionalState.intensity = Math.min(1,
        targetEntity.emotionalState.intensity + contagion.intensity * 0.3);
    }
  }

  /**
   * 查找附近实体
   */
  private findNearbyEntities(entityId: string, radius: number): string[] {
    const entity = this.entities.get(entityId);
    if (!entity) return [];

    const nearbyEntities: string[] = [];

    for (const [otherId, otherEntity] of this.entities) {
      if (otherId === entityId) continue;

      const distance = entity.position.distanceTo(otherEntity.position);
      if (distance <= radius) {
        nearbyEntities.push(otherId);
      }
    }

    return nearbyEntities;
  }

  /**
   * 处理社交学习
   */
  private processSocialLearning(): void {
    for (const [learnerId, learnerEntity] of this.entities) {
      // 寻找潜在的教师
      const potentialTeachers = this.findPotentialTeachers(learnerId);

      for (const teacherId of potentialTeachers) {
        const teacherEntity = this.entities.get(teacherId);
        if (teacherEntity && this.canLearnFrom(learnerEntity, teacherEntity)) {
          this.createSocialLearningRecord(learnerId, teacherId);
        }
      }
    }
  }

  /**
   * 寻找潜在教师
   */
  private findPotentialTeachers(learnerId: string): string[] {
    const learnerEntity = this.entities.get(learnerId);
    if (!learnerEntity) return [];

    const teachers: string[] = [];

    for (const [entityId, entity] of this.entities) {
      if (entityId === learnerId) continue;

      // 检查是否在学习范围内
      const distance = learnerEntity.position.distanceTo(entity.position);
      if (distance <= 10 && entity.socialSkills.leadership > learnerEntity.socialSkills.leadership) {
        teachers.push(entityId);
      }
    }

    return teachers;
  }

  /**
   * 检查是否可以学习
   */
  private canLearnFrom(learner: SocialEntityDetail, teacher: SocialEntityDetail): boolean {
    return learner.relationshipStrength > 0.2 &&
           learner.trustLevel > 0.5 &&
           teacher.socialSkills.communication > 0.6;
  }

  /**
   * 创建社交学习记录
   */
  private createSocialLearningRecord(learnerId: string, teacherId: string): void {
    const record: SocialLearningRecord = {
      learnerId,
      teacherId,
      skillType: 'communication', // 简化实现
      learningMethod: 'observation',
      progress: 0.1,
      effectiveness: 0.7,
      timestamp: Date.now()
    };

    this.socialLearningRecords.push(record);

    // 限制记录大小
    if (this.socialLearningRecords.length > this.config.maxHistorySize) {
      this.socialLearningRecords.shift();
    }

    // 应用学习效果
    const learnerEntity = this.entities.get(learnerId);
    if (learnerEntity) {
      learnerEntity.socialSkills.communication = Math.min(1,
        learnerEntity.socialSkills.communication + record.progress);
    }

    this.stats.socialLearningEvents++;
  }

  /**
   * 检测和解决冲突
   */
  private detectAndResolveConflicts(): void {
    const conflicts = this.detectConflicts();

    for (const conflict of conflicts) {
      const strategy = this.generateConflictResolutionStrategy(conflict);
      this.conflictResolutionStrategies.set(conflict.id, strategy);

      // 尝试自动解决简单冲突
      if (strategy.severity < 0.5) {
        this.attemptConflictResolution(strategy);
      }
    }
  }

  /**
   * 检测冲突
   */
  private detectConflicts(): Array<{ id: string; participants: string[]; type: string; severity: number }> {
    const conflicts: Array<{ id: string; participants: string[]; type: string; severity: number }> = [];

    for (const [entityId, entity] of this.entities) {
      for (const [otherId, otherEntity] of this.entities) {
        if (entityId >= otherId) continue;

        if (entity.relationshipStrength < -0.5 && otherEntity.relationshipStrength < -0.5) {
          const distance = entity.position.distanceTo(otherEntity.position);
          if (distance <= 10) { // 冲突发生在近距离
            conflicts.push({
              id: `conflict_${entityId}_${otherId}`,
              participants: [entityId, otherId],
              type: 'interpersonal',
              severity: Math.abs(entity.relationshipStrength + otherEntity.relationshipStrength) / 2
            });
          }
        }
      }
    }

    this.stats.conflictCount = conflicts.length;
    return conflicts;
  }

  /**
   * 生成冲突解决策略
   */
  private generateConflictResolutionStrategy(conflict: { id: string; participants: string[]; type: string; severity: number }): ConflictResolutionStrategy {
    const mediator = this.findBestMediator(conflict.participants);

    return {
      conflictId: conflict.id,
      participants: conflict.participants,
      conflictType: conflict.type,
      severity: conflict.severity,
      suggestedActions: this.getSuggestedActions(conflict.severity),
      mediator,
      expectedOutcome: conflict.severity < 0.5 ? 'resolution' : 'mitigation',
      timeline: conflict.severity * 10000 // 时间线基于严重程度
    };
  }

  /**
   * 寻找最佳调解者
   */
  private findBestMediator(participants: string[]): string | undefined {
    let bestMediator: string | undefined;
    let bestScore = 0;

    for (const [entityId, entity] of this.entities) {
      if (participants.includes(entityId)) continue;

      const score = entity.socialSkills.conflictResolution +
                   entity.socialSkills.empathy +
                   entity.trustLevel;

      if (score > bestScore) {
        bestScore = score;
        bestMediator = entityId;
      }
    }

    return bestMediator;
  }

  /**
   * 获取建议行动
   */
  private getSuggestedActions(severity: number): string[] {
    if (severity < 0.3) {
      return ['direct_communication', 'clarify_misunderstanding'];
    } else if (severity < 0.7) {
      return ['mediated_discussion', 'cooling_off_period', 'find_common_ground'];
    } else {
      return ['formal_mediation', 'separation', 'authority_intervention'];
    }
  }

  /**
   * 尝试冲突解决
   */
  private attemptConflictResolution(strategy: ConflictResolutionStrategy): void {
    // 简化的冲突解决实现
    for (const participantId of strategy.participants) {
      const entity = this.entities.get(participantId);
      if (entity) {
        // 轻微改善关系
        entity.relationshipStrength = Math.min(0, entity.relationshipStrength + 0.1);
      }
    }

    this.createSocialEvent('conflict_resolution', strategy.participants, 'conflict_resolved');
  }

  /**
   * 分析社交影响力
   */
  private analyzeSocialInfluence(): void {
    for (const [entityId, entity] of this.entities) {
      const influenceScore = this.calculateInfluenceScore(entityId);
      const affectedEntities = this.findAffectedEntities(entityId);

      const assessment: SocialInfluenceAssessment = {
        entityId,
        influenceScore,
        influenceType: influenceScore > 0.6 ? 'positive' : influenceScore < 0.3 ? 'negative' : 'neutral',
        affectedEntities,
        influenceMethods: this.identifyInfluenceMethods(entity),
        temporalPattern: this.analyzeInfluencePattern(entityId)
      };

      this.socialInfluenceMap.set(entityId, assessment);
    }
  }

  /**
   * 计算影响力分数
   */
  private calculateInfluenceScore(entityId: string): number {
    const entity = this.entities.get(entityId);
    if (!entity) return 0;

    const leadershipWeight = 0.3;
    const communicationWeight = 0.2;
    const trustWeight = 0.2;
    const relationshipWeight = 0.3;

    return entity.socialSkills.leadership * leadershipWeight +
           entity.socialSkills.communication * communicationWeight +
           entity.trustLevel * trustWeight +
           Math.max(0, entity.relationshipStrength) * relationshipWeight;
  }

  /**
   * 查找受影响的实体
   */
  private findAffectedEntities(entityId: string): string[] {
    const entity = this.entities.get(entityId);
    if (!entity) return [];

    const affected: string[] = [];

    for (const [otherId, otherEntity] of this.entities) {
      if (otherId === entityId) continue;

      const distance = entity.position.distanceTo(otherEntity.position);
      if (distance <= 20 && otherEntity.relationshipStrength > 0.3) {
        affected.push(otherId);
      }
    }

    return affected;
  }

  /**
   * 识别影响方法
   */
  private identifyInfluenceMethods(entity: SocialEntityDetail): string[] {
    const methods: string[] = [];

    if (entity.socialSkills.leadership > 0.7) methods.push('leadership');
    if (entity.socialSkills.persuasion > 0.7) methods.push('persuasion');
    if (entity.socialSkills.empathy > 0.7) methods.push('empathy');
    if (entity.socialSkills.communication > 0.7) methods.push('communication');

    return methods.length > 0 ? methods : ['presence'];
  }

  /**
   * 分析影响模式
   */
  private analyzeInfluencePattern(entityId: string): number[] {
    // 简化实现：返回过去10个时间点的影响力变化
    const pattern: number[] = [];
    const currentScore = this.calculateInfluenceScore(entityId);

    for (let i = 0; i < 10; i++) {
      // 模拟历史数据
      const variation = (Math.random() - 0.5) * 0.2;
      pattern.push(Math.max(0, Math.min(1, currentScore + variation)));
    }

    return pattern;
  }

  /**
   * 执行网络分析
   */
  private performNetworkAnalysis(): void {
    const analysis: SocialNetworkAnalysis = {
      centralityScores: this.calculateCentralityScores(),
      clusteringCoefficient: this.calculateClusteringCoefficient(),
      networkDensity: this.calculateNetworkDensity(),
      influentialNodes: this.identifyInfluentialNodes(),
      communityStructure: this.detectCommunities(),
      bridgeNodes: this.identifyBridgeNodes()
    };

    this.networkAnalysisCache = analysis;
    this.stats.networkAnalysisCount++;
  }

  /**
   * 计算中心性分数
   */
  private calculateCentralityScores(): Map<string, number> {
    const scores = new Map<string, number>();

    for (const [entityId] of this.entities) {
      const connections = this.countConnections(entityId);
      const totalEntities = this.entities.size - 1;
      scores.set(entityId, totalEntities > 0 ? connections / totalEntities : 0);
    }

    return scores;
  }

  /**
   * 计算连接数
   */
  private countConnections(entityId: string): number {
    const entity = this.entities.get(entityId);
    if (!entity) return 0;

    let connections = 0;

    for (const [otherId, otherEntity] of this.entities) {
      if (otherId === entityId) continue;

      if (Math.abs(entity.relationshipStrength) > this.config.relationshipThreshold ||
          Math.abs(otherEntity.relationshipStrength) > this.config.relationshipThreshold) {
        connections++;
      }
    }

    return connections;
  }

  /**
   * 计算聚类系数
   */
  private calculateClusteringCoefficient(): number {
    let totalCoefficient = 0;
    let nodeCount = 0;

    for (const [entityId] of this.entities) {
      const coefficient = this.calculateNodeClusteringCoefficient(entityId);
      if (coefficient >= 0) {
        totalCoefficient += coefficient;
        nodeCount++;
      }
    }

    return nodeCount > 0 ? totalCoefficient / nodeCount : 0;
  }

  /**
   * 计算节点聚类系数
   */
  private calculateNodeClusteringCoefficient(entityId: string): number {
    const neighbors = this.getNeighbors(entityId);
    if (neighbors.length < 2) return 0;

    let triangles = 0;
    const possibleTriangles = neighbors.length * (neighbors.length - 1) / 2;

    for (let i = 0; i < neighbors.length; i++) {
      for (let j = i + 1; j < neighbors.length; j++) {
        if (this.areConnected(neighbors[i], neighbors[j])) {
          triangles++;
        }
      }
    }

    return possibleTriangles > 0 ? triangles / possibleTriangles : 0;
  }

  /**
   * 获取邻居节点
   */
  private getNeighbors(entityId: string): string[] {
    const entity = this.entities.get(entityId);
    if (!entity) return [];

    const neighbors: string[] = [];

    for (const [otherId, otherEntity] of this.entities) {
      if (otherId === entityId) continue;

      if (Math.abs(entity.relationshipStrength) > this.config.relationshipThreshold ||
          Math.abs(otherEntity.relationshipStrength) > this.config.relationshipThreshold) {
        neighbors.push(otherId);
      }
    }

    return neighbors;
  }

  /**
   * 检查两个节点是否连接
   */
  private areConnected(entityId1: string, entityId2: string): boolean {
    const entity1 = this.entities.get(entityId1);
    const entity2 = this.entities.get(entityId2);

    if (!entity1 || !entity2) return false;

    return Math.abs(entity1.relationshipStrength) > this.config.relationshipThreshold ||
           Math.abs(entity2.relationshipStrength) > this.config.relationshipThreshold;
  }

  /**
   * 计算网络密度
   */
  private calculateNetworkDensity(): number {
    const nodeCount = this.entities.size;
    if (nodeCount < 2) return 0;

    let edgeCount = 0;
    const possibleEdges = nodeCount * (nodeCount - 1) / 2;

    for (const [entityId] of this.entities) {
      edgeCount += this.countConnections(entityId);
    }

    edgeCount /= 2; // 避免重复计算

    return possibleEdges > 0 ? edgeCount / possibleEdges : 0;
  }

  /**
   * 识别影响力节点
   */
  private identifyInfluentialNodes(): string[] {
    const centralityScores = this.calculateCentralityScores();
    const sortedNodes = Array.from(centralityScores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, Math.max(1, Math.floor(this.entities.size * 0.2))); // 取前20%

    return sortedNodes.map(([nodeId]) => nodeId);
  }

  /**
   * 检测社区结构
   */
  private detectCommunities(): string[][] {
    // 简化的社区检测算法
    const communities: string[][] = [];
    const visited = new Set<string>();

    for (const [entityId] of this.entities) {
      if (visited.has(entityId)) continue;

      const community = this.expandCommunity(entityId, visited);
      if (community.length > 1) {
        communities.push(community);
      }
    }

    return communities;
  }

  /**
   * 扩展社区
   */
  private expandCommunity(startEntityId: string, visited: Set<string>): string[] {
    const community = [startEntityId];
    visited.add(startEntityId);

    const neighbors = this.getNeighbors(startEntityId);

    for (const neighborId of neighbors) {
      if (!visited.has(neighborId)) {
        const neighborEntity = this.entities.get(neighborId);
        const startEntity = this.entities.get(startEntityId);

        if (neighborEntity && startEntity &&
            neighborEntity.relationshipStrength > 0.5 &&
            startEntity.relationshipStrength > 0.5) {
          community.push(neighborId);
          visited.add(neighborId);
        }
      }
    }

    return community;
  }

  /**
   * 识别桥接节点
   */
  private identifyBridgeNodes(): string[] {
    const bridgeNodes: string[] = [];

    for (const [entityId] of this.entities) {
      if (this.isBridgeNode(entityId)) {
        bridgeNodes.push(entityId);
      }
    }

    return bridgeNodes;
  }

  /**
   * 检查是否为桥接节点
   */
  private isBridgeNode(entityId: string): boolean {
    const neighbors = this.getNeighbors(entityId);
    if (neighbors.length < 2) return false;

    // 检查移除该节点后是否会分割网络
    const connectedComponents = this.countConnectedComponents(neighbors, entityId);
    return connectedComponents > 1;
  }

  /**
   * 计算连通分量数
   */
  private countConnectedComponents(nodes: string[], excludeNode: string): number {
    const visited = new Set<string>();
    let components = 0;

    for (const nodeId of nodes) {
      if (!visited.has(nodeId)) {
        this.dfsComponent(nodeId, visited, excludeNode);
        components++;
      }
    }

    return components;
  }

  /**
   * 深度优先搜索连通分量
   */
  private dfsComponent(nodeId: string, visited: Set<string>, excludeNode: string): void {
    visited.add(nodeId);

    const neighbors = this.getNeighbors(nodeId);
    for (const neighborId of neighbors) {
      if (!visited.has(neighborId) && neighborId !== excludeNode) {
        this.dfsComponent(neighborId, visited, excludeNode);
      }
    }
  }

  /**
   * 清理历史数据
   */
  private cleanupHistory(): void {
    // 清理交互历史
    if (this.interactionHistory.length > this.config.maxHistorySize) {
      this.interactionHistory = this.interactionHistory.slice(-this.config.maxHistorySize);
    }

    // 清理社交事件
    if (this.socialEvents.length > this.config.maxHistorySize) {
      this.socialEvents = this.socialEvents.slice(-this.config.maxHistorySize);
    }

    // 清理情感传播记录
    if (this.emotionContagions.length > this.config.maxHistorySize) {
      this.emotionContagions = this.emotionContagions.slice(-this.config.maxHistorySize);
    }

    // 清理社交学习记录
    if (this.socialLearningRecords.length > this.config.maxHistorySize) {
      this.socialLearningRecords = this.socialLearningRecords.slice(-this.config.maxHistorySize);
    }
  }

  /**
   * 更新统计数据
   */
  private updateStats(): void {
    this.stats.totalEntitiesTracked = this.entities.size;
    this.stats.totalInteractions = this.interactionHistory.length;
    this.stats.totalGroups = this.groups.size;

    // 计算平均群体大小
    let totalMembers = 0;
    for (const group of this.groups.values()) {
      totalMembers += group.members.length;
    }
    this.stats.averageGroupSize = this.groups.size > 0 ? totalMembers / this.groups.size : 0;

    // 统计合作次数
    this.stats.cooperationCount = this.interactionHistory.filter(
      interaction => interaction.type === InteractionType.COOPERATION
    ).length;
  }

  /**
   * 获取统计数据
   */
  public getStats(): any {
    return { ...this.stats };
  }

  /**
   * 获取网络分析结果
   */
  public getNetworkAnalysis(): SocialNetworkAnalysis | null {
    return this.networkAnalysisCache;
  }

  /**
   * 获取情感传播数据
   */
  public getEmotionContagions(): EmotionContagion[] {
    return [...this.emotionContagions];
  }

  /**
   * 获取社交学习记录
   */
  public getSocialLearningRecords(): SocialLearningRecord[] {
    return [...this.socialLearningRecords];
  }

  /**
   * 获取冲突解决策略
   */
  public getConflictResolutionStrategies(): Map<string, ConflictResolutionStrategy> {
    return new Map(this.conflictResolutionStrategies);
  }

  /**
   * 获取社交影响力评估
   */
  public getSocialInfluenceAssessments(): Map<string, SocialInfluenceAssessment> {
    return new Map(this.socialInfluenceMap);
  }

  /**
   * 获取社交事件
   */
  public getSocialEvents(limit?: number): SocialEvent[] {
    if (limit) {
      return this.socialEvents.slice(-limit);
    }
    return [...this.socialEvents];
  }

  /**
   * 设置配置
   */
  public updateConfig(newConfig: Partial<SocialPerceptionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取配置
   */
  public getConfig(): SocialPerceptionConfig {
    return { ...this.config };
  }
}
