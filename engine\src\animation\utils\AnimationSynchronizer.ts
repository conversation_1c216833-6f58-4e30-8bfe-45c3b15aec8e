/**
 * 动画同步器
 * 用于多客户端动画同步和网络协调
 */
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 同步状态
 */
export enum SyncState {
  /** 未连接 */
  DISCONNECTED = 'disconnected',
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 同步中 */
  SYNCING = 'syncing',
  /** 已同步 */
  SYNCHRONIZED = 'synchronized',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 同步消息类型
 */
export enum SyncMessageType {
  /** 动画开始 */
  ANIMATION_START = 'animationStart',
  /** 动画停止 */
  ANIMATION_STOP = 'animationStop',
  /** 动画暂停 */
  ANIMATION_PAUSE = 'animationPause',
  /** 动画恢复 */
  ANIMATION_RESUME = 'animationResume',
  /** 时间同步 */
  TIME_SYNC = 'timeSync',
  /** 状态同步 */
  STATE_SYNC = 'stateSync',
  /** 心跳 */
  HEARTBEAT = 'heartbeat',
  /** 客户端加入 */
  CLIENT_JOIN = 'clientJoin',
  /** 客户端离开 */
  CLIENT_LEAVE = 'clientLeave'
}

/**
 * 同步消息
 */
export interface SyncMessage {
  /** 消息类型 */
  type: SyncMessageType;
  /** 客户端ID */
  clientId: string;
  /** 时间戳 */
  timestamp: number;
  /** 动画名称 */
  animationName?: string;
  /** 动画时间 */
  animationTime?: number;
  /** 数据 */
  data?: any;
}

/**
 * 客户端信息
 */
export interface ClientInfo {
  /** 客户端ID */
  id: string;
  /** 延迟（毫秒） */
  latency: number;
  /** 最后心跳时间 */
  lastHeartbeat: number;
  /** 是否在线 */
  online: boolean;
  /** 客户端数据 */
  data?: any;
}

/**
 * 同步配置
 */
export interface SynchronizerConfig {
  /** 服务器URL */
  serverUrl?: string;
  /** 客户端ID */
  clientId?: string;
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number;
  /** 连接超时（毫秒） */
  connectionTimeout?: number;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连延迟（毫秒） */
  reconnectDelay?: number;
  /** 时间同步间隔（毫秒） */
  timeSyncInterval?: number;
  /** 延迟补偿阈值（毫秒） */
  latencyThreshold?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 动画同步器
 */
export class AnimationSynchronizer extends EventEmitter {
  /** 配置 */
  private config: SynchronizerConfig;
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: SynchronizerConfig = {
    heartbeatInterval: 5000,
    connectionTimeout: 10000,
    maxReconnectAttempts: 5,
    reconnectDelay: 2000,
    timeSyncInterval: 30000,
    latencyThreshold: 100,
    debug: false
  };

  /** 当前状态 */
  private state: SyncState = SyncState.DISCONNECTED;
  /** WebSocket连接 */
  private socket: WebSocket | null = null;
  /** 客户端列表 */
  private clients: Map<string, ClientInfo> = new Map();
  /** 心跳定时器 */
  private heartbeatTimer: NodeJS.Timeout | null = null;
  /** 时间同步定时器 */
  private timeSyncTimer: NodeJS.Timeout | null = null;
  /** 重连计数 */
  private reconnectCount: number = 0;
  /** 服务器时间偏移 */
  private serverTimeOffset: number = 0;
  /** 本地时间基准 */
  private localTimeBase: number = 0;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<SynchronizerConfig>) {
    super();
    this.config = { ...AnimationSynchronizer.DEFAULT_CONFIG, ...config };
    
    if (!this.config.clientId) {
      this.config.clientId = this.generateClientId();
    }
  }

  /**
   * 连接到服务器
   * @param serverUrl 服务器URL
   */
  public connect(serverUrl?: string): void {
    if (this.state === SyncState.CONNECTED || this.state === SyncState.CONNECTING) {
      return;
    }

    const url = serverUrl || this.config.serverUrl;
    if (!url) {
      throw new Error('服务器URL未指定');
    }

    this.state = SyncState.CONNECTING;
    this.emit('stateChange', this.state);

    try {
      this.socket = new WebSocket(url);
      this.setupSocketHandlers();

      // 连接超时处理
      setTimeout(() => {
        if (this.state === SyncState.CONNECTING) {
          this.handleConnectionError(new Error('连接超时'));
        }
      }, this.config.connectionTimeout!);

    } catch (error) {
      this.handleConnectionError(error as Error);
    }
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    this.state = SyncState.DISCONNECTED;
    this.emit('stateChange', this.state);

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    this.stopHeartbeat();
    this.stopTimeSync();
    this.clients.clear();
    this.reconnectCount = 0;

    if (this.config.debug) {
      console.log('已断开连接');
    }
  }

  /**
   * 发送动画开始消息
   * @param animationName 动画名称
   * @param startTime 开始时间
   */
  public sendAnimationStart(animationName: string, startTime?: number): void {
    const message: SyncMessage = {
      type: SyncMessageType.ANIMATION_START,
      clientId: this.config.clientId!,
      timestamp: this.getServerTime(),
      animationName,
      animationTime: startTime || 0
    };

    this.sendMessage(message);
  }

  /**
   * 发送动画停止消息
   * @param animationName 动画名称
   */
  public sendAnimationStop(animationName: string): void {
    const message: SyncMessage = {
      type: SyncMessageType.ANIMATION_STOP,
      clientId: this.config.clientId!,
      timestamp: this.getServerTime(),
      animationName
    };

    this.sendMessage(message);
  }

  /**
   * 发送动画暂停消息
   * @param animationName 动画名称
   * @param pauseTime 暂停时间
   */
  public sendAnimationPause(animationName: string, pauseTime: number): void {
    const message: SyncMessage = {
      type: SyncMessageType.ANIMATION_PAUSE,
      clientId: this.config.clientId!,
      timestamp: this.getServerTime(),
      animationName,
      animationTime: pauseTime
    };

    this.sendMessage(message);
  }

  /**
   * 发送动画恢复消息
   * @param animationName 动画名称
   * @param resumeTime 恢复时间
   */
  public sendAnimationResume(animationName: string, resumeTime: number): void {
    const message: SyncMessage = {
      type: SyncMessageType.ANIMATION_RESUME,
      clientId: this.config.clientId!,
      timestamp: this.getServerTime(),
      animationName,
      animationTime: resumeTime
    };

    this.sendMessage(message);
  }

  /**
   * 获取当前状态
   * @returns 同步状态
   */
  public getState(): SyncState {
    return this.state;
  }

  /**
   * 获取客户端列表
   * @returns 客户端信息数组
   */
  public getClients(): ClientInfo[] {
    return Array.from(this.clients.values());
  }

  /**
   * 获取服务器时间
   * @returns 服务器时间戳
   */
  public getServerTime(): number {
    return Date.now() + this.serverTimeOffset;
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupSocketHandlers(): void {
    if (!this.socket) return;

    this.socket.onopen = () => {
      this.state = SyncState.CONNECTED;
      this.emit('stateChange', this.state);
      this.reconnectCount = 0;

      // 发送客户端加入消息
      this.sendMessage({
        type: SyncMessageType.CLIENT_JOIN,
        clientId: this.config.clientId!,
        timestamp: Date.now()
      });

      // 启动心跳和时间同步
      this.startHeartbeat();
      this.startTimeSync();

      if (this.config.debug) {
        console.log('已连接到服务器');
      }
    };

    this.socket.onmessage = (event) => {
      try {
        const message: SyncMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        if (this.config.debug) {
          console.error('解析消息失败:', error);
        }
      }
    };

    this.socket.onclose = () => {
      if (this.state !== SyncState.DISCONNECTED) {
        this.handleConnectionError(new Error('连接已关闭'));
      }
    };

    this.socket.onerror = (error) => {
      this.handleConnectionError(new Error('WebSocket错误'));
    };
  }

  /**
   * 处理消息
   * @param message 同步消息
   */
  private handleMessage(message: SyncMessage): void {
    switch (message.type) {
      case SyncMessageType.ANIMATION_START:
        this.emit('animationStart', {
          clientId: message.clientId,
          animationName: message.animationName,
          startTime: message.animationTime,
          timestamp: message.timestamp
        });
        break;

      case SyncMessageType.ANIMATION_STOP:
        this.emit('animationStop', {
          clientId: message.clientId,
          animationName: message.animationName,
          timestamp: message.timestamp
        });
        break;

      case SyncMessageType.ANIMATION_PAUSE:
        this.emit('animationPause', {
          clientId: message.clientId,
          animationName: message.animationName,
          pauseTime: message.animationTime,
          timestamp: message.timestamp
        });
        break;

      case SyncMessageType.ANIMATION_RESUME:
        this.emit('animationResume', {
          clientId: message.clientId,
          animationName: message.animationName,
          resumeTime: message.animationTime,
          timestamp: message.timestamp
        });
        break;

      case SyncMessageType.TIME_SYNC:
        this.handleTimeSync(message);
        break;

      case SyncMessageType.CLIENT_JOIN:
        this.handleClientJoin(message);
        break;

      case SyncMessageType.CLIENT_LEAVE:
        this.handleClientLeave(message);
        break;

      case SyncMessageType.HEARTBEAT:
        this.handleHeartbeat(message);
        break;
    }
  }

  /**
   * 处理时间同步
   * @param message 同步消息
   */
  private handleTimeSync(message: SyncMessage): void {
    if (message.data && message.data.serverTime) {
      const now = Date.now();
      const roundTripTime = now - message.data.clientTime;
      const serverTime = message.data.serverTime + roundTripTime / 2;
      this.serverTimeOffset = serverTime - now;

      if (this.config.debug) {
        console.log(`时间同步: 偏移=${this.serverTimeOffset}ms, RTT=${roundTripTime}ms`);
      }
    }
  }

  /**
   * 处理客户端加入
   * @param message 同步消息
   */
  private handleClientJoin(message: SyncMessage): void {
    const clientInfo: ClientInfo = {
      id: message.clientId,
      latency: 0,
      lastHeartbeat: message.timestamp,
      online: true,
      data: message.data
    };

    this.clients.set(message.clientId, clientInfo);
    this.emit('clientJoin', clientInfo);

    if (this.config.debug) {
      console.log(`客户端加入: ${message.clientId}`);
    }
  }

  /**
   * 处理客户端离开
   * @param message 同步消息
   */
  private handleClientLeave(message: SyncMessage): void {
    const clientInfo = this.clients.get(message.clientId);
    if (clientInfo) {
      clientInfo.online = false;
      this.emit('clientLeave', clientInfo);
      this.clients.delete(message.clientId);

      if (this.config.debug) {
        console.log(`客户端离开: ${message.clientId}`);
      }
    }
  }

  /**
   * 处理心跳
   * @param message 同步消息
   */
  private handleHeartbeat(message: SyncMessage): void {
    const clientInfo = this.clients.get(message.clientId);
    if (clientInfo) {
      const now = Date.now();
      clientInfo.latency = now - message.timestamp;
      clientInfo.lastHeartbeat = now;
    }
  }

  /**
   * 发送消息
   * @param message 同步消息
   */
  private sendMessage(message: SyncMessage): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      this.sendMessage({
        type: SyncMessageType.HEARTBEAT,
        clientId: this.config.clientId!,
        timestamp: Date.now()
      });
    }, this.config.heartbeatInterval!);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 启动时间同步
   */
  private startTimeSync(): void {
    this.stopTimeSync();
    
    this.timeSyncTimer = setInterval(() => {
      this.sendMessage({
        type: SyncMessageType.TIME_SYNC,
        clientId: this.config.clientId!,
        timestamp: Date.now(),
        data: { clientTime: Date.now() }
      });
    }, this.config.timeSyncInterval!);
  }

  /**
   * 停止时间同步
   */
  private stopTimeSync(): void {
    if (this.timeSyncTimer) {
      clearInterval(this.timeSyncTimer);
      this.timeSyncTimer = null;
    }
  }

  /**
   * 处理连接错误
   * @param error 错误
   */
  private handleConnectionError(error: Error): void {
    this.state = SyncState.ERROR;
    this.emit('stateChange', this.state);
    this.emit('error', error);

    if (this.config.debug) {
      console.error('连接错误:', error);
    }

    // 尝试重连
    if (this.reconnectCount < this.config.maxReconnectAttempts!) {
      this.reconnectCount++;
      
      setTimeout(() => {
        if (this.config.debug) {
          console.log(`尝试重连 (${this.reconnectCount}/${this.config.maxReconnectAttempts})`);
        }
        this.connect();
      }, this.config.reconnectDelay!);
    }
  }

  /**
   * 生成客户端ID
   * @returns 客户端ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<SynchronizerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): SynchronizerConfig {
    return { ...this.config };
  }

  /**
   * 销毁同步器
   */
  public destroy(): void {
    this.disconnect();
    this.removeAllListeners();
  }
}
