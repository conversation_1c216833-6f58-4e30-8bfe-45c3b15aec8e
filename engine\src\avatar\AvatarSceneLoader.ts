/**
 * 虚拟化身场景加载系统
 * 
 * 提供虚拟化身快速加载到虚拟场景的功能
 * 支持场景选择、化身加载、位置设置、动画应用等
 */

import { System } from '../core/System';
import { World } from '../core/World';
import { Entity } from '../core/Entity';
import { Transform } from '../scene/Transform';
import { EventEmitter } from 'events';
import { AvatarData } from './AvatarCustomizationSystem';
import { Vector3 } from '../math/Vector3';

/**
 * 场景加载配置接口
 */
export interface SceneLoadConfig {
  /** 场景ID或路径 */
  sceneId: string;
  /** 虚拟化身数据 */
  avatarData: AvatarData;
  /** 生成位置 */
  spawnPosition?: Vector3;
  /** 生成旋转 */
  spawnRotation?: Vector3;
  /** 生成缩放 */
  spawnScale?: Vector3;
  /** 初始动画 */
  initialAnimation?: string;
  /** 是否自动开始 */
  autoStart?: boolean;
  /** 加载选项 */
  loadOptions?: {
    /** 是否预加载资源 */
    preloadAssets?: boolean;
    /** 是否启用物理 */
    enablePhysics?: boolean;
    /** 是否启用AI */
    enableAI?: boolean;
    /** 是否启用语音 */
    enableVoice?: boolean;
  };
}

/**
 * 场景加载结果接口
 */
export interface SceneLoadResult {
  /** 加载是否成功 */
  success: boolean;
  /** 场景实体 */
  sceneEntity?: Entity;
  /** 虚拟化身实体 */
  avatarEntity?: Entity;
  /** 加载时间（毫秒） */
  loadTime: number;
  /** 错误信息 */
  error?: string;
  /** 加载的资源列表 */
  loadedAssets?: string[];
}

/**
 * 虚拟化身场景加载系统配置
 */
export interface AvatarSceneLoaderConfig {
  /** 默认场景目录 */
  defaultSceneDirectory: string;
  /** 默认生成位置 */
  defaultSpawnPosition: Vector3;
  /** 最大并发加载数 */
  maxConcurrentLoads: number;
  /** 加载超时时间（毫秒） */
  loadTimeout: number;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 虚拟化身场景加载系统
 */
export class AvatarSceneLoader extends System {
  /** 系统名称 */
  public static readonly NAME = 'AvatarSceneLoader';

  /** 配置 */
  private config: AvatarSceneLoaderConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 加载队列 */
  private loadQueue: Map<string, {
    config: SceneLoadConfig;
    resolve: (result: SceneLoadResult) => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = new Map();

  /** 正在加载的任务 */
  private loadingTasks: Set<string> = new Set();

  /** 已加载的场景 */
  private loadedScenes: Map<string, {
    sceneEntity: Entity;
    avatarEntity: Entity;
    loadTime: Date;
  }> = new Map();

  /** 可用场景列表 */
  private availableScenes: Map<string, {
    name: string;
    path: string;
    description: string;
    thumbnail?: string;
    tags: string[];
  }> = new Map();

  /**
   * 构造函数
   */
  constructor(world: World, config: Partial<AvatarSceneLoaderConfig> = {}) {
    super(0);
    this.setWorld(world);

    this.config = {
      defaultSceneDirectory: './scenes',
      defaultSpawnPosition: new Vector3(0, 0, 0),
      maxConcurrentLoads: 3,
      loadTimeout: 30000, // 30秒
      debug: false,
      ...config
    };

    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    // 扫描可用场景
    this.scanAvailableScenes();

    // 启动加载队列处理器
    this.startLoadQueueProcessor();

    if (this.config.debug) {
      console.log('虚拟化身场景加载系统已初始化');
    }
  }

  /**
   * 扫描可用场景
   */
  private scanAvailableScenes(): void {
    // 添加一些默认场景
    this.availableScenes.set('default', {
      name: '默认场景',
      path: 'scenes/default.scene',
      description: '基础的虚拟环境场景',
      tags: ['basic', 'indoor']
    });

    this.availableScenes.set('medical_hall', {
      name: '医疗展厅',
      path: 'scenes/medical_hall.scene',
      description: '医疗健康展示场景',
      tags: ['medical', 'exhibition', 'educational']
    });

    this.availableScenes.set('classroom', {
      name: '虚拟教室',
      path: 'scenes/classroom.scene',
      description: '教育培训场景',
      tags: ['education', 'indoor', 'learning']
    });

    this.availableScenes.set('outdoor_park', {
      name: '户外公园',
      path: 'scenes/outdoor_park.scene',
      description: '自然环境场景',
      tags: ['outdoor', 'nature', 'relaxing']
    });

    if (this.config.debug) {
      console.log(`扫描到 ${this.availableScenes.size} 个可用场景`);
    }
  }

  /**
   * 启动加载队列处理器
   */
  private startLoadQueueProcessor(): void {
    setInterval(() => {
      this.processLoadQueue();
    }, 100); // 每100ms处理一次队列
  }

  /**
   * 处理加载队列
   */
  private async processLoadQueue(): Promise<void> {
    // 检查超时的任务
    const now = Date.now();
    for (const [loadId, task] of this.loadQueue) {
      if (now - task.timestamp > this.config.loadTimeout) {
        this.loadQueue.delete(loadId);
        task.reject(new Error('加载超时'));
      }
    }

    // 处理新的加载任务
    if (this.loadingTasks.size < this.config.maxConcurrentLoads) {
      for (const [loadId, task] of this.loadQueue) {
        if (!this.loadingTasks.has(loadId)) {
          this.loadingTasks.add(loadId);
          this.loadQueue.delete(loadId);

          try {
            const result = await this.performSceneLoad(task.config);
            task.resolve(result);
          } catch (error) {
            task.reject(error);
          } finally {
            this.loadingTasks.delete(loadId);
          }

          // 达到并发限制，退出循环
          if (this.loadingTasks.size >= this.config.maxConcurrentLoads) {
            break;
          }
        }
      }
    }
  }

  /**
   * 加载虚拟化身到场景
   */
  public async loadAvatarToScene(config: SceneLoadConfig): Promise<SceneLoadResult> {
    const loadId = this.generateLoadId(config);

    return new Promise((resolve, reject) => {
      this.loadQueue.set(loadId, {
        config,
        resolve,
        reject,
        timestamp: Date.now()
      });

      this.eventEmitter.emit('loadQueued', { loadId, sceneId: config.sceneId });
    });
  }

  /**
   * 执行场景加载
   */
  private async performSceneLoad(config: SceneLoadConfig): Promise<SceneLoadResult> {
    const startTime = Date.now();

    try {
      // 验证配置
      this.validateLoadConfig(config);

      // 检查场景是否存在
      if (!this.availableScenes.has(config.sceneId)) {
        throw new Error(`场景不存在: ${config.sceneId}`);
      }

      const sceneInfo = this.availableScenes.get(config.sceneId)!;

      // 加载场景
      const sceneEntity = await this.loadScene(sceneInfo.path);

      // 创建虚拟化身实体
      const avatarEntity = await this.createAvatarEntity(config);

      // 设置虚拟化身位置
      this.setupAvatarTransform(avatarEntity, config);

      // 应用初始动画
      if (config.initialAnimation) {
        await this.applyInitialAnimation(avatarEntity, config.initialAnimation);
      }

      // 启用各种功能
      if (config.loadOptions?.enablePhysics) {
        this.enablePhysics(avatarEntity);
      }

      if (config.loadOptions?.enableAI) {
        this.enableAI(avatarEntity);
      }

      if (config.loadOptions?.enableVoice) {
        this.enableVoice(avatarEntity);
      }

      // 记录加载结果
      const loadTime = Date.now() - startTime;
      const result: SceneLoadResult = {
        success: true,
        sceneEntity,
        avatarEntity,
        loadTime,
        loadedAssets: [] // 这里应该记录实际加载的资源
      };

      // 缓存加载结果
      this.loadedScenes.set(config.sceneId, {
        sceneEntity,
        avatarEntity,
        loadTime: new Date()
      });

      // 触发事件
      this.eventEmitter.emit('sceneLoaded', result);

      if (this.config.debug) {
        console.log(`场景 ${config.sceneId} 加载完成，耗时 ${loadTime}ms`);
      }

      return result;

    } catch (error) {
      const loadTime = Date.now() - startTime;
      const errorResult: SceneLoadResult = {
        success: false,
        loadTime,
        error: error.message
      };

      this.eventEmitter.emit('loadFailed', { sceneId: config.sceneId, error });

      if (this.config.debug) {
        console.error(`场景 ${config.sceneId} 加载失败:`, error);
      }

      return errorResult;
    }
  }

  /**
   * 生成加载ID
   */
  private generateLoadId(config: SceneLoadConfig): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${config.sceneId}_${config.avatarData.id}_${timestamp}_${random}`;
  }

  /**
   * 验证加载配置
   */
  private validateLoadConfig(config: SceneLoadConfig): void {
    if (!config.sceneId) {
      throw new Error('场景ID不能为空');
    }

    if (!config.avatarData || !config.avatarData.id) {
      throw new Error('虚拟化身数据无效');
    }
  }

  /**
   * 加载场景
   */
  private async loadScene(scenePath: string): Promise<Entity> {
    // 模拟场景加载过程
    await new Promise(resolve => setTimeout(resolve, 500));

    // 创建场景根实体
    const sceneEntity = this.world.createEntity(`Scene_${Date.now()}`);

    if (this.config.debug) {
      console.log(`场景已加载: ${scenePath}`);
    }

    return sceneEntity;
  }

  /**
   * 创建虚拟化身实体
   */
  private async createAvatarEntity(config: SceneLoadConfig): Promise<Entity> {
    // 模拟虚拟化身创建过程
    await new Promise(resolve => setTimeout(resolve, 200));

    // 创建虚拟化身实体
    const avatarEntity = this.world.createEntity(`Avatar_${config.avatarData.id}`);

    if (this.config.debug) {
      console.log(`虚拟化身实体已创建: ${config.avatarData.id}`);
    }

    return avatarEntity;
  }

  /**
   * 设置虚拟化身变换
   */
  private setupAvatarTransform(avatarEntity: Entity, config: SceneLoadConfig): void {
    const transform = avatarEntity.getTransform();
    
    const position = config.spawnPosition || this.config.defaultSpawnPosition;
    const rotation = config.spawnRotation || new Vector3(0, 0, 0);
    const scale = config.spawnScale || new Vector3(1, 1, 1);

    transform.setPosition(position.x, position.y, position.z);
    transform.setRotation(rotation.x, rotation.y, rotation.z);
    transform.setScale(scale.x, scale.y, scale.z);
  }

  /**
   * 应用初始动画
   */
  private async applyInitialAnimation(avatarEntity: Entity, animationName: string): Promise<void> {
    // 模拟动画应用过程
    await new Promise(resolve => setTimeout(resolve, 100));

    if (this.config.debug) {
      console.log(`初始动画已应用: ${animationName}`);
    }
  }

  /**
   * 启用物理
   */
  private enablePhysics(avatarEntity: Entity): void {
    // 这里应该添加物理组件
    if (this.config.debug) {
      console.log('虚拟化身物理已启用');
    }
  }

  /**
   * 启用AI
   */
  private enableAI(avatarEntity: Entity): void {
    // 这里应该添加AI组件
    if (this.config.debug) {
      console.log('虚拟化身AI已启用');
    }
  }

  /**
   * 启用语音
   */
  private enableVoice(avatarEntity: Entity): void {
    // 这里应该添加语音组件
    if (this.config.debug) {
      console.log('虚拟化身语音已启用');
    }
  }

  /**
   * 获取可用场景列表
   */
  public getAvailableScenes(): Array<{
    id: string;
    name: string;
    description: string;
    tags: string[];
    thumbnail?: string;
  }> {
    const scenes: Array<{
      id: string;
      name: string;
      description: string;
      tags: string[];
      thumbnail?: string;
    }> = [];

    for (const [id, info] of this.availableScenes) {
      scenes.push({
        id,
        name: info.name,
        description: info.description,
        tags: info.tags,
        thumbnail: info.thumbnail
      });
    }

    return scenes;
  }

  /**
   * 获取场景信息
   */
  public getSceneInfo(sceneId: string): {
    name: string;
    path: string;
    description: string;
    tags: string[];
    thumbnail?: string;
  } | null {
    return this.availableScenes.get(sceneId) || null;
  }

  /**
   * 卸载场景
   */
  public async unloadScene(sceneId: string): Promise<boolean> {
    try {
      const loadedScene = this.loadedScenes.get(sceneId);
      if (!loadedScene) {
        return false;
      }

      // 销毁场景实体
      if (loadedScene.sceneEntity) {
        this.world?.removeEntity(loadedScene.sceneEntity);
      }

      // 销毁虚拟化身实体
      if (loadedScene.avatarEntity) {
        this.world?.removeEntity(loadedScene.avatarEntity);
      }

      // 从缓存中移除
      this.loadedScenes.delete(sceneId);

      // 触发事件
      this.eventEmitter.emit('sceneUnloaded', { sceneId });

      if (this.config.debug) {
        console.log(`场景 ${sceneId} 已卸载`);
      }

      return true;

    } catch (error) {
      if (this.config.debug) {
        console.error(`卸载场景失败: ${sceneId}`, error);
      }
      return false;
    }
  }

  /**
   * 获取已加载的场景
   */
  public getLoadedScenes(): string[] {
    return Array.from(this.loadedScenes.keys());
  }

  /**
   * 检查场景是否已加载
   */
  public isSceneLoaded(sceneId: string): boolean {
    return this.loadedScenes.has(sceneId);
  }

  /**
   * 获取虚拟化身实体
   */
  public getAvatarEntity(sceneId: string): Entity | null {
    const loadedScene = this.loadedScenes.get(sceneId);
    return loadedScene?.avatarEntity || null;
  }

  /**
   * 获取场景实体
   */
  public getSceneEntity(sceneId: string): Entity | null {
    const loadedScene = this.loadedScenes.get(sceneId);
    return loadedScene?.sceneEntity || null;
  }

  /**
   * 移动虚拟化身到新位置
   */
  public moveAvatarToPosition(sceneId: string, position: Vector3, rotation?: Vector3): boolean {
    try {
      const avatarEntity = this.getAvatarEntity(sceneId);
      if (!avatarEntity) {
        return false;
      }

      const transform = avatarEntity.getTransform();
      transform.setPosition(position.x, position.y, position.z);

      if (rotation) {
        transform.setRotation(rotation.x, rotation.y, rotation.z);
      }

      this.eventEmitter.emit('avatarMoved', { sceneId, position, rotation });

      if (this.config.debug) {
        console.log(`虚拟化身已移动到新位置: ${position.x}, ${position.y}, ${position.z}`);
      }

      return true;

    } catch (error) {
      if (this.config.debug) {
        console.error('移动虚拟化身失败:', error);
      }
      return false;
    }
  }

  /**
   * 切换场景
   */
  public async switchScene(fromSceneId: string, toSceneId: string, avatarData: AvatarData): Promise<SceneLoadResult> {
    try {
      // 获取当前虚拟化身的位置
      const currentAvatarEntity = this.getAvatarEntity(fromSceneId);
      let currentPosition = this.config.defaultSpawnPosition;

      if (currentAvatarEntity) {
        const transform = currentAvatarEntity.getTransform();
        currentPosition = new Vector3(
          transform.getPosition().x,
          transform.getPosition().y,
          transform.getPosition().z
        );
      }

      // 卸载当前场景
      await this.unloadScene(fromSceneId);

      // 加载新场景
      const loadConfig: SceneLoadConfig = {
        sceneId: toSceneId,
        avatarData,
        spawnPosition: currentPosition,
        autoStart: true
      };

      const result = await this.loadAvatarToScene(loadConfig);

      this.eventEmitter.emit('sceneSwitch', {
        fromSceneId,
        toSceneId,
        success: result.success
      });

      return result;

    } catch (error) {
      if (this.config.debug) {
        console.error('切换场景失败:', error);
      }

      return {
        success: false,
        loadTime: 0,
        error: error.message
      };
    }
  }

  /**
   * 获取加载统计信息
   */
  public getLoadStatistics(): {
    totalLoads: number;
    successfulLoads: number;
    failedLoads: number;
    averageLoadTime: number;
    currentlyLoading: number;
    queuedLoads: number;
  } {
    // 这里应该实现实际的统计逻辑
    return {
      totalLoads: this.loadedScenes.size,
      successfulLoads: this.loadedScenes.size,
      failedLoads: 0,
      averageLoadTime: 500,
      currentlyLoading: this.loadingTasks.size,
      queuedLoads: this.loadQueue.size
    };
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener?: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 创建实例
   */
  public createInstance(): AvatarSceneLoader {
    return new AvatarSceneLoader(this.getWorld()!, this.config);
  }

  /**
   * 系统更新
   */
  public update(deltaTime: number): void {
    // 场景加载系统通常不需要每帧更新
    // 可以在这里添加定期检查或清理逻辑
  }

  /**
   * 系统销毁
   */
  public destroy(): void {
    // 卸载所有场景
    for (const sceneId of this.loadedScenes.keys()) {
      this.unloadScene(sceneId);
    }

    this.loadQueue.clear();
    this.loadingTasks.clear();
    this.loadedScenes.clear();
    this.availableScenes.clear();
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('虚拟化身场景加载系统已销毁');
    }
  }
}
