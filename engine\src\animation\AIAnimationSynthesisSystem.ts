/**
 * AI动画合成系统
 * 用于管理和更新AI动画合成组件
 */
import type { Entity } from '../core/Entity';
import { System } from '../core/System';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { AIAnimationSynthesisComponent, AIAnimationSynthesisConfig, AnimationGenerationRequest, AnimationGenerationResult } from './AIAnimationSynthesis';
import { IAIAnimationModel, LocalAIAnimationModel } from './ai';

/**
 * 系统状态枚举
 */
export enum AISystemState {
  UNINITIALIZED = 'uninitialized',
  INITIALIZING = 'initializing',
  READY = 'ready',
  LOADING_MODEL = 'loading_model',
  ERROR = 'error',
  DISPOSING = 'disposing'
}

/**
 * 性能统计信息
 */
export interface PerformanceStats {
  /** 总请求数 */
  totalRequests: number;
  /** 成功请求数 */
  successfulRequests: number;
  /** 失败请求数 */
  failedRequests: number;
  /** 平均生成时间（毫秒） */
  averageGenerationTime: number;
  /** 当前活跃请求数 */
  activeRequests: number;
  /** 缓存命中率 */
  cacheHitRate: number;
  /** 系统负载 */
  systemLoad: number;
}

/**
 * 模型信息
 */
export interface ModelInfo {
  /** 模型ID */
  id: string;
  /** 模型类型 */
  type: string;
  /** 是否已加载 */
  loaded: boolean;
  /** 加载时间 */
  loadTime?: number;
  /** 模型大小（字节） */
  size?: number;
  /** 支持的功能 */
  capabilities: string[];
}

/**
 * AI动画合成系统
 */
export class AIAnimationSynthesisSystem extends System {
  /** 系统类型 */
  static readonly type = 'AIAnimationSynthesis';

  /** AI动画合成组件 */
  private components: Map<Entity, AIAnimationSynthesisComponent> = new Map();

  /** 配置 */
  private config: AIAnimationSynthesisConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AIAnimationSynthesisConfig = {
    debug: false,
    useLocalModel: true,
    batchSize: 4,
    sampleRate: 30,
    maxContextLength: 1024,
    maxCacheSize: 100,
    cacheExpireTime: 300000,
    maxRetries: 3,
    retryDelay: 1000,
    enableProgressReporting: true
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** AI模型集合 */
  private aiModels: Map<string, IAIAnimationModel> = new Map();

  /** 当前活跃模型 */
  private activeModel: IAIAnimationModel | null = null;

  /** 系统状态 */
  private systemState: AISystemState = AISystemState.UNINITIALIZED;

  /** 模型加载进度 */
  private modelLoadProgress: number = 0;

  /** AI系统性能统计 */
  private aiPerformanceStats: PerformanceStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageGenerationTime: 0,
    activeRequests: 0,
    cacheHitRate: 0,
    systemLoad: 0
  };

  /** 批处理队列 */
  private batchQueue: AnimationGenerationRequest[] = [];

  /** 是否正在批处理 */
  private isBatchProcessing: boolean = false;

  /** 健康检查定时器 */
  private healthCheckTimer: NodeJS.Timeout | null = null;

  /** 配置更新回调 */
  private configUpdateCallbacks: Set<(config: AIAnimationSynthesisConfig) => void> = new Set();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(_world: World, config?: Partial<AIAnimationSynthesisConfig>) {
    super(500); // 设置系统优先级
    this.config = { ...AIAnimationSynthesisSystem.DEFAULT_CONFIG, ...config };
    this.systemState = AISystemState.UNINITIALIZED;
  }

  /**
   * 系统初始化
   */
  protected onInitialize(): void {
    this.systemState = AISystemState.INITIALIZING;

    if (this.config.debug) {
      console.log('AI动画合成系统开始初始化...');
    }

    // 初始化模型
    this.initModel();

    // 启动健康检查
    this.startHealthCheck();

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 初始化AI模型
   */
  private async initModel(): Promise<void> {
    this.systemState = AISystemState.LOADING_MODEL;

    if (this.config.debug) {
      console.log('正在初始化AI模型...');
    }

    try {
      // 创建AI模型
      const modelId = this.config.useLocalModel ? 'local-model' : 'remote-model';
      const model = new LocalAIAnimationModel({
        debug: this.config.debug,
        batchSize: this.config.batchSize
      });

      // 初始化进度报告
      for (let i = 0; i <= 10; i++) {
        this.modelLoadProgress = i / 10;
        this.eventEmitter.emit('modelLoadProgress', { progress: this.modelLoadProgress });

        if (i < 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // 初始化模型
      const success = await model.initialize();

      if (success) {
        // 添加到模型集合
        this.aiModels.set(modelId, model);
        this.activeModel = model;

        this.systemState = AISystemState.READY;
        this.eventEmitter.emit('modelLoaded', { success: true, modelId });

        if (this.config.debug) {
          console.log(`AI模型初始化完成: ${modelId}`);
        }

        // 为已存在的组件设置模型
        this.updateComponentModels();
      } else {
        throw new Error('模型初始化失败');
      }
    } catch (error) {
      this.systemState = AISystemState.ERROR;
      this.activeModel = null;
      this.eventEmitter.emit('modelLoaded', { success: false, error });

      if (this.config.debug) {
        console.error('AI模型初始化失败:', error);
      }
    }
  }

  /**
   * 更新所有组件的模型
   */
  private updateComponentModels(): void {
    if (this.activeModel) {
      for (const component of this.components.values()) {
        component.setAIModel(this.activeModel);
      }
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听组件事件
    this.eventEmitter.on('generationComplete', (data) => {
      this.updateAIPerformanceStats(data.result, true);
    });

    this.eventEmitter.on('generationError', (data) => {
      this.updateAIPerformanceStats(data.result, false);
    });
  }

  /**
   * 更新AI性能统计
   * @param result 生成结果
   * @param success 是否成功
   */
  private updateAIPerformanceStats(result: AnimationGenerationResult, success: boolean): void {
    this.aiPerformanceStats.totalRequests++;

    if (success) {
      this.aiPerformanceStats.successfulRequests++;

      if (result.generationTime) {
        const totalTime = this.aiPerformanceStats.averageGenerationTime * (this.aiPerformanceStats.successfulRequests - 1) + result.generationTime;
        this.aiPerformanceStats.averageGenerationTime = totalTime / this.aiPerformanceStats.successfulRequests;
      }
    } else {
      this.aiPerformanceStats.failedRequests++;
    }

    // 更新系统负载
    this.aiPerformanceStats.systemLoad = this.aiPerformanceStats.activeRequests / Math.max(1, this.config.batchSize || 1);
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // 每30秒检查一次
  }

  /**
   * 执行健康检查
   */
  private performHealthCheck(): void {
    const isHealthy = this.systemState === AISystemState.READY &&
                     this.activeModel !== null &&
                     this.aiPerformanceStats.systemLoad < 0.9;

    this.eventEmitter.emit('healthCheck', {
      isHealthy,
      state: this.systemState,
      stats: this.aiPerformanceStats,
      timestamp: Date.now()
    });

    if (!isHealthy && this.config.debug) {
      console.warn('AI动画合成系统健康检查失败', {
        state: this.systemState,
        hasActiveModel: this.activeModel !== null,
        systemLoad: this.aiPerformanceStats.systemLoad
      });
    }
  }

  /**
   * 创建AI动画合成组件
   * @param entity 实体
   * @returns AI动画合成组件
   */
  public createAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new AIAnimationSynthesisComponent(entity, this.config);
    this.components.set(entity, component);

    // 如果AI模型已加载，设置AI模型
    if (this.systemState === AISystemState.READY && this.activeModel) {
      component.setAIModel(this.activeModel);
    }

    // 设置组件事件监听
    component.addEventListener('generationComplete', (data) => {
      this.eventEmitter.emit('generationComplete', data);
    });

    component.addEventListener('generationError', (data) => {
      this.eventEmitter.emit('generationError', data);
    });

    component.addEventListener('generationProgress', (data) => {
      this.eventEmitter.emit('generationProgress', data);
    });

    if (this.config.debug) {
      console.log(`创建AI动画合成组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除AI动画合成组件
   * @param entity 实体
   */
  public removeAIAnimationSynthesis(entity: Entity): void {
    const component = this.components.get(entity);
    if (component) {
      // 清理组件资源
      component.onDestroy();
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除AI动画合成组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取AI动画合成组件
   * @param entity 实体
   * @returns AI动画合成组件，如果不存在则返回null
   */
  public getAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 生成身体动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateBodyAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'body',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 生成面部动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateFacialAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'facial',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 生成组合动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateCombinedAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'combined',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 取消生成请求
   * @param entity 实体
   * @param requestId 请求ID
   * @returns 是否成功取消
   */
  public cancelRequest(entity: Entity, requestId: string): boolean {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return false;

    return component.cancelRequest(requestId);
  }

  /**
   * 获取生成结果
   * @param entity 实体
   * @param requestId 请求ID
   * @returns 生成结果，如果不存在则返回null
   */
  public getResult(entity: Entity, requestId: string): AnimationGenerationResult | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    return component.getResult(requestId);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 系统更新
   * @param deltaTime 帧间隔时间（秒）
   * @returns 处理的实体数量
   */
  protected onUpdate(deltaTime: number): number {
    let processedEntities = 0;

    // 检查系统状态
    if (this.systemState !== AISystemState.READY) {
      return processedEntities;
    }

    // 更新所有AI动画合成组件
    for (const component of this.components.values()) {
      component.update(deltaTime);
      processedEntities++;
    }

    // 处理批处理队列
    this.processBatchQueue();

    // 更新活跃请求数
    this.updateActiveRequestsCount();

    return processedEntities;
  }

  /**
   * 处理批处理队列
   */
  private processBatchQueue(): void {
    if (this.isBatchProcessing || this.batchQueue.length === 0) {
      return;
    }

    const batchSize = this.config.batchSize || 4;
    if (this.batchQueue.length >= batchSize) {
      this.isBatchProcessing = true;

      const batch = this.batchQueue.splice(0, batchSize);
      this.processAIBatch(batch).finally(() => {
        this.isBatchProcessing = false;
      });
    }
  }

  /**
   * 处理AI请求批次
   * @param batch 批次请求
   */
  private async processAIBatch(batch: AnimationGenerationRequest[]): Promise<void> {
    if (!this.activeModel) {
      return;
    }

    try {
      // 并行处理批次中的所有请求
      const promises = batch.map(request => this.processRequest(request));
      await Promise.all(promises);
    } catch (error) {
      if (this.config.debug) {
        console.error('批处理失败:', error);
      }
    }
  }

  /**
   * 处理单个请求
   * @param request 请求
   */
  private async processRequest(request: AnimationGenerationRequest): Promise<void> {
    if (!this.activeModel) {
      return;
    }

    this.aiPerformanceStats.activeRequests++;

    try {
      let result: AnimationGenerationResult;

      switch (request.type) {
        case 'body':
          result = await this.activeModel.generateBodyAnimation(request);
          break;
        case 'facial':
          result = await this.activeModel.generateFacialAnimation(request);
          break;
        case 'combined':
          result = await this.activeModel.generateCombinedAnimation(request);
          break;
        default:
          throw new Error(`不支持的动画类型: ${request.type}`);
      }

      this.eventEmitter.emit('generationComplete', { result });
    } catch (error) {
      const errorResult: AnimationGenerationResult = {
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userData: request.userData
      };

      this.eventEmitter.emit('generationError', { result: errorResult });
    } finally {
      this.aiPerformanceStats.activeRequests--;
    }
  }

  /**
   * 更新活跃请求数
   */
  private updateActiveRequestsCount(): void {
    let activeCount = 0;
    for (const component of this.components.values()) {
      // 假设组件有获取活跃请求数的方法
      activeCount += (component as any).getActiveRequestsCount?.() || 0;
    }
    this.aiPerformanceStats.activeRequests = activeCount;
  }

  /**
   * 添加AI模型
   * @param modelId 模型ID
   * @param model AI模型
   */
  public addModel(modelId: string, model: IAIAnimationModel): void {
    this.aiModels.set(modelId, model);

    if (this.config.debug) {
      console.log(`添加AI模型: ${modelId}`);
    }
  }

  /**
   * 移除AI模型
   * @param modelId 模型ID
   */
  public removeModel(modelId: string): void {
    this.aiModels.delete(modelId);

    if (this.activeModel && this.getActiveModelId() === modelId) {
      this.activeModel = null;
      this.systemState = AISystemState.ERROR;
    }

    if (this.config.debug) {
      console.log(`移除AI模型: ${modelId}`);
    }
  }

  /**
   * 切换活跃模型
   * @param modelId 模型ID
   * @returns 是否成功切换
   */
  public async switchModel(modelId: string): Promise<boolean> {
    const model = this.aiModels.get(modelId);
    if (!model) {
      if (this.config.debug) {
        console.error(`模型不存在: ${modelId}`);
      }
      return false;
    }

    try {
      this.systemState = AISystemState.LOADING_MODEL;

      // 初始化新模型（如果尚未初始化）
      const success = await model.initialize();
      if (!success) {
        throw new Error(`模型初始化失败: ${modelId}`);
      }

      // 切换活跃模型
      this.activeModel = model;
      this.systemState = AISystemState.READY;

      // 更新所有组件的模型
      this.updateComponentModels();

      this.eventEmitter.emit('modelSwitched', { modelId, success: true });

      if (this.config.debug) {
        console.log(`成功切换到模型: ${modelId}`);
      }

      return true;
    } catch (error) {
      this.systemState = AISystemState.ERROR;
      this.eventEmitter.emit('modelSwitched', { modelId, success: false, error });

      if (this.config.debug) {
        console.error(`切换模型失败: ${modelId}`, error);
      }

      return false;
    }
  }

  /**
   * 获取当前活跃模型ID
   * @returns 模型ID
   */
  public getActiveModelId(): string | null {
    for (const [id, model] of this.aiModels.entries()) {
      if (model === this.activeModel) {
        return id;
      }
    }
    return null;
  }

  /**
   * 获取所有模型信息
   * @returns 模型信息列表
   */
  public getModelInfos(): ModelInfo[] {
    const infos: ModelInfo[] = [];

    for (const [id, model] of this.aiModels.entries()) {
      infos.push({
        id,
        type: 'LocalAIAnimationModel', // 可以从模型获取实际类型
        loaded: model === this.activeModel,
        capabilities: ['body', 'facial', 'combined'] // 可以从模型获取实际能力
      });
    }

    return infos;
  }

  /**
   * 获取AI性能统计
   * @returns AI性能统计信息
   */
  public getAIPerformanceStats(): PerformanceStats {
    return { ...this.aiPerformanceStats };
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.aiPerformanceStats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageGenerationTime: 0,
      activeRequests: 0,
      cacheHitRate: 0,
      systemLoad: 0
    };
  }

  /**
   * 获取系统状态
   * @returns 系统状态
   */
  public getSystemState(): AISystemState {
    return this.systemState;
  }

  /**
   * 动态更新配置
   * @param newConfig 新配置
   */
  public updateConfig(newConfig: Partial<AIAnimationSynthesisConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 通知配置更新回调
    for (const callback of this.configUpdateCallbacks) {
      callback(this.config);
    }

    // 如果批处理大小改变，更新组件配置
    if (oldConfig.batchSize !== this.config.batchSize) {
      for (const component of this.components.values()) {
        // 假设组件有更新配置的方法
        (component as any).updateConfig?.(this.config);
      }
    }

    this.eventEmitter.emit('configUpdated', { oldConfig, newConfig: this.config });

    if (this.config.debug) {
      console.log('配置已更新', { oldConfig, newConfig: this.config });
    }
  }

  /**
   * 添加配置更新回调
   * @param callback 回调函数
   */
  public onConfigUpdate(callback: (config: AIAnimationSynthesisConfig) => void): void {
    this.configUpdateCallbacks.add(callback);
  }

  /**
   * 移除配置更新回调
   * @param callback 回调函数
   */
  public offConfigUpdate(callback: (config: AIAnimationSynthesisConfig) => void): void {
    this.configUpdateCallbacks.delete(callback);
  }

  /**
   * 系统销毁
   */
  protected onDestroy(): void {
    this.systemState = AISystemState.DISPOSING;

    // 停止健康检查
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    // 清理所有组件
    for (const component of this.components.values()) {
      component.onDestroy();
    }
    this.components.clear();

    // 清理AI模型
    this.aiModels.clear();
    this.activeModel = null;

    // 清理队列
    this.batchQueue = [];

    // 清理回调
    this.configUpdateCallbacks.clear();

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    // 重置统计
    this.resetPerformanceStats();

    if (this.config.debug) {
      console.log('AI动画合成系统已销毁');
    }
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return AIAnimationSynthesisSystem.type;
  }

  /**
   * 检查系统是否健康
   * @returns 是否健康
   */
  public isHealthy(): boolean {
    return this.systemState === AISystemState.READY &&
           this.activeModel !== null &&
           this.aiPerformanceStats.systemLoad < 0.9;
  }

  /**
   * 获取AI系统信息
   * @returns AI系统信息
   */
  public getAISystemInfo(): {
    type: string;
    state: AISystemState;
    activeModelId: string | null;
    componentCount: number;
    modelCount: number;
    stats: PerformanceStats;
  } {
    return {
      type: this.getType(),
      state: this.systemState,
      activeModelId: this.getActiveModelId(),
      componentCount: this.components.size,
      modelCount: this.aiModels.size,
      stats: this.getAIPerformanceStats()
    };
  }

  /**
   * 强制垃圾回收（如果支持）
   */
  public forceGarbageCollection(): void {
    // 清理过期缓存
    for (const component of this.components.values()) {
      (component as any).clearCache?.();
    }

    // 如果浏览器支持，触发垃圾回收
    if ((window as any).gc) {
      (window as any).gc();
    }

    if (this.config.debug) {
      console.log('已执行垃圾回收');
    }
  }
}
