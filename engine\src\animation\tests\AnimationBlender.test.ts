/**
 * 动画混合器测试
 * 验证动画混合器的完整功能，包括新增的高级功能
 */
import { AnimationBlender } from '../AnimationBlender';
import { Animator } from '../Animator';
import { DebugMode } from '../utils/AnimationDebugger';
import { QualityLevel } from '../utils/AnimationQualityController';

// Mock Animator class
class MockAnimator {
  private clips: Map<string, any> = new Map();

  addClip(clip: any): void {
    this.clips.set(clip.name, clip);
  }

  getClip(name: string): any {
    return this.clips.get(name);
  }

  removeClip(name: string): void {
    this.clips.delete(name);
  }

  play(name: string): void {
    // Mock implementation
  }

  stop(name: string): void {
    // Mock implementation
  }
}

describe('AnimationBlender', () => {
  let animator: MockAnimator;
  let blender: AnimationBlender;

  beforeEach(() => {
    animator = new MockAnimator();
    blender = new AnimationBlender(animator as any, {
      debug: true,
      enableCompression: true,
      enableQualityControl: true,
      enablePerformanceMonitoring: true
    });
  });

  afterEach(() => {
    if (blender) {
      blender.destroy();
    }
  });

  describe('基础功能', () => {
    test('应该正确创建混合器', () => {
      expect(blender).toBeDefined();
      expect(typeof blender.addLayer).toBe('function');
      expect(typeof blender.removeLayer).toBe('function');
    });

    test('应该正确添加和移除混合层', () => {
      // 添加测试动画片段
      const mockClip = { name: 'testClip', duration: 2.0 };
      animator.addClip(mockClip);

      // 添加混合层
      const layerId = blender.addLayer('testClip', 1.0);
      expect(layerId).toBeGreaterThanOrEqual(0);

      // 移除混合层
      const removed = blender.removeLayer(layerId);
      expect(removed).toBe(true);
    });

    test('应该正确创建和管理遮罩', () => {
      const maskName = 'testMask';
      const joints = ['joint1', 'joint2', 'joint3'];

      blender.createMask(maskName, joints);
      const mask = blender.getMask(maskName);
      
      expect(mask).toBeDefined();
      expect(mask?.getName()).toBe(maskName);
    });
  });

  describe('事件系统', () => {
    test('应该正确处理动画事件', (done) => {
      const mockClip = { name: 'eventTestClip', duration: 1.0 };
      animator.addClip(mockClip);

      // 监听事件
      let eventReceived = false;
      const eventSystem = (blender as any).eventSystem;
      
      eventSystem.on('animationStart', () => {
        eventReceived = true;
        expect(eventReceived).toBe(true);
        done();
      });

      // 触发动画开始事件
      eventSystem.registerAnimationStart('eventTestClip', 1.0, false);
    });

    test('应该正确处理动画标记点', () => {
      const eventSystem = (blender as any).eventSystem;
      
      eventSystem.addMarker('testAnimation', {
        name: 'testMarker',
        time: 0.5,
        data: { test: true }
      });

      const markers = eventSystem.getMarkers('testAnimation');
      expect(markers).toHaveLength(1);
      expect(markers[0].name).toBe('testMarker');
      expect(markers[0].time).toBe(0.5);
    });
  });

  describe('动画压缩', () => {
    test('应该正确压缩动画片段', () => {
      // 创建模拟动画片段
      const mockClip = {
        name: 'compressTestClip',
        duration: 2.0,
        tracks: [
          {
            name: 'test.position',
            times: [0, 1, 2],
            values: [0, 0, 0, 1, 1, 1, 2, 2, 2],
            getValueSize: () => 3,
            getInterpolation: () => 'LINEAR'
          }
        ]
      };
      
      animator.addClip(mockClip);

      const result = blender.compressAnimation('compressTestClip');
      expect(result).toBeDefined();
      expect(result.stats).toBeDefined();
      expect(typeof result.stats.compressionRatio).toBe('number');
    });
  });

  describe('动画预加载', () => {
    test('应该正确添加预加载项', () => {
      const url = 'test://animation.json';
      const priority = 1;

      const id = blender.preloadAnimation(url, priority);
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^anim_/);
    });

    test('应该正确获取预加载状态', () => {
      const preloader = (blender as any).preloader;
      const stats = preloader.getStats();
      
      expect(stats).toBeDefined();
      expect(typeof stats.totalItems).toBe('number');
      expect(typeof stats.completedItems).toBe('number');
    });
  });

  describe('质量控制', () => {
    test('应该正确设置质量等级', () => {
      blender.setQualityLevel(QualityLevel.HIGH);
      
      const qualityController = (blender as any).qualityController;
      expect(qualityController.getQuality()).toBe(QualityLevel.HIGH);
    });

    test('应该正确获取质量设置', () => {
      const qualityController = (blender as any).qualityController;
      const settings = qualityController.getQualitySettings(QualityLevel.MEDIUM);
      
      expect(settings).toBeDefined();
      expect(typeof settings.animationFrameRate).toBe('number');
      expect(typeof settings.maxBlendLayers).toBe('number');
    });

    test('应该正确检测设备性能', () => {
      const qualityController = (blender as any).qualityController;
      const devicePerformance = qualityController.getDevicePerformance();
      
      expect(['low', 'medium', 'high']).toContain(devicePerformance);
    });
  });

  describe('调试功能', () => {
    test('应该正确设置调试模式', () => {
      blender.setDebugMode(DebugMode.DETAILED);
      
      const debugger = (blender as any).debugger;
      expect(debugger.getConfig().mode).toBe(DebugMode.DETAILED);
    });

    test('应该正确记录调试日志', () => {
      const debugger = (blender as any).debugger;
      
      debugger.log('info', 'test', 'Test message', { test: true });
      
      const logs = debugger.getLogs('test');
      expect(logs.length).toBeGreaterThan(0);
      expect(logs[logs.length - 1].message).toBe('Test message');
    });

    test('应该正确导出调试数据', () => {
      const debugger = (blender as any).debugger;
      
      debugger.log('info', 'export', 'Export test');
      const exportData = debugger.exportDebugData();
      
      expect(typeof exportData).toBe('string');
      
      const parsed = JSON.parse(exportData);
      expect(parsed.logs).toBeDefined();
      expect(parsed.config).toBeDefined();
    });
  });

  describe('网络同步', () => {
    test('应该正确启用网络同步', () => {
      const serverUrl = 'ws://localhost:8080';
      
      // 这个测试可能需要模拟WebSocket
      expect(() => {
        blender.enableNetworkSync(serverUrl);
      }).not.toThrow();
    });

    test('应该正确禁用网络同步', () => {
      expect(() => {
        blender.disableNetworkSync();
      }).not.toThrow();
    });
  });

  describe('系统状态', () => {
    test('应该正确获取系统状态', () => {
      const status = blender.getSystemStatus();
      
      expect(status).toBeDefined();
      expect(status.blender).toBeDefined();
      expect(status.eventSystem).toBeDefined();
      expect(status.preloader).toBeDefined();
      expect(status.debugger).toBeDefined();
      expect(status.qualityController).toBeDefined();
      
      expect(typeof status.blender.layersCount).toBe('number');
      expect(typeof status.blender.cacheEnabled).toBe('boolean');
      expect(typeof status.debugger.enabled).toBe('boolean');
    });
  });

  describe('高级混合功能', () => {
    test('应该正确处理层级混合', () => {
      // 添加测试动画片段
      const mockClip1 = { name: 'clip1', duration: 2.0 };
      const mockClip2 = { name: 'clip2', duration: 2.0 };
      animator.addClip(mockClip1);
      animator.addClip(mockClip2);

      // 创建层级混合
      const layerId1 = blender.addLayer('clip1', 0.7);
      const layerId2 = blender.addLayer('clip2', 0.3);

      expect(layerId1).toBeGreaterThanOrEqual(0);
      expect(layerId2).toBeGreaterThanOrEqual(0);
      expect(layerId1).not.toBe(layerId2);
    });

    test('应该正确处理序列混合', () => {
      // 添加测试动画片段
      const clips = ['seq1', 'seq2', 'seq3'].map(name => ({ name, duration: 1.0 }));
      clips.forEach(clip => animator.addClip(clip));

      // 创建序列混合
      const sequenceId = blender.createSequence('testSequence', [
        { clipName: 'seq1', duration: 1.0, blendTime: 0.2 },
        { clipName: 'seq2', duration: 1.0, blendTime: 0.2 },
        { clipName: 'seq3', duration: 1.0, blendTime: 0.2 }
      ]);

      expect(sequenceId).toBeGreaterThanOrEqual(0);
    });
  });

  describe('性能优化', () => {
    test('应该正确处理缓存', () => {
      blender.setCacheEnabled(true);
      
      // 测试缓存功能
      const status = blender.getSystemStatus();
      expect(status.blender.cacheEnabled).toBe(true);
    });

    test('应该正确处理对象池', () => {
      blender.setObjectPoolEnabled(true);
      
      // 测试对象池功能
      expect(() => {
        blender.setObjectPoolEnabled(false);
      }).not.toThrow();
    });

    test('应该正确处理批处理', () => {
      blender.setBatchProcessingEnabled(true);
      
      // 测试批处理功能
      expect(() => {
        blender.setBatchProcessingEnabled(false);
      }).not.toThrow();
    });
  });

  describe('资源清理', () => {
    test('应该正确销毁混合器', () => {
      const status = blender.getSystemStatus();
      expect(status.eventSystem.isRunning).toBe(true);
      
      blender.destroy();
      
      // 验证资源已清理
      expect(() => {
        blender.getSystemStatus();
      }).not.toThrow();
    });
  });
});
