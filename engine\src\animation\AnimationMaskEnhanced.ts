/**
 * 增强版动画遮罩
 * 提供更高级的遮罩功能，包括骨骼层次结构遮罩和动态遮罩
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { AnimationMask, MaskType, MaskWeightType, BoneGroupType, AnimationMaskConfig } from './AnimationMask';

/**
 * 动态遮罩类型
 */
export enum DynamicMaskType {
  /** 距离 - 基于与目标点的距离 */
  DISTANCE = 'distance',
  /** 方向 - 基于与目标方向的夹角 */
  DIRECTION = 'direction',
  /** 速度 - 基于骨骼的运动速度 */
  VELOCITY = 'velocity',
  /** 时间 - 基于动画时间 */
  TIME = 'time',
  /** 参数 - 基于外部参数 */
  PARAMETER = 'parameter',
  /** 加速度 - 基于骨骼的加速度 */
  ACCELERATION = 'acceleration',
  /** 角速度 - 基于骨骼的角速度 */
  ANGULAR_VELOCITY = 'angular_velocity',
  /** 碰撞 - 基于碰撞检测 */
  COLLISION = 'collision',
  /** 音频 - 基于音频分析 */
  AUDIO = 'audio',
  /** 多层 - 多个遮罩的组合 */
  MULTI_LAYER = 'multi_layer'
}

/**
 * 增强版动画遮罩配置
 */
export interface EnhancedMaskConfig extends AnimationMaskConfig {
  /** 动态遮罩类型 */
  dynamicType?: DynamicMaskType;
  /** 动态遮罩参数 */
  dynamicParams?: {
    /** 目标点（用于距离和方向） */
    target?: THREE.Vector3;
    /** 最大距离（用于距离） */
    maxDistance?: number;
    /** 最小距离（用于距离） */
    minDistance?: number;
    /** 方向向量（用于方向） */
    direction?: THREE.Vector3;
    /** 最大角度（用于方向） */
    maxAngle?: number;
    /** 速度阈值（用于速度） */
    velocityThreshold?: number;
    /** 时间曲线（用于时间） */
    timeCurve?: (time: number) => number;
    /** 参数名称（用于参数） */
    paramName?: string;
    /** 参数范围（用于参数） */
    paramRange?: [number, number];
  };
  /** 是否启用骨骼层次结构缓存 */
  enableHierarchyCache?: boolean;
  /** 是否启用权重插值 */
  enableWeightInterpolation?: boolean;
  /** 权重插值速度 */
  weightInterpolationSpeed?: number;
}

/**
 * 增强版动画遮罩事件类型
 */
export enum EnhancedMaskEventType {
  /** 动态更新 */
  DYNAMIC_UPDATE = 'dynamicUpdate',
  /** 层次结构更新 */
  HIERARCHY_UPDATE = 'hierarchyUpdate',
  /** 权重插值更新 */
  WEIGHT_INTERPOLATION_UPDATE = 'weightInterpolationUpdate'
}

/**
 * 增强版动画遮罩
 */
export class AnimationMaskEnhanced extends AnimationMask {
  /** 动态遮罩类型 */
  private dynamicType?: DynamicMaskType;
  /** 动态遮罩参数 */
  private dynamicParams: any = {};
  /** 是否启用骨骼层次结构缓存 */
  private enableHierarchyCache: boolean = true;
  /** 骨骼层次结构缓存 */
  private hierarchyCache: Map<string, string[]> = new Map();
  /** 是否启用增强权重插值 */
  private enableEnhancedWeightInterpolation: boolean = false;
  /** 增强权重插值速度 */
  private enhancedWeightInterpolationSpeed: number = 5.0;
  /** 目标权重映射 */
  private targetBoneWeights: Map<string, number> = new Map();
  /** 事件发射器 */
  private enhancedEventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EnhancedMaskConfig = {}) {
    super(config);

    this.dynamicType = config.dynamicType;
    this.dynamicParams = config.dynamicParams || {};
    this.enableHierarchyCache = config.enableHierarchyCache !== undefined ? config.enableHierarchyCache : true;
    this.enableEnhancedWeightInterpolation = config.enableWeightInterpolation || false;
    this.enhancedWeightInterpolationSpeed = config.weightInterpolationSpeed || 5.0;

    // 如果配置了权重插值，也设置基类的权重插值
    if (config.enableWeightInterpolation) {
      this.setWeightInterpolationEnabled(true, config.weightInterpolationSpeed || 5.0);
    }
  }

  /**
   * 获取动态遮罩类型
   * @returns 动态遮罩类型
   */
  public getDynamicType(): DynamicMaskType | undefined {
    return this.dynamicType;
  }

  /**
   * 设置动态遮罩类型
   * @param type 动态遮罩类型
   */
  public setDynamicType(type?: DynamicMaskType): void {
    this.dynamicType = type;
  }

  /**
   * 获取动态遮罩参数
   * @returns 动态遮罩参数
   */
  public getDynamicParams(): any {
    return this.dynamicParams;
  }

  /**
   * 设置动态遮罩参数
   * @param params 动态遮罩参数
   */
  public setDynamicParams(params: any): void {
    this.dynamicParams = params;
  }

  /**
   * 更新动态遮罩
   * @param skeleton 骨骼对象
   * @param time 当前时间
   * @param params 外部参数
   */
  public updateDynamicMask(
    skeleton?: THREE.Skeleton,
    time?: number,
    params?: Map<string, number>
  ): void {
    if (!this.dynamicType) return;

    switch (this.dynamicType) {
      case DynamicMaskType.DISTANCE:
        this.updateDistanceMask(skeleton);
        break;
      case DynamicMaskType.DIRECTION:
        this.updateDirectionMask(skeleton);
        break;
      case DynamicMaskType.VELOCITY:
        this.updateVelocityMask(skeleton);
        break;
      case DynamicMaskType.TIME:
        this.updateTimeMask(time);
        break;
      case DynamicMaskType.PARAMETER:
        this.updateParameterMask(params);
        break;
      case DynamicMaskType.ACCELERATION:
        this.updateAccelerationMask(skeleton);
        break;
      case DynamicMaskType.ANGULAR_VELOCITY:
        this.updateAngularVelocityMask(skeleton);
        break;
      case DynamicMaskType.COLLISION:
        this.updateCollisionMask(skeleton);
        break;
      case DynamicMaskType.AUDIO:
        this.updateAudioMask(params);
        break;
      case DynamicMaskType.MULTI_LAYER:
        this.updateMultiLayerMask(skeleton, time, params);
        break;
    }

    // 发出动态更新事件
    this.enhancedEventEmitter.emit(EnhancedMaskEventType.DYNAMIC_UPDATE, {
      type: this.dynamicType,
      time,
      params
    });
  }

  /**
   * 更新距离遮罩
   * @param skeleton 骨骼对象
   */
  private updateDistanceMask(skeleton?: THREE.Skeleton): void {
    if (!skeleton || !this.dynamicParams.target) return;

    const target = this.dynamicParams.target as THREE.Vector3;
    const maxDistance = this.dynamicParams.maxDistance || 10.0;
    const minDistance = this.dynamicParams.minDistance || 0.0;

    // 遍历所有骨骼
    skeleton.bones.forEach(bone => {
      // 获取骨骼世界位置
      const boneWorldPosition = new THREE.Vector3();
      bone.getWorldPosition(boneWorldPosition);

      // 计算与目标的距离
      const distance = boneWorldPosition.distanceTo(target);

      // 计算权重
      let weight = 1.0 - Math.max(0, Math.min(1, (distance - minDistance) / (maxDistance - minDistance)));

      // 设置目标权重
      this.setEnhancedTargetBoneWeight(bone.name, weight);
    });

    // 更新插值权重
    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016); // 假设16ms帧率
    }
  }

  /**
   * 更新方向遮罩
   * @param skeleton 骨骼对象
   */
  private updateDirectionMask(skeleton?: THREE.Skeleton): void {
    if (!skeleton || !this.dynamicParams.direction) return;

    const direction = (this.dynamicParams.direction as THREE.Vector3).normalize();
    const maxAngle = this.dynamicParams.maxAngle || Math.PI / 2;

    // 遍历所有骨骼
    skeleton.bones.forEach(bone => {
      // 获取骨骼方向
      const boneDirection = new THREE.Vector3(0, 1, 0).applyQuaternion(bone.quaternion).normalize();

      // 计算与目标方向的夹角
      const angle = Math.acos(boneDirection.dot(direction));

      // 计算权重
      let weight = 1.0 - Math.min(1, angle / maxAngle);

      // 设置目标权重
      this.setEnhancedTargetBoneWeight(bone.name, weight);
    });

    // 更新插值权重
    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016); // 假设16ms帧率
    }
  }

  /**
   * 更新速度遮罩
   * @param skeleton 骨骼对象
   */
  private updateVelocityMask(skeleton?: THREE.Skeleton): void {
    // 速度遮罩需要跟踪骨骼位置变化，这里简化实现
    if (!skeleton) return;

    const velocityThreshold = this.dynamicParams.velocityThreshold || 0.1;

    // 遍历所有骨骼
    skeleton.bones.forEach(bone => {
      // 这里应该计算骨骼的速度，但需要存储上一帧的位置
      // 简化实现，使用随机值模拟
      const velocity = Math.random() * 0.2;

      // 计算权重
      let weight = Math.min(1, velocity / velocityThreshold);

      // 设置目标权重
      this.setEnhancedTargetBoneWeight(bone.name, weight);
    });

    // 更新插值权重
    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016); // 假设16ms帧率
    }
  }

  /**
   * 更新时间遮罩
   * @param time 当前时间
   */
  private updateTimeMask(time?: number): void {
    if (time === undefined) return;

    const timeCurve = this.dynamicParams.timeCurve;
    if (!timeCurve) return;

    // 计算时间权重
    const weight = timeCurve(time);

    // 获取所有骨骼
    const bones = this.getBones();

    // 设置所有骨骼的目标权重
    bones.forEach(bone => {
      this.setEnhancedTargetBoneWeight(bone, weight);
    });

    // 更新插值权重
    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016); // 假设16ms帧率
    }
  }

  /**
   * 更新参数遮罩
   * @param params 外部参数
   */
  private updateParameterMask(params?: Map<string, number>): void {
    if (!params) return;

    const paramName = this.dynamicParams.paramName;
    if (!paramName) return;

    const paramValue = params.get(paramName);
    if (paramValue === undefined) return;

    const paramRange = this.dynamicParams.paramRange || [0, 1];
    
    // 计算参数权重
    const weight = Math.max(0, Math.min(1, (paramValue - paramRange[0]) / (paramRange[1] - paramRange[0])));

    // 获取所有骨骼
    const bones = this.getBones();

    // 设置所有骨骼的目标权重
    bones.forEach(bone => {
      this.setEnhancedTargetBoneWeight(bone, weight);
    });

    // 更新插值权重
    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016); // 假设16ms帧率
    }
  }

  /**
   * 设置增强目标骨骼权重
   * @param boneName 骨骼名称
   * @param weight 权重
   */
  private setEnhancedTargetBoneWeight(boneName: string, weight: number): void {
    this.targetBoneWeights.set(boneName, weight);

    // 如果不启用插值，直接设置权重
    if (!this.enableEnhancedWeightInterpolation) {
      this.setBoneWeight(boneName, weight);
    }
  }

  /**
   * 设置目标骨骼权重（保持兼容性）
   * @param boneName 骨骼名称
   * @param weight 权重
   */
  public setTargetBoneWeight(boneName: string, weight: number): void {
    this.setEnhancedTargetBoneWeight(boneName, weight);
  }

  /**
   * 更新增强权重插值
   * @param deltaTime 时间增量
   */
  private updateEnhancedWeightInterpolation(deltaTime: number): void {
    // 获取所有骨骼
    const bones = this.getBones();

    // 更新每个骨骼的权重
    bones.forEach(bone => {
      const currentWeight = this.getBoneWeight(bone);
      const targetWeight = this.targetBoneWeights.get(bone) || currentWeight;

      // 插值计算新权重
      const newWeight = THREE.MathUtils.lerp(
        currentWeight,
        targetWeight,
        Math.min(1, deltaTime * this.enhancedWeightInterpolationSpeed)
      );

      // 设置新权重
      this.setBoneWeight(bone, newWeight);
    });

    // 发出权重插值更新事件
    this.enhancedEventEmitter.emit(EnhancedMaskEventType.WEIGHT_INTERPOLATION_UPDATE, {
      deltaTime
    });
  }

  /**
   * 更新权重插值（保持兼容性）
   * @param deltaTime 时间增量
   */
  public updateWeightInterpolation(deltaTime: number): void {
    this.updateEnhancedWeightInterpolation(deltaTime);
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEnhancedEventListener(event: EnhancedMaskEventType, listener: (data: any) => void): void {
    this.enhancedEventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEnhancedEventListener(event: EnhancedMaskEventType, listener: (data: any) => void): void {
    this.enhancedEventEmitter.off(event, listener);
  }

  /**
   * 获取权重插值启用状态
   * @returns 是否启用权重插值
   */
  public isWeightInterpolationEnabled(): boolean {
    return this.enableEnhancedWeightInterpolation;
  }

  /**
   * 获取权重插值速度
   * @returns 权重插值速度
   */
  public getWeightInterpolationSpeed(): number {
    return this.enhancedWeightInterpolationSpeed;
  }

  /**
   * 设置权重插值启用状态
   * @param enabled 是否启用
   * @param speed 插值速度
   */
  public setEnhancedWeightInterpolationEnabled(enabled: boolean, speed: number = 5.0): void {
    this.enableEnhancedWeightInterpolation = enabled;
    this.enhancedWeightInterpolationSpeed = speed;
  }

  /**
   * 更新骨骼层次结构缓存
   * @param skeleton 骨骼对象
   */
  public updateHierarchyCache(skeleton: THREE.Skeleton): void {
    if (!this.enableHierarchyCache) return;

    // 清空现有缓存
    this.hierarchyCache.clear();

    // 构建骨骼层次结构
    skeleton.bones.forEach(bone => {
      const children: string[] = [];
      bone.children.forEach(child => {
        if (child instanceof THREE.Bone) {
          children.push(child.name);
        }
      });
      this.hierarchyCache.set(bone.name, children);
    });

    // 发出层次结构更新事件
    this.enhancedEventEmitter.emit(EnhancedMaskEventType.HIERARCHY_UPDATE, {
      cacheSize: this.hierarchyCache.size
    });
  }

  /**
   * 获取骨骼的子骨骼列表
   * @param boneName 骨骼名称
   * @returns 子骨骼列表
   */
  public getBoneChildren(boneName: string): string[] {
    return this.hierarchyCache.get(boneName) || [];
  }

  /**
   * 序列化增强遮罩
   * @returns 序列化数据
   */
  public serialize(): any {
    const baseData = super.serialize();

    return {
      ...baseData,
      dynamicType: this.dynamicType,
      dynamicParams: this.dynamicParams,
      enableHierarchyCache: this.enableHierarchyCache,
      enableEnhancedWeightInterpolation: this.enableEnhancedWeightInterpolation,
      enhancedWeightInterpolationSpeed: this.enhancedWeightInterpolationSpeed,
      targetBoneWeights: Array.from(this.targetBoneWeights.entries()),
      hierarchyCache: Array.from(this.hierarchyCache.entries())
    };
  }

  /**
   * 反序列化增强遮罩
   * @param data 序列化数据
   * @returns 增强遮罩实例
   */
  public static deserialize(data: any): AnimationMaskEnhanced {
    const config: EnhancedMaskConfig = {
      name: data.name,
      type: data.type,
      weightType: data.weightType,
      bones: data.bones,
      debug: data.debug,
      rootBone: data.rootBone,
      distanceWeightConfig: data.distanceWeightConfig,
      gradientWeightConfig: data.gradientWeightConfig,
      dynamicType: data.dynamicType,
      dynamicParams: data.dynamicParams,
      enableHierarchyCache: data.enableHierarchyCache,
      enableWeightInterpolation: data.enableEnhancedWeightInterpolation,
      weightInterpolationSpeed: data.enhancedWeightInterpolationSpeed
    };

    const mask = new AnimationMaskEnhanced(config);

    // 恢复骨骼权重
    if (data.boneWeights) {
      for (const [boneName, weight] of data.boneWeights) {
        mask.setBoneWeight(boneName, weight);
      }
    }

    // 恢复目标权重
    if (data.targetBoneWeights) {
      for (const [boneName, weight] of data.targetBoneWeights) {
        mask.targetBoneWeights.set(boneName, weight);
      }
    }

    // 恢复层次结构缓存
    if (data.hierarchyCache) {
      for (const [boneName, children] of data.hierarchyCache) {
        mask.hierarchyCache.set(boneName, children);
      }
    }

    return mask;
  }

  /**
   * 克隆增强遮罩
   * @returns 克隆的增强遮罩
   */
  public clone(): AnimationMaskEnhanced {
    const serialized = this.serialize();
    const cloned = AnimationMaskEnhanced.deserialize(serialized);
    cloned.setName(`${this.getName()}_clone`);
    return cloned;
  }

  /**
   * 更新加速度遮罩
   * @param skeleton 骨骼对象
   */
  private updateAccelerationMask(skeleton?: THREE.Skeleton): void {
    if (!skeleton) return;

    const accelerationThreshold = this.dynamicParams.accelerationThreshold || 0.5;

    skeleton.bones.forEach(bone => {
      // 简化实现：基于骨骼位置的二阶导数模拟加速度
      const acceleration = Math.random() * 1.0; // 实际应该计算真实加速度
      const weight = Math.min(1, acceleration / accelerationThreshold);

      this.setEnhancedTargetBoneWeight(bone.name, weight);
    });

    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016);
    }
  }

  /**
   * 更新角速度遮罩
   * @param skeleton 骨骼对象
   */
  private updateAngularVelocityMask(skeleton?: THREE.Skeleton): void {
    if (!skeleton) return;

    const angularVelocityThreshold = this.dynamicParams.angularVelocityThreshold || 1.0;

    skeleton.bones.forEach(bone => {
      // 简化实现：基于四元数变化模拟角速度
      const angularVelocity = Math.random() * 2.0; // 实际应该计算真实角速度
      const weight = Math.min(1, angularVelocity / angularVelocityThreshold);

      this.setEnhancedTargetBoneWeight(bone.name, weight);
    });

    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016);
    }
  }

  /**
   * 更新碰撞遮罩
   * @param skeleton 骨骼对象
   */
  private updateCollisionMask(skeleton?: THREE.Skeleton): void {
    if (!skeleton) return;

    const collisionRadius = this.dynamicParams.collisionRadius || 1.0;
    const collisionObjects = this.dynamicParams.collisionObjects || [];

    skeleton.bones.forEach(bone => {
      const bonePosition = new THREE.Vector3();
      bone.getWorldPosition(bonePosition);

      let weight = 0;
      for (const collisionObj of collisionObjects) {
        const distance = bonePosition.distanceTo(collisionObj.position);
        if (distance < collisionRadius) {
          weight = Math.max(weight, 1.0 - (distance / collisionRadius));
        }
      }

      this.setEnhancedTargetBoneWeight(bone.name, weight);
    });

    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016);
    }
  }

  /**
   * 更新音频遮罩
   * @param params 外部参数
   */
  private updateAudioMask(params?: Map<string, number>): void {
    if (!params) return;

    const audioLevel = params.get('audioLevel') || 0;
    const audioFrequency = params.get('audioFrequency') || 0;
    const audioThreshold = this.dynamicParams.audioThreshold || 0.5;

    // 基于音频强度和频率计算权重
    const weight = Math.min(1, (audioLevel + audioFrequency * 0.1) / audioThreshold);

    const bones = this.getBones();
    bones.forEach(bone => {
      this.setEnhancedTargetBoneWeight(bone, weight);
    });

    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016);
    }
  }

  /**
   * 更新多层遮罩
   * @param skeleton 骨骼对象
   * @param time 当前时间
   * @param params 外部参数
   */
  private updateMultiLayerMask(
    skeleton?: THREE.Skeleton,
    time?: number,
    params?: Map<string, number>
  ): void {
    const layers = this.dynamicParams.layers || [];
    const bones = this.getBones();

    bones.forEach(bone => {
      let finalWeight = 0;
      let totalLayerWeight = 0;

      for (const layer of layers) {
        let layerWeight = 0;

        switch (layer.type) {
          case DynamicMaskType.DISTANCE:
            if (skeleton && layer.target) {
              const bonePosition = new THREE.Vector3();
              const boneObj = skeleton.bones.find(b => b.name === bone);
              if (boneObj) {
                boneObj.getWorldPosition(bonePosition);
                const distance = bonePosition.distanceTo(layer.target);
                layerWeight = 1.0 - Math.min(1, distance / (layer.maxDistance || 10));
              }
            }
            break;
          case DynamicMaskType.TIME:
            if (time !== undefined && layer.timeCurve) {
              layerWeight = layer.timeCurve(time);
            }
            break;
          case DynamicMaskType.PARAMETER:
            if (params && layer.paramName) {
              const paramValue = params.get(layer.paramName) || 0;
              const range = layer.paramRange || [0, 1];
              layerWeight = (paramValue - range[0]) / (range[1] - range[0]);
            }
            break;
        }

        finalWeight += layerWeight * (layer.weight || 1.0);
        totalLayerWeight += (layer.weight || 1.0);
      }

      if (totalLayerWeight > 0) {
        finalWeight /= totalLayerWeight;
      }

      this.setEnhancedTargetBoneWeight(bone, Math.max(0, Math.min(1, finalWeight)));
    });

    if (this.enableEnhancedWeightInterpolation) {
      this.updateEnhancedWeightInterpolation(0.016);
    }
  }
}
