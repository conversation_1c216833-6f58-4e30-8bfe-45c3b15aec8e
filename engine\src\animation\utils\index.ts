/**
 * 动画工具模块导出
 */

// 动画事件系统
export { AnimationEventSystem, AnimationEventType } from './AnimationEventSystem';
export type { AnimationEventData, AnimationMarker, AnimationEventConfig } from './AnimationEventSystem';

// 动画压缩器
export { AnimationCompressor } from './AnimationCompressor';
export type { CompressionConfig, CompressionStats } from './AnimationCompressor';

// 动画预加载器
export { AnimationPreloader } from './AnimationPreloader';

// 动画同步器
export { AnimationSynchronizer } from './AnimationSynchronizer';
export type { SyncState, SyncMessage, ClientInfo, SynchronizerConfig } from './AnimationSynchronizer';

// 动画调试器
export { AnimationDebugger, DebugMode } from './AnimationDebugger';
export type { DebugInfo, PerformanceMetrics, VisualizationConfig, DebuggerConfig } from './AnimationDebugger';

// 动画质量控制器
export { AnimationQualityController, QualityLevel } from './AnimationQualityController';
export type { QualitySettings, DevicePerformance, QualityControlConfig } from './AnimationQualityController';

// 动画遮罩
export { AnimationMask } from './AnimationMask';

// 动画子片段
export { AnimationSubClip } from './AnimationSubClip';

// 动画片段适配器
export { AnimationClipAdapter } from './AnimationClipAdapter';

// 混合性能监控器
export { BlendPerformanceMonitor } from './BlendPerformanceMonitor';
