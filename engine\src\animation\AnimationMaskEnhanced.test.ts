/**
 * AnimationMaskEnhanced 测试文件
 * 验证修复后的功能是否正常工作
 */
import * as THREE from 'three';
import { AnimationMaskEnhanced, DynamicMaskType, EnhancedMaskEventType } from './AnimationMaskEnhanced';
import { MaskType, MaskWeightType } from './AnimationMask';

describe('AnimationMaskEnhanced', () => {
  let enhancedMask: AnimationMaskEnhanced;
  let skeleton: THREE.Skeleton;

  beforeEach(() => {
    enhancedMask = new AnimationMaskEnhanced({
      name: 'testEnhancedMask',
      type: MaskType.INCLUDE,
      weightType: MaskWeightType.SMOOTH,
      bones: ['head', 'neck', 'spine'],
      enableHierarchyCache: true,
      enableWeightInterpolation: true,
      weightInterpolationSpeed: 10.0
    });

    // 创建简单的骨骼结构用于测试
    const bones = [
      new THREE.Bone(), // head
      new THREE.Bone(), // neck  
      new THREE.Bone()  // spine
    ];
    bones[0].name = 'head';
    bones[1].name = 'neck';
    bones[2].name = 'spine';

    skeleton = new THREE.Skeleton(bones);
  });

  describe('基础功能', () => {
    test('应该能够创建增强遮罩', () => {
      expect(enhancedMask.getName()).toBe('testEnhancedMask');
      expect(enhancedMask.getType()).toBe(MaskType.INCLUDE);
      expect(enhancedMask.getWeightType()).toBe(MaskWeightType.SMOOTH);
    });

    test('应该能够设置和获取动态遮罩类型', () => {
      enhancedMask.setDynamicType(DynamicMaskType.DISTANCE);
      expect(enhancedMask.getDynamicType()).toBe(DynamicMaskType.DISTANCE);
    });

    test('应该能够设置和获取动态遮罩参数', () => {
      const params = {
        target: new THREE.Vector3(0, 0, 0),
        maxDistance: 5.0,
        minDistance: 1.0
      };
      enhancedMask.setDynamicParams(params);
      expect(enhancedMask.getDynamicParams()).toEqual(params);
    });
  });

  describe('权重插值功能', () => {
    test('应该支持权重插值启用状态查询', () => {
      expect(enhancedMask.isWeightInterpolationEnabled()).toBe(true);
      expect(enhancedMask.getWeightInterpolationSpeed()).toBe(10.0);
    });

    test('应该能够设置权重插值参数', () => {
      enhancedMask.setEnhancedWeightInterpolationEnabled(false, 5.0);
      expect(enhancedMask.isWeightInterpolationEnabled()).toBe(false);
      expect(enhancedMask.getWeightInterpolationSpeed()).toBe(5.0);
    });

    test('应该能够更新权重插值', () => {
      enhancedMask.setTargetBoneWeight('head', 0.8);
      enhancedMask.updateWeightInterpolation(0.1);
      
      // 权重应该向目标值插值
      const weight = enhancedMask.getBoneWeight('head');
      expect(weight).toBeGreaterThan(0);
    });
  });

  describe('动态遮罩功能', () => {
    test('应该支持距离遮罩', () => {
      enhancedMask.setDynamicType(DynamicMaskType.DISTANCE);
      enhancedMask.setDynamicParams({
        target: new THREE.Vector3(0, 0, 0),
        maxDistance: 10.0,
        minDistance: 0.0
      });

      enhancedMask.updateDynamicMask(skeleton);
      
      // 应该为所有骨骼设置了权重
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThanOrEqual(0);
      expect(enhancedMask.getBoneWeight('neck')).toBeGreaterThanOrEqual(0);
      expect(enhancedMask.getBoneWeight('spine')).toBeGreaterThanOrEqual(0);
    });

    test('应该支持方向遮罩', () => {
      enhancedMask.setDynamicType(DynamicMaskType.DIRECTION);
      enhancedMask.setDynamicParams({
        direction: new THREE.Vector3(0, 1, 0),
        maxAngle: Math.PI / 2
      });

      enhancedMask.updateDynamicMask(skeleton);
      
      // 应该为所有骨骼设置了权重
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThanOrEqual(0);
    });

    test('应该支持时间遮罩', () => {
      enhancedMask.setDynamicType(DynamicMaskType.TIME);
      enhancedMask.setDynamicParams({
        timeCurve: (t: number) => Math.sin(t * Math.PI * 2)
      });

      enhancedMask.updateDynamicMask(skeleton, 0.25);
      
      // 应该为所有骨骼设置了权重
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThan(0);
    });

    test('应该支持参数遮罩', () => {
      enhancedMask.setDynamicType(DynamicMaskType.PARAMETER);
      enhancedMask.setDynamicParams({
        paramName: 'intensity',
        paramRange: [0, 1]
      });

      const params = new Map<string, number>();
      params.set('intensity', 0.7);

      enhancedMask.updateDynamicMask(skeleton, undefined, params);
      
      // 应该为所有骨骼设置了权重
      expect(enhancedMask.getBoneWeight('head')).toBe(0.7);
    });

    test('应该支持新的动态遮罩类型', () => {
      // 测试加速度遮罩
      enhancedMask.setDynamicType(DynamicMaskType.ACCELERATION);
      enhancedMask.setDynamicParams({ accelerationThreshold: 0.5 });
      enhancedMask.updateDynamicMask(skeleton);
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThanOrEqual(0);

      // 测试角速度遮罩
      enhancedMask.setDynamicType(DynamicMaskType.ANGULAR_VELOCITY);
      enhancedMask.setDynamicParams({ angularVelocityThreshold: 1.0 });
      enhancedMask.updateDynamicMask(skeleton);
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThanOrEqual(0);

      // 测试音频遮罩
      enhancedMask.setDynamicType(DynamicMaskType.AUDIO);
      enhancedMask.setDynamicParams({ audioThreshold: 0.5 });
      const audioParams = new Map<string, number>();
      audioParams.set('audioLevel', 0.8);
      audioParams.set('audioFrequency', 440);
      enhancedMask.updateDynamicMask(skeleton, undefined, audioParams);
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThan(0);
    });
  });

  describe('骨骼层次结构缓存', () => {
    test('应该能够更新和查询骨骼层次结构', () => {
      enhancedMask.updateHierarchyCache(skeleton);
      
      const children = enhancedMask.getBoneChildren('head');
      expect(Array.isArray(children)).toBe(true);
    });
  });

  describe('序列化和反序列化', () => {
    test('应该支持序列化和反序列化', () => {
      enhancedMask.setDynamicType(DynamicMaskType.DISTANCE);
      enhancedMask.setDynamicParams({
        target: new THREE.Vector3(1, 2, 3),
        maxDistance: 5.0
      });
      enhancedMask.setBoneWeight('head', 0.8);

      const serialized = enhancedMask.serialize();
      expect(serialized.dynamicType).toBe(DynamicMaskType.DISTANCE);
      expect(serialized.dynamicParams.target).toBeDefined();

      const deserialized = AnimationMaskEnhanced.deserialize(serialized);
      expect(deserialized.getName()).toBe('testEnhancedMask');
      expect(deserialized.getDynamicType()).toBe(DynamicMaskType.DISTANCE);
      expect(deserialized.getBoneWeight('head')).toBe(0.8);
    });

    test('应该支持克隆功能', () => {
      enhancedMask.setDynamicType(DynamicMaskType.TIME);
      enhancedMask.setBoneWeight('head', 0.6);
      
      const cloned = enhancedMask.clone();
      expect(cloned.getName()).toBe('testEnhancedMask_clone');
      expect(cloned.getDynamicType()).toBe(DynamicMaskType.TIME);
      expect(cloned.getBoneWeight('head')).toBe(0.6);
    });
  });

  describe('事件系统', () => {
    test('应该能够添加和触发事件监听器', () => {
      let eventTriggered = false;
      const listener = () => {
        eventTriggered = true;
      };

      enhancedMask.addEnhancedEventListener(EnhancedMaskEventType.DYNAMIC_UPDATE, listener);
      
      enhancedMask.setDynamicType(DynamicMaskType.DISTANCE);
      enhancedMask.updateDynamicMask(skeleton);
      
      expect(eventTriggered).toBe(true);

      enhancedMask.removeEnhancedEventListener(EnhancedMaskEventType.DYNAMIC_UPDATE, listener);
    });

    test('应该能够触发权重插值更新事件', () => {
      let interpolationEventTriggered = false;
      const listener = () => {
        interpolationEventTriggered = true;
      };

      enhancedMask.addEnhancedEventListener(EnhancedMaskEventType.WEIGHT_INTERPOLATION_UPDATE, listener);
      
      enhancedMask.setTargetBoneWeight('head', 0.5);
      enhancedMask.updateWeightInterpolation(0.1);
      
      expect(interpolationEventTriggered).toBe(true);
    });

    test('应该能够触发层次结构更新事件', () => {
      let hierarchyEventTriggered = false;
      const listener = () => {
        hierarchyEventTriggered = true;
      };

      enhancedMask.addEnhancedEventListener(EnhancedMaskEventType.HIERARCHY_UPDATE, listener);
      
      enhancedMask.updateHierarchyCache(skeleton);
      
      expect(hierarchyEventTriggered).toBe(true);
    });
  });

  describe('多层遮罩', () => {
    test('应该支持多层遮罩组合', () => {
      enhancedMask.setDynamicType(DynamicMaskType.MULTI_LAYER);
      enhancedMask.setDynamicParams({
        layers: [
          {
            type: DynamicMaskType.DISTANCE,
            target: new THREE.Vector3(0, 0, 0),
            maxDistance: 5.0,
            weight: 0.6
          },
          {
            type: DynamicMaskType.TIME,
            timeCurve: (t: number) => Math.sin(t * Math.PI),
            weight: 0.4
          }
        ]
      });

      const params = new Map<string, number>();
      enhancedMask.updateDynamicMask(skeleton, 0.5, params);
      
      // 应该基于多层计算设置权重
      expect(enhancedMask.getBoneWeight('head')).toBeGreaterThanOrEqual(0);
    });
  });
});
