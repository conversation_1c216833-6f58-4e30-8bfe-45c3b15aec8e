/**
 * AI动画合成系统测试
 * 验证AI动画合成系统的完整功能
 */
import { AIAnimationSynthesisSystem, AISystemState, PerformanceStats } from '../AIAnimationSynthesisSystem';
import { AIAnimationSynthesisConfig } from '../AIAnimationSynthesis';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { LocalAIAnimationModel } from '../ai/LocalAIAnimationModel';

describe('AIAnimationSynthesisSystem', () => {
  let world: World;
  let system: AIAnimationSynthesisSystem;
  let entity: Entity;

  beforeEach(() => {
    world = new World();
    entity = new Entity();
    world.addEntity(entity);
  });

  afterEach(() => {
    if (system) {
      system.onDestroy();
      world.removeSystem(system);
    }
  });

  describe('系统初始化', () => {
    test('应该正确创建系统', () => {
      const config: Partial<AIAnimationSynthesisConfig> = {
        debug: true,
        useLocalModel: true,
        batchSize: 4
      };

      system = new AIAnimationSynthesisSystem(world, config);
      
      expect(system).toBeDefined();
      expect(system.getType()).toBe('AIAnimationSynthesis');
      expect(system.getSystemState()).toBe(AISystemState.UNINITIALIZED);
    });

    test('应该正确初始化系统', async () => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      
      // 模拟系统初始化
      system.onInitialize();
      
      // 等待模型加载
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      expect(system.getSystemState()).toBe(AISystemState.READY);
    });
  });

  describe('组件管理', () => {
    beforeEach(() => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
    });

    test('应该正确创建和管理组件', () => {
      const component = system.createAIAnimationSynthesis(entity);
      
      expect(component).toBeDefined();
      expect(system.getAIAnimationSynthesis(entity)).toBe(component);
      
      // 重复创建应该返回同一个组件
      const component2 = system.createAIAnimationSynthesis(entity);
      expect(component2).toBe(component);
    });

    test('应该正确移除组件', () => {
      const component = system.createAIAnimationSynthesis(entity);
      expect(system.getAIAnimationSynthesis(entity)).toBe(component);
      
      system.removeAIAnimationSynthesis(entity);
      expect(system.getAIAnimationSynthesis(entity)).toBeNull();
    });
  });

  describe('模型管理', () => {
    beforeEach(() => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
    });

    test('应该支持添加和移除模型', () => {
      const model = new LocalAIAnimationModel({ debug: true });
      
      system.addModel('test-model', model);
      const modelInfos = system.getModelInfos();
      
      expect(modelInfos.some(info => info.id === 'test-model')).toBe(true);
      
      system.removeModel('test-model');
      const updatedInfos = system.getModelInfos();
      
      expect(updatedInfos.some(info => info.id === 'test-model')).toBe(false);
    });

    test('应该支持模型切换', async () => {
      const model1 = new LocalAIAnimationModel({ debug: true });
      const model2 = new LocalAIAnimationModel({ debug: true });
      
      system.addModel('model1', model1);
      system.addModel('model2', model2);
      
      const success1 = await system.switchModel('model1');
      expect(success1).toBe(true);
      expect(system.getActiveModelId()).toBe('model1');
      
      const success2 = await system.switchModel('model2');
      expect(success2).toBe(true);
      expect(system.getActiveModelId()).toBe('model2');
    });

    test('应该处理无效模型切换', async () => {
      const success = await system.switchModel('nonexistent-model');
      expect(success).toBe(false);
    });
  });

  describe('动画生成', () => {
    beforeEach(async () => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
      
      // 等待模型加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      system.createAIAnimationSynthesis(entity);
    });

    test('应该支持生成身体动画', () => {
      const requestId = system.generateBodyAnimation(entity, '跳跃动作', 3.0, {
        loop: true,
        style: '卡通'
      });
      
      expect(requestId).toBeDefined();
      expect(typeof requestId).toBe('string');
    });

    test('应该支持生成面部动画', () => {
      const requestId = system.generateFacialAnimation(entity, '开心表情', 2.0, {
        intensity: 0.8
      });
      
      expect(requestId).toBeDefined();
      expect(typeof requestId).toBe('string');
    });

    test('应该支持生成组合动画', () => {
      const requestId = system.generateCombinedAnimation(entity, '开心地跳跃', 4.0, {
        loop: true
      });
      
      expect(requestId).toBeDefined();
      expect(typeof requestId).toBe('string');
    });

    test('应该支持取消请求', () => {
      const requestId = system.generateBodyAnimation(entity, '测试动画', 5.0);
      
      const canceled = system.cancelRequest(entity, requestId);
      expect(canceled).toBe(true);
    });
  });

  describe('性能监控', () => {
    beforeEach(() => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
    });

    test('应该提供性能统计', () => {
      const stats = system.getAIPerformanceStats();
      
      expect(stats).toBeDefined();
      expect(typeof stats.totalRequests).toBe('number');
      expect(typeof stats.successfulRequests).toBe('number');
      expect(typeof stats.failedRequests).toBe('number');
      expect(typeof stats.averageGenerationTime).toBe('number');
    });

    test('应该支持重置性能统计', () => {
      system.resetPerformanceStats();
      const stats = system.getAIPerformanceStats();
      
      expect(stats.totalRequests).toBe(0);
      expect(stats.successfulRequests).toBe(0);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('配置管理', () => {
    beforeEach(() => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
    });

    test('应该支持动态配置更新', () => {
      const newConfig = {
        batchSize: 8,
        maxCacheSize: 200
      };
      
      system.updateConfig(newConfig);
      
      // 验证配置更新事件
      let configUpdated = false;
      system.addEventListener('configUpdated', () => {
        configUpdated = true;
      });
      
      system.updateConfig({ debug: false });
      expect(configUpdated).toBe(true);
    });

    test('应该支持配置更新回调', () => {
      let callbackCalled = false;
      let receivedConfig: AIAnimationSynthesisConfig | null = null;
      
      const callback = (config: AIAnimationSynthesisConfig) => {
        callbackCalled = true;
        receivedConfig = config;
      };
      
      system.onConfigUpdate(callback);
      system.updateConfig({ batchSize: 16 });
      
      expect(callbackCalled).toBe(true);
      expect(receivedConfig?.batchSize).toBe(16);
      
      // 测试移除回调
      system.offConfigUpdate(callback);
      callbackCalled = false;
      system.updateConfig({ batchSize: 32 });
      expect(callbackCalled).toBe(false);
    });
  });

  describe('健康检查', () => {
    beforeEach(() => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
    });

    test('应该提供健康状态检查', () => {
      const isHealthy = system.isHealthy();
      expect(typeof isHealthy).toBe('boolean');
    });

    test('应该提供系统信息', () => {
      const info = system.getAISystemInfo();
      
      expect(info).toBeDefined();
      expect(info.type).toBe('AIAnimationSynthesis');
      expect(typeof info.componentCount).toBe('number');
      expect(typeof info.modelCount).toBe('number');
      expect(info.stats).toBeDefined();
    });
  });

  describe('系统更新', () => {
    beforeEach(() => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
    });

    test('应该正确处理系统更新', () => {
      const processedEntities = system.onUpdate(0.016);
      expect(typeof processedEntities).toBe('number');
      expect(processedEntities).toBeGreaterThanOrEqual(0);
    });
  });

  describe('资源清理', () => {
    test('应该正确清理资源', () => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      system.onInitialize();
      
      const component = system.createAIAnimationSynthesis(entity);
      expect(system.getAIAnimationSynthesis(entity)).toBe(component);
      
      system.onDestroy();
      
      expect(system.getSystemState()).toBe(AISystemState.DISPOSING);
      expect(system.getAISystemInfo().componentCount).toBe(0);
    });

    test('应该支持强制垃圾回收', () => {
      system = new AIAnimationSynthesisSystem(world, { debug: true });
      
      // 这个测试主要验证方法不会抛出错误
      expect(() => {
        system.forceGarbageCollection();
      }).not.toThrow();
    });
  });
});
