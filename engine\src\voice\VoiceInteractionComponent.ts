/**
 * 语音交互组件
 * 集成语音识别、语音合成和实时通信功能
 */
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { EventEmitter } from 'events';

// 可选的socket.io-client导入
let io: any;

// 定义Socket接口类型
interface SocketLike {
  on(event: string, callback: (...args: any[]) => void): void;
  emit(event: string, ...args: any[]): void;
  disconnect(): void;
}

// 模拟Socket类
class MockSocket implements SocketLike {
  on() {}
  emit() {}
  disconnect() {}
}

try {
  const socketIO = require('socket.io-client');
  io = socketIO.io;
} catch (error) {
  // socket.io-client未安装，使用模拟实现
  console.warn('socket.io-client未安装，语音交互功能将受限');
  io = null;
}

/**
 * 语音配置
 */
export interface VoiceConfig {
  /** 服务器URL */
  serverUrl?: string;
  /** 认证令牌 */
  authToken?: string;
  /** 语音识别配置 */
  recognition?: {
    provider?: 'azure' | 'google' | 'baidu' | 'openai';
    language?: string;
    continuous?: boolean;
    interimResults?: boolean;
  };
  /** 语音合成配置 */
  synthesis?: {
    provider?: 'azure' | 'google' | 'baidu' | 'openai';
    voice?: string;
    language?: string;
    rate?: number;
    pitch?: number;
    volume?: number;
  };
  /** 音频配置 */
  audio?: {
    sampleRate?: number;
    channels?: number;
    bitDepth?: number;
  };
}

/**
 * 语音识别结果
 */
export interface VoiceRecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
  words?: Array<{
    word: string;
    startTime: number;
    endTime: number;
    confidence: number;
  }>;
}

/**
 * 语音合成结果
 */
export interface VoiceSynthesisResult {
  id: string;
  audioData: ArrayBuffer;
  duration: number;
  format: string;
  sampleRate: number;
}

/**
 * 嘴形同步数据
 */
export interface LipSyncData {
  id: string;
  duration: number;
  keyframes: Array<{
    time: number;
    viseme: string;
    intensity: number;
    duration: number;
  }>;
}

/**
 * 语音交互组件
 */
export class VoiceInteractionComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'VoiceInteractionComponent';

  /** 配置 */
  private config: VoiceConfig;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** WebSocket连接 */
  private socket: SocketLike | null = null;
  /** 媒体录音器 */
  private mediaRecorder: MediaRecorder | null = null;
  /** 音频流 */
  private audioStream: MediaStream | null = null;
  /** 音频上下文 */
  private audioContext: AudioContext | null = null;
  /** 是否正在录音 */
  private isRecording: boolean = false;
  /** 是否正在播放 */
  private isPlaying: boolean = false;
  /** 是否已连接 */
  private isConnected: boolean = false;

  /**
   * 构造函数
   */
  constructor(entity: Entity, config: VoiceConfig = {}) {
    super(VoiceInteractionComponent.TYPE);
    this.setEntity(entity);
    
    this.config = {
      serverUrl: 'ws://localhost:4010/voice',
      recognition: {
        provider: 'azure',
        language: 'zh-CN',
        continuous: true,
        interimResults: true,
        ...config.recognition,
      },
      synthesis: {
        provider: 'azure',
        voice: 'zh-CN-XiaoxiaoNeural',
        language: 'zh-CN',
        rate: 1.0,
        pitch: 1.0,
        volume: 1.0,
        ...config.synthesis,
      },
      audio: {
        sampleRate: 16000,
        channels: 1,
        bitDepth: 16,
        ...config.audio,
      },
      ...config,
    };
  }

  /**
   * 初始化组件
   */
  async initialize(): Promise<void> {
    try {
      // 初始化音频上下文
      await this.initializeAudioContext();
      
      // 连接WebSocket
      await this.connectWebSocket();
      
      // 请求麦克风权限
      await this.requestMicrophonePermission();
      
      console.log('语音交互组件初始化完成');
    } catch (error) {
      console.error('语音交互组件初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化音频上下文
   */
  private async initializeAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.audio!.sampleRate,
      });

      // 如果音频上下文被暂停，尝试恢复
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }
    } catch (error) {
      throw new Error(`音频上下文初始化失败: ${error.message}`);
    }
  }

  /**
   * 连接WebSocket
   */
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        if (!io) {
          reject(new Error('socket.io-client未安装，无法建立WebSocket连接'));
          return;
        }

        this.socket = io(this.config.serverUrl!, {
          auth: {
            token: this.config.authToken,
          },
          transports: ['websocket'],
        });

        this.socket.on('connect', () => {
          this.isConnected = true;
          console.log('WebSocket连接成功');
          
          // 配置语音服务
          this.socket!.emit('configure', {
            recognition: this.config.recognition,
            synthesis: this.config.synthesis,
          });
          
          resolve();
        });

        this.socket.on('disconnect', () => {
          this.isConnected = false;
          console.log('WebSocket连接断开');
          this.eventEmitter.emit('disconnected');
        });

        this.socket.on('error', (error: any) => {
          console.error('WebSocket错误:', error);
          this.eventEmitter.emit('error', error);
          reject(error);
        });

        // 监听语音识别结果
        this.socket.on('recognition-result', (result: VoiceRecognitionResult) => {
          this.eventEmitter.emit('recognitionResult', result);
        });

        // 监听语音合成完成
        this.socket.on('synthesis-completed', (result: VoiceSynthesisResult) => {
          this.playAudio(result.audioData);
          this.eventEmitter.emit('synthesisCompleted', result);
        });

        // 监听嘴形同步数据
        this.socket.on('lip-sync-generated', (data: LipSyncData) => {
          this.eventEmitter.emit('lipSyncGenerated', data);
        });

        // 设置连接超时
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('WebSocket连接超时'));
          }
        }, 10000);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 请求麦克风权限
   */
  private async requestMicrophonePermission(): Promise<void> {
    try {
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.audio!.sampleRate,
          channelCount: this.config.audio!.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });
    } catch (error) {
      throw new Error(`麦克风权限请求失败: ${error.message}`);
    }
  }

  /**
   * 开始语音识别
   */
  async startRecognition(): Promise<void> {
    if (!this.isConnected || !this.socket) {
      throw new Error('WebSocket未连接');
    }

    if (this.isRecording) {
      console.warn('语音识别已在进行中');
      return;
    }

    try {
      this.isRecording = true;
      
      // 开始录音
      await this.startRecording();
      
      // 通知服务器开始识别
      this.socket.emit('start-recognition');
      
      this.eventEmitter.emit('recognitionStarted');
    } catch (error) {
      this.isRecording = false;
      throw error;
    }
  }

  /**
   * 停止语音识别
   */
  async stopRecognition(): Promise<void> {
    if (!this.isRecording) {
      return;
    }

    try {
      this.isRecording = false;
      
      // 停止录音
      this.stopRecording();
      
      // 通知服务器停止识别
      if (this.socket) {
        this.socket.emit('stop-recognition');
      }
      
      this.eventEmitter.emit('recognitionStopped');
    } catch (error) {
      console.error('停止语音识别失败:', error);
    }
  }

  /**
   * 开始录音
   */
  private async startRecording(): Promise<void> {
    if (!this.audioStream) {
      throw new Error('音频流未初始化');
    }

    try {
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      const audioChunks: Blob[] = [];

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        const audioBuffer = await audioBlob.arrayBuffer();
        
        // 发送音频数据到服务器
        if (this.socket) {
          const base64Audio = this.arrayBufferToBase64(audioBuffer);
          this.socket.emit('audio-data', {
            audioData: base64Audio,
            format: 'webm',
          });
        }
      };

      this.mediaRecorder.start(1000); // 每秒发送一次数据
    } catch (error) {
      throw new Error(`开始录音失败: ${error.message}`);
    }
  }

  /**
   * 停止录音
   */
  private stopRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
  }

  /**
   * 语音合成
   */
  async synthesizeSpeech(text: string, options: {
    voice?: string;
    rate?: number;
    pitch?: number;
    volume?: number;
    generateLipSync?: boolean;
  } = {}): Promise<void> {
    if (!this.isConnected || !this.socket) {
      throw new Error('WebSocket未连接');
    }

    if (this.isPlaying) {
      console.warn('语音播放正在进行中');
      return;
    }

    try {
      this.isPlaying = true;
      
      const config = {
        ...this.config.synthesis,
        ...options,
      };

      // 发送合成请求
      this.socket.emit('synthesize', {
        text,
        config,
      });

      // 如果需要生成嘴形同步数据
      if (options.generateLipSync) {
        this.socket.emit('generate-lip-sync', {
          text,
          method: 'phoneme',
          language: config.language,
        });
      }

      this.eventEmitter.emit('synthesisStarted', { text });
    } catch (error) {
      this.isPlaying = false;
      throw error;
    }
  }

  /**
   * 播放音频
   */
  private async playAudio(audioData: ArrayBuffer): Promise<void> {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    try {
      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(audioData);
      
      // 创建音频源
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      
      // 连接到输出
      source.connect(this.audioContext.destination);
      
      // 播放音频
      source.start();
      
      // 监听播放结束
      source.onended = () => {
        this.isPlaying = false;
        this.eventEmitter.emit('playbackEnded');
      };

    } catch (error) {
      this.isPlaying = false;
      throw new Error(`音频播放失败: ${error.message}`);
    }
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    isRecording: boolean;
    isPlaying: boolean;
  } {
    return {
      isConnected: this.isConnected,
      isRecording: this.isRecording,
      isPlaying: this.isPlaying,
    };
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<VoiceConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.socket && this.isConnected) {
      this.socket.emit('configure', {
        recognition: this.config.recognition,
        synthesis: this.config.synthesis,
      });
    }
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 停止录音
    this.stopRecording();
    
    // 关闭音频流
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
    }
    
    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
    }
    
    // 断开WebSocket连接
    if (this.socket) {
      this.socket.disconnect();
    }
    
    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    super.dispose();
  }

  /**
   * 创建组件实例
   * @returns 新的VoiceInteractionComponent组件实例
   */
  protected createInstance(): Component {
    return new VoiceInteractionComponent(this.config);
  }
}
