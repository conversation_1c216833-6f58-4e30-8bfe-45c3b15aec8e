/**
 * 强化学习决策系统
 * 
 * 基于深度Q网络(DQN)和策略梯度方法的强化学习决策系统。
 * 支持在线学习、经验回放、目标网络等先进技术。
 */

import { EventEmitter } from 'events';
import { 
  DecisionContext, 
  DecisionOption, 
  DecisionResult 
} from '../behavior/IntelligentDecisionSystem';

/**
 * 状态表示
 */
export interface State {
  features: Float32Array;
  entityId: string;
  timestamp: number;
  metadata: { [key: string]: any };
}

/**
 * 动作表示
 */
export interface Action {
  id: string;
  type: string;
  parameters: Float32Array;
  expectedReward: number;
}

/**
 * 经验样本
 */
export interface Experience {
  state: State;
  action: Action;
  reward: number;
  nextState: State;
  done: boolean;
  timestamp: number;
}

/**
 * 神经网络层
 */
interface NetworkLayer {
  weights: Float32Array;
  biases: Float32Array;
  activation: 'relu' | 'sigmoid' | 'tanh' | 'linear';
}

/**
 * 深度Q网络
 */
class DeepQNetwork {
  private layers: NetworkLayer[] = [];
  private learningRate: number;
  private inputSize: number;
  private outputSize: number;

  constructor(
    inputSize: number, 
    hiddenSizes: number[], 
    outputSize: number, 
    learningRate: number = 0.001
  ) {
    this.inputSize = inputSize;
    this.outputSize = outputSize;
    this.learningRate = learningRate;
    
    this.initializeNetwork(inputSize, hiddenSizes, outputSize);
  }

  /**
   * 初始化网络
   */
  private initializeNetwork(inputSize: number, hiddenSizes: number[], outputSize: number): void {
    const sizes = [inputSize, ...hiddenSizes, outputSize];
    
    for (let i = 0; i < sizes.length - 1; i++) {
      const inputDim = sizes[i];
      const outputDim = sizes[i + 1];
      
      // Xavier初始化
      const limit = Math.sqrt(6 / (inputDim + outputDim));
      const weights = new Float32Array(inputDim * outputDim);
      const biases = new Float32Array(outputDim);
      
      for (let j = 0; j < weights.length; j++) {
        weights[j] = (Math.random() * 2 - 1) * limit;
      }
      
      biases.fill(0);
      
      this.layers.push({
        weights,
        biases,
        activation: i === sizes.length - 2 ? 'linear' : 'relu'
      });
    }
  }

  /**
   * 前向传播
   */
  public forward(input: Float32Array): Float32Array {
    let current = new Float32Array(input);
    
    for (const layer of this.layers) {
      current = this.forwardLayer(current, layer);
    }
    
    return current;
  }

  /**
   * 层前向传播
   */
  private forwardLayer(input: Float32Array, layer: NetworkLayer): Float32Array {
    const inputSize = input.length;
    const outputSize = layer.biases.length;
    const output = new Float32Array(outputSize);
    
    // 矩阵乘法 + 偏置
    for (let i = 0; i < outputSize; i++) {
      let sum = layer.biases[i];
      for (let j = 0; j < inputSize; j++) {
        sum += input[j] * layer.weights[j * outputSize + i];
      }
      output[i] = this.activate(sum, layer.activation);
    }
    
    return output;
  }

  /**
   * 激活函数
   */
  private activate(x: number, activation: string): number {
    switch (activation) {
      case 'relu':
        return Math.max(0, x);
      case 'sigmoid':
        return 1 / (1 + Math.exp(-x));
      case 'tanh':
        return Math.tanh(x);
      case 'linear':
      default:
        return x;
    }
  }

  /**
   * 反向传播（简化实现）
   */
  public backward(input: Float32Array, target: Float32Array, output: Float32Array): void {
    // 简化的梯度下降更新
    const error = new Float32Array(target.length);
    for (let i = 0; i < target.length; i++) {
      error[i] = target[i] - output[i];
    }
    
    // 更新最后一层权重（简化）
    const lastLayer = this.layers[this.layers.length - 1];
    const inputSize = input.length;
    
    for (let i = 0; i < lastLayer.biases.length; i++) {
      lastLayer.biases[i] += this.learningRate * error[i];
      
      for (let j = 0; j < inputSize; j++) {
        lastLayer.weights[j * lastLayer.biases.length + i] += 
          this.learningRate * error[i] * input[j];
      }
    }
  }

  /**
   * 复制网络权重
   */
  public copyWeights(): NetworkLayer[] {
    return this.layers.map(layer => ({
      weights: new Float32Array(layer.weights),
      biases: new Float32Array(layer.biases),
      activation: layer.activation
    }));
  }

  /**
   * 设置网络权重
   */
  public setWeights(layers: NetworkLayer[]): void {
    for (let i = 0; i < this.layers.length; i++) {
      this.layers[i].weights.set(layers[i].weights);
      this.layers[i].biases.set(layers[i].biases);
    }
  }

  /**
   * 获取网络参数数量
   */
  public getParameterCount(): number {
    let count = 0;
    for (const layer of this.layers) {
      count += layer.weights.length + layer.biases.length;
    }
    return count;
  }
}

/**
 * 经验回放缓冲区
 */
class ExperienceReplayBuffer {
  private buffer: Experience[] = [];
  private maxSize: number;
  private index = 0;

  constructor(maxSize: number = 10000) {
    this.maxSize = maxSize;
  }

  /**
   * 添加经验
   */
  public add(experience: Experience): void {
    if (this.buffer.length < this.maxSize) {
      this.buffer.push(experience);
    } else {
      this.buffer[this.index] = experience;
      this.index = (this.index + 1) % this.maxSize;
    }
  }

  /**
   * 采样批次
   */
  public sample(batchSize: number): Experience[] {
    if (this.buffer.length < batchSize) {
      return [...this.buffer];
    }
    
    const batch: Experience[] = [];
    const indices = new Set<number>();
    
    while (indices.size < batchSize) {
      const randomIndex = Math.floor(Math.random() * this.buffer.length);
      if (!indices.has(randomIndex)) {
        indices.add(randomIndex);
        batch.push(this.buffer[randomIndex]);
      }
    }
    
    return batch;
  }

  /**
   * 获取缓冲区大小
   */
  public size(): number {
    return this.buffer.length;
  }

  /**
   * 清空缓冲区
   */
  public clear(): void {
    this.buffer = [];
    this.index = 0;
  }
}

/**
 * 强化学习算法类型
 */
export enum RLAlgorithm {
  DQN = 'dqn',
  DOUBLE_DQN = 'double_dqn',
  DUELING_DQN = 'dueling_dqn',
  PRIORITIZED_DQN = 'prioritized_dqn',
  A3C = 'a3c',
  PPO = 'ppo',
  SAC = 'sac'
}

/**
 * 优先级经验回放项
 */
interface PrioritizedExperience extends Experience {
  priority: number;
  tdError: number;
}

/**
 * 策略网络（用于Actor-Critic算法）
 */
class PolicyNetwork {
  private layers: NetworkLayer[] = [];
  private learningRate: number;

  constructor(
    inputSize: number,
    hiddenSizes: number[],
    outputSize: number,
    learningRate: number = 0.001
  ) {
    this.learningRate = learningRate;
    this.initializeNetwork(inputSize, hiddenSizes, outputSize);
  }

  private initializeNetwork(inputSize: number, hiddenSizes: number[], outputSize: number): void {
    const sizes = [inputSize, ...hiddenSizes, outputSize];

    for (let i = 0; i < sizes.length - 1; i++) {
      const inputDim = sizes[i];
      const outputDim = sizes[i + 1];

      const limit = Math.sqrt(6 / (inputDim + outputDim));
      const weights = new Float32Array(inputDim * outputDim);
      const biases = new Float32Array(outputDim);

      for (let j = 0; j < weights.length; j++) {
        weights[j] = (Math.random() * 2 - 1) * limit;
      }

      biases.fill(0);

      this.layers.push({
        weights,
        biases,
        activation: i === sizes.length - 2 ? 'sigmoid' : 'relu' // 最后一层使用sigmoid输出概率
      });
    }
  }

  public forward(input: Float32Array): Float32Array {
    let current = new Float32Array(input);

    for (const layer of this.layers) {
      current = this.forwardLayer(current, layer);
    }

    // 应用softmax到最后的输出
    return this.softmax(current);
  }

  private forwardLayer(input: Float32Array, layer: NetworkLayer): Float32Array {
    const inputSize = input.length;
    const outputSize = layer.biases.length;
    const output = new Float32Array(outputSize);

    for (let i = 0; i < outputSize; i++) {
      let sum = layer.biases[i];
      for (let j = 0; j < inputSize; j++) {
        sum += input[j] * layer.weights[j * outputSize + i];
      }
      output[i] = this.activate(sum, layer.activation);
    }

    return output;
  }

  private activate(x: number, activation: string): number {
    switch (activation) {
      case 'relu':
        return Math.max(0, x);
      case 'sigmoid':
        return 1 / (1 + Math.exp(-x));
      case 'tanh':
        return Math.tanh(x);
      default:
        return x;
    }
  }

  private softmax(input: Float32Array): Float32Array {
    const maxVal = Math.max(...Array.from(input));
    const expValues = input.map(x => Math.exp(x - maxVal));
    const sum = expValues.reduce((a, b) => a + b, 0);
    return new Float32Array(expValues.map(x => x / sum));
  }

  public copyWeights(): NetworkLayer[] {
    return this.layers.map(layer => ({
      weights: new Float32Array(layer.weights),
      biases: new Float32Array(layer.biases),
      activation: layer.activation
    }));
  }

  public setWeights(layers: NetworkLayer[]): void {
    for (let i = 0; i < this.layers.length; i++) {
      this.layers[i].weights.set(layers[i].weights);
      this.layers[i].biases.set(layers[i].biases);
    }
  }
}

/**
 * 优先级经验回放缓冲区
 */
class PrioritizedExperienceReplayBuffer {
  private buffer: PrioritizedExperience[] = [];
  private priorities: number[] = [];
  private maxSize: number;
  private index = 0;
  private alpha = 0.6; // 优先级指数
  private beta = 0.4;  // 重要性采样指数
  private betaIncrement = 0.001;
  private epsilon = 1e-6; // 防止优先级为0

  constructor(maxSize: number = 10000) {
    this.maxSize = maxSize;
  }

  public add(experience: Experience, tdError: number = 1.0): void {
    const priority = Math.pow(Math.abs(tdError) + this.epsilon, this.alpha);
    const prioritizedExp: PrioritizedExperience = {
      ...experience,
      priority,
      tdError
    };

    if (this.buffer.length < this.maxSize) {
      this.buffer.push(prioritizedExp);
      this.priorities.push(priority);
    } else {
      this.buffer[this.index] = prioritizedExp;
      this.priorities[this.index] = priority;
      this.index = (this.index + 1) % this.maxSize;
    }
  }

  public sample(batchSize: number): { experiences: PrioritizedExperience[], weights: Float32Array, indices: number[] } {
    if (this.buffer.length < batchSize) {
      const experiences = [...this.buffer];
      const weights = new Float32Array(experiences.length).fill(1.0);
      const indices = Array.from({ length: experiences.length }, (_, i) => i);
      return { experiences, weights, indices };
    }

    const totalPriority = this.priorities.reduce((sum, p) => sum + p, 0);
    const experiences: PrioritizedExperience[] = [];
    const weights = new Float32Array(batchSize);
    const indices: number[] = [];

    // 计算最大权重用于归一化
    const minProbability = Math.min(...this.priorities) / totalPriority;
    const maxWeight = Math.pow(this.buffer.length * minProbability, -this.beta);

    for (let i = 0; i < batchSize; i++) {
      const randomValue = Math.random() * totalPriority;
      let cumulativePriority = 0;
      let selectedIndex = 0;

      for (let j = 0; j < this.priorities.length; j++) {
        cumulativePriority += this.priorities[j];
        if (cumulativePriority >= randomValue) {
          selectedIndex = j;
          break;
        }
      }

      experiences.push(this.buffer[selectedIndex]);
      indices.push(selectedIndex);

      // 计算重要性采样权重
      const probability = this.priorities[selectedIndex] / totalPriority;
      const weight = Math.pow(this.buffer.length * probability, -this.beta);
      weights[i] = weight / maxWeight; // 归一化
    }

    // 增加beta值
    this.beta = Math.min(1.0, this.beta + this.betaIncrement);

    return { experiences, weights, indices };
  }

  public updatePriorities(indices: number[], tdErrors: number[]): void {
    for (let i = 0; i < indices.length; i++) {
      const priority = Math.pow(Math.abs(tdErrors[i]) + this.epsilon, this.alpha);
      this.priorities[indices[i]] = priority;
      this.buffer[indices[i]].priority = priority;
      this.buffer[indices[i]].tdError = tdErrors[i];
    }
  }

  public size(): number {
    return this.buffer.length;
  }

  public clear(): void {
    this.buffer = [];
    this.priorities = [];
    this.index = 0;
  }
}

/**
 * 强化学习决策系统
 */
export class ReinforcementLearningDecisionSystem extends EventEmitter {
  private qNetwork: DeepQNetwork;
  private targetNetwork: DeepQNetwork;
  private policyNetwork?: PolicyNetwork; // 用于Actor-Critic算法
  private replayBuffer: ExperienceReplayBuffer;
  private prioritizedBuffer?: PrioritizedExperienceReplayBuffer;

  // 算法配置
  private algorithm: RLAlgorithm = RLAlgorithm.DQN;
  private usePrioritizedReplay = false;
  private useDoubleDQN = false;
  private useDuelingDQN = false;

  // 超参数
  private epsilon = 0.1;           // 探索率
  private epsilonDecay = 0.995;    // 探索率衰减
  private epsilonMin = 0.01;       // 最小探索率
  private gamma = 0.95;            // 折扣因子
  private batchSize = 32;          // 批次大小
  private targetUpdateFreq = 100;  // 目标网络更新频率
  private learningRate = 0.001;    // 学习率
  private tau = 0.005;             // 软更新参数

  // 训练状态
  private trainingSteps = 0;
  private totalReward = 0;
  private episodeCount = 0;
  private isTraining = true;

  // 性能统计
  private rewardHistory: number[] = [];
  private lossHistory: number[] = [];
  private actionCounts = new Map<string, number>();
  private qValueHistory: number[] = [];
  private explorationHistory: number[] = [];

  // 多步学习
  private nStepReturns = 1; // N步TD学习
  private nStepBuffer: Experience[] = [];

  // 噪声网络（用于探索）
  private useNoisyNetworks = false;
  private noiseScale = 0.5;

  constructor(
    stateSize: number = 64,
    actionSize: number = 10,
    hiddenSizes: number[] = [128, 64],
    learningRate: number = 0.001,
    algorithm: RLAlgorithm = RLAlgorithm.DQN
  ) {
    super();

    this.algorithm = algorithm;
    this.learningRate = learningRate;

    // 初始化网络
    this.qNetwork = new DeepQNetwork(stateSize, hiddenSizes, actionSize, learningRate);
    this.targetNetwork = new DeepQNetwork(stateSize, hiddenSizes, actionSize, learningRate);

    // 根据算法类型初始化额外组件
    this.initializeAlgorithmSpecificComponents(stateSize, actionSize, hiddenSizes);

    // 初始化目标网络权重
    this.targetNetwork.setWeights(this.qNetwork.copyWeights());

    // 初始化经验回放
    this.replayBuffer = new ExperienceReplayBuffer(10000);

    if (this.usePrioritizedReplay) {
      this.prioritizedBuffer = new PrioritizedExperienceReplayBuffer(10000);
    }
  }

  /**
   * 初始化算法特定组件
   */
  private initializeAlgorithmSpecificComponents(
    stateSize: number,
    actionSize: number,
    hiddenSizes: number[]
  ): void {
    switch (this.algorithm) {
      case RLAlgorithm.DOUBLE_DQN:
        this.useDoubleDQN = true;
        break;

      case RLAlgorithm.DUELING_DQN:
        this.useDuelingDQN = true;
        break;

      case RLAlgorithm.PRIORITIZED_DQN:
        this.usePrioritizedReplay = true;
        break;

      case RLAlgorithm.A3C:
      case RLAlgorithm.PPO:
        this.policyNetwork = new PolicyNetwork(stateSize, hiddenSizes, actionSize, this.learningRate);
        break;

      case RLAlgorithm.SAC:
        this.policyNetwork = new PolicyNetwork(stateSize, hiddenSizes, actionSize, this.learningRate);
        this.useNoisyNetworks = true;
        break;
    }
  }

  /**
   * 将决策上下文转换为状态
   */
  private contextToState(context: DecisionContext): State {
    const features = new Float32Array(64); // 固定大小的特征向量
    let index = 0;
    
    // 环境状态特征
    features[index++] = context.environmentState.location.x / 1000; // 归一化位置
    features[index++] = context.environmentState.location.y / 1000;
    features[index++] = context.environmentState.location.z / 1000;
    features[index++] = context.environmentState.timeOfDay / 24; // 归一化时间
    features[index++] = context.environmentState.temperature / 50; // 归一化温度
    features[index++] = context.environmentState.visibility / 1000; // 归一化可见度
    
    // 情感状态特征
    features[index++] = context.emotionalState.intensity;
    features[index++] = context.emotionalState.stress;
    features[index++] = context.emotionalState.confidence;
    features[index++] = context.emotionalState.motivation;
    
    // 目标特征
    features[index++] = context.currentGoals.length / 10; // 归一化目标数量
    const avgGoalProgress = context.currentGoals.length > 0 ?
      context.currentGoals.reduce((sum, g) => sum + g.progress, 0) / context.currentGoals.length : 0;
    features[index++] = avgGoalProgress;
    
    // 社交特征
    features[index++] = context.socialContext.nearbyEntities.length / 20; // 归一化附近实体数量
    features[index++] = context.socialContext.groupMembership.length / 5; // 归一化群体数量
    
    // 约束特征
    features[index++] = context.constraints.length / 10; // 归一化约束数量
    const avgConstraintSeverity = context.constraints.length > 0 ?
      context.constraints.reduce((sum, c) => sum + c.severity, 0) / context.constraints.length : 0;
    features[index++] = avgConstraintSeverity;
    
    // 填充剩余特征为0
    while (index < features.length) {
      features[index++] = 0;
    }
    
    return {
      features,
      entityId: context.entityId,
      timestamp: context.timestamp,
      metadata: { contextType: 'decision' }
    };
  }

  /**
   * 将决策选项转换为动作
   */
  private optionsToActions(options: DecisionOption[]): Action[] {
    return options.map((option, index) => ({
      id: option.id,
      type: option.type,
      parameters: new Float32Array([
        option.cost / 100,        // 归一化成本
        option.benefit / 100,     // 归一化收益
        option.risk,              // 风险
        option.duration / 10000,  // 归一化持续时间
        option.confidence,        // 置信度
        index / options.length    // 选项索引
      ]),
      expectedReward: option.benefit - option.cost - option.risk * 50
    }));
  }

  /**
   * 选择动作（支持多种策略）
   */
  public selectAction(state: State, actions: Action[]): Action {
    // 记录探索率历史
    this.explorationHistory.push(this.epsilon);
    if (this.explorationHistory.length > 1000) {
      this.explorationHistory.shift();
    }

    switch (this.algorithm) {
      case RLAlgorithm.DQN:
      case RLAlgorithm.DOUBLE_DQN:
      case RLAlgorithm.DUELING_DQN:
      case RLAlgorithm.PRIORITIZED_DQN:
        return this.selectActionEpsilonGreedy(state, actions);

      case RLAlgorithm.A3C:
      case RLAlgorithm.PPO:
        return this.selectActionPolicy(state, actions);

      case RLAlgorithm.SAC:
        return this.selectActionSAC(state, actions);

      default:
        return this.selectActionEpsilonGreedy(state, actions);
    }
  }

  /**
   * ε-贪心动作选择
   */
  private selectActionEpsilonGreedy(state: State, actions: Action[]): Action {
    if (this.isTraining && Math.random() < this.epsilon) {
      // 探索：随机选择动作
      const randomIndex = Math.floor(Math.random() * actions.length);
      return actions[randomIndex];
    } else {
      // 利用：选择Q值最高的动作
      return this.selectBestAction(state, actions);
    }
  }

  /**
   * 基于策略网络的动作选择
   */
  private selectActionPolicy(state: State, actions: Action[]): Action {
    if (!this.policyNetwork) {
      return this.selectActionEpsilonGreedy(state, actions);
    }

    // 使用策略网络计算动作概率
    const actionProbs = this.policyNetwork.forward(state.features);

    if (this.isTraining) {
      // 训练时根据概率分布采样
      return this.sampleActionFromProbabilities(actions, actionProbs);
    } else {
      // 推理时选择概率最高的动作
      const maxProbIndex = actionProbs.indexOf(Math.max(...Array.from(actionProbs)));
      return actions[Math.min(maxProbIndex, actions.length - 1)];
    }
  }

  /**
   * SAC算法的动作选择（带噪声）
   */
  private selectActionSAC(state: State, actions: Action[]): Action {
    if (!this.policyNetwork) {
      return this.selectActionEpsilonGreedy(state, actions);
    }

    // 添加噪声到状态特征
    const noisyFeatures = this.addNoise(state.features, this.noiseScale);
    const noisyState = { ...state, features: noisyFeatures };

    const actionProbs = this.policyNetwork.forward(noisyState.features);
    return this.sampleActionFromProbabilities(actions, actionProbs);
  }

  /**
   * 从概率分布中采样动作
   */
  private sampleActionFromProbabilities(actions: Action[], probabilities: Float32Array): Action {
    const random = Math.random();
    let cumulativeProb = 0;

    for (let i = 0; i < Math.min(actions.length, probabilities.length); i++) {
      cumulativeProb += probabilities[i];
      if (random <= cumulativeProb) {
        return actions[i];
      }
    }

    // 回退到最后一个动作
    return actions[actions.length - 1];
  }

  /**
   * 添加噪声到特征向量
   */
  private addNoise(features: Float32Array, scale: number): Float32Array {
    const noisyFeatures = new Float32Array(features.length);
    for (let i = 0; i < features.length; i++) {
      const noise = (Math.random() * 2 - 1) * scale;
      noisyFeatures[i] = features[i] + noise;
    }
    return noisyFeatures;
  }

  /**
   * 选择最佳动作
   */
  private selectBestAction(state: State, actions: Action[]): Action {
    let bestAction = actions[0];
    let bestQValue = -Infinity;
    
    for (const action of actions) {
      const qValue = this.getQValue(state, action);
      if (qValue > bestQValue) {
        bestQValue = qValue;
        bestAction = action;
      }
    }
    
    return bestAction;
  }

  /**
   * 获取Q值
   */
  private getQValue(state: State, action: Action): number {
    // 将状态和动作特征合并
    const input = new Float32Array(state.features.length + action.parameters.length);
    input.set(state.features, 0);
    input.set(action.parameters, state.features.length);
    
    const qValues = this.qNetwork.forward(input);
    return qValues[0]; // 简化：返回第一个Q值
  }

  /**
   * 强化学习决策
   */
  public async makeRLDecision(
    context: DecisionContext,
    options: DecisionOption[]
  ): Promise<DecisionResult> {
    const state = this.contextToState(context);
    const actions = this.optionsToActions(options);
    
    // 选择动作
    const selectedAction = this.selectAction(state, actions);
    
    // 更新动作计数
    const actionCount = this.actionCounts.get(selectedAction.type) || 0;
    this.actionCounts.set(selectedAction.type, actionCount + 1);
    
    // 找到对应的决策选项
    const selectedOption = options.find(opt => opt.id === selectedAction.id) || options[0];
    
    // 构建决策结果
    const result: DecisionResult = {
      selectedOption,
      reasoning: `强化学习选择，预期奖励: ${selectedAction.expectedReward.toFixed(2)}`,
      confidence: this.calculateConfidence(state, selectedAction),
      alternatives: options.filter(opt => opt.id !== selectedOption.id),
      executionPlan: [{
        id: `rl_step_${Date.now()}`,
        action: selectedAction.type,
        parameters: Array.from(selectedAction.parameters),
        duration: selectedOption.duration,
        dependencies: [],
        priority: 1
      }],
      timestamp: Date.now()
    };
    
    // 触发事件
    this.emit('actionSelected', {
      state,
      action: selectedAction,
      qValue: this.getQValue(state, selectedAction)
    });
    
    return result;
  }

  /**
   * 计算决策置信度
   */
  private calculateConfidence(state: State, action: Action): number {
    const qValue = this.getQValue(state, action);
    const normalizedQ = Math.tanh(qValue / 100); // 归一化Q值
    return Math.max(0.1, Math.min(0.9, (normalizedQ + 1) / 2));
  }

  /**
   * 添加经验并训练
   */
  public addExperience(
    state: State,
    action: Action,
    reward: number,
    nextState: State,
    done: boolean = false
  ): void {
    this.totalReward += reward;

    // 使用N步学习或标准经验回放
    if (this.nStepReturns > 1) {
      this.addNStepExperience(state, action, reward, nextState, done);
    } else {
      const experience: Experience = {
        state,
        action,
        reward,
        nextState,
        done,
        timestamp: Date.now()
      };

      if (this.usePrioritizedReplay && this.prioritizedBuffer) {
        // 计算初始TD误差作为优先级
        const tdError = this.calculateTDError(experience);
        this.prioritizedBuffer.add(experience, Math.abs(tdError));
      } else {
        this.replayBuffer.add(experience);
      }
    }

    // 训练网络
    const bufferSize = this.usePrioritizedReplay && this.prioritizedBuffer ?
      this.prioritizedBuffer.size() : this.replayBuffer.size();

    if (this.isTraining && bufferSize >= this.batchSize) {
      this.trainNetwork();
    }

    // 更新目标网络
    if (this.trainingSteps % this.targetUpdateFreq === 0) {
      this.updateTargetNetwork();
    }

    // 衰减探索率
    if (this.epsilon > this.epsilonMin) {
      this.epsilon *= this.epsilonDecay;
    }

    this.trainingSteps++;

    // 触发经验添加事件
    this.emit('experienceAdded', {
      reward,
      done,
      totalReward: this.totalReward,
      bufferSize,
      trainingSteps: this.trainingSteps
    });
  }

  /**
   * 计算TD误差
   */
  private calculateTDError(experience: Experience): number {
    const { state, action, reward, nextState, done } = experience;

    // 当前Q值
    const currentInput = new Float32Array(state.features.length + action.parameters.length);
    currentInput.set(state.features, 0);
    currentInput.set(action.parameters, state.features.length);
    const currentQ = this.qNetwork.forward(currentInput)[0];

    // 目标Q值
    let targetQ = reward;
    if (!done) {
      const nextInput = new Float32Array(nextState.features.length + action.parameters.length);
      nextInput.set(nextState.features, 0);
      nextInput.set(action.parameters, nextState.features.length);
      const nextQValues = this.targetNetwork.forward(nextInput);
      const maxNextQ = Math.max(...Array.from(nextQValues));
      targetQ += this.gamma * maxNextQ;
    }

    return targetQ - currentQ;
  }

  /**
   * 训练网络（支持多种算法）
   */
  private trainNetwork(): void {
    switch (this.algorithm) {
      case RLAlgorithm.DQN:
        this.trainDQN();
        break;

      case RLAlgorithm.DOUBLE_DQN:
        this.trainDoubleDQN();
        break;

      case RLAlgorithm.DUELING_DQN:
        this.trainDuelingDQN();
        break;

      case RLAlgorithm.PRIORITIZED_DQN:
        this.trainPrioritizedDQN();
        break;

      case RLAlgorithm.A3C:
        this.trainA3C();
        break;

      case RLAlgorithm.PPO:
        this.trainPPO();
        break;

      case RLAlgorithm.SAC:
        this.trainSAC();
        break;

      default:
        this.trainDQN();
    }
  }

  /**
   * 标准DQN训练
   */
  private trainDQN(): void {
    const batch = this.replayBuffer.sample(this.batchSize);
    let totalLoss = 0;
    const qValues: number[] = [];

    for (const experience of batch) {
      const { state, action, reward, nextState, done } = experience;

      // 计算目标Q值
      let targetQ = reward;
      if (!done) {
        const nextInput = new Float32Array(nextState.features.length + action.parameters.length);
        nextInput.set(nextState.features, 0);
        nextInput.set(action.parameters, nextState.features.length);

        const nextQValues = this.targetNetwork.forward(nextInput);
        const maxNextQ = Math.max(...Array.from(nextQValues));
        targetQ += this.gamma * maxNextQ;
      }

      // 当前Q值
      const currentInput = new Float32Array(state.features.length + action.parameters.length);
      currentInput.set(state.features, 0);
      currentInput.set(action.parameters, state.features.length);

      const currentQ = this.qNetwork.forward(currentInput);
      qValues.push(currentQ[0]);

      // 计算损失
      const target = new Float32Array(currentQ.length);
      target.set(currentQ);
      target[0] = targetQ;

      const loss = Math.pow(targetQ - currentQ[0], 2);
      totalLoss += loss;

      // 反向传播
      this.qNetwork.backward(currentInput, target, currentQ);
    }

    this.updateTrainingStats(totalLoss, batch.length, qValues);
  }

  /**
   * Double DQN训练
   */
  private trainDoubleDQN(): void {
    const batch = this.replayBuffer.sample(this.batchSize);
    let totalLoss = 0;
    const qValues: number[] = [];

    for (const experience of batch) {
      const { state, action, reward, nextState, done } = experience;

      let targetQ = reward;
      if (!done) {
        const nextInput = new Float32Array(nextState.features.length + action.parameters.length);
        nextInput.set(nextState.features, 0);
        nextInput.set(action.parameters, nextState.features.length);

        // 使用主网络选择动作
        const nextQValues = this.qNetwork.forward(nextInput);
        const bestActionIndex = nextQValues.indexOf(Math.max(...Array.from(nextQValues)));

        // 使用目标网络评估动作
        const targetNextQValues = this.targetNetwork.forward(nextInput);
        const maxNextQ = targetNextQValues[bestActionIndex];

        targetQ += this.gamma * maxNextQ;
      }

      const currentInput = new Float32Array(state.features.length + action.parameters.length);
      currentInput.set(state.features, 0);
      currentInput.set(action.parameters, state.features.length);

      const currentQ = this.qNetwork.forward(currentInput);
      qValues.push(currentQ[0]);

      const target = new Float32Array(currentQ.length);
      target.set(currentQ);
      target[0] = targetQ;

      const loss = Math.pow(targetQ - currentQ[0], 2);
      totalLoss += loss;

      this.qNetwork.backward(currentInput, target, currentQ);
    }

    this.updateTrainingStats(totalLoss, batch.length, qValues);
  }

  /**
   * Dueling DQN训练（简化实现）
   */
  private trainDuelingDQN(): void {
    // 简化实现：使用标准DQN训练，但在网络架构上有所不同
    this.trainDQN();
  }

  /**
   * 优先级经验回放DQN训练
   */
  private trainPrioritizedDQN(): void {
    if (!this.prioritizedBuffer) {
      this.trainDQN();
      return;
    }

    const { experiences, weights, indices } = this.prioritizedBuffer.sample(this.batchSize);
    let totalLoss = 0;
    const qValues: number[] = [];
    const tdErrors: number[] = [];

    for (let i = 0; i < experiences.length; i++) {
      const experience = experiences[i];
      const weight = weights[i];
      const { state, action, reward, nextState, done } = experience;

      let targetQ = reward;
      if (!done) {
        const nextInput = new Float32Array(nextState.features.length + action.parameters.length);
        nextInput.set(nextState.features, 0);
        nextInput.set(action.parameters, nextState.features.length);

        const nextQValues = this.targetNetwork.forward(nextInput);
        const maxNextQ = Math.max(...Array.from(nextQValues));
        targetQ += this.gamma * maxNextQ;
      }

      const currentInput = new Float32Array(state.features.length + action.parameters.length);
      currentInput.set(state.features, 0);
      currentInput.set(action.parameters, state.features.length);

      const currentQ = this.qNetwork.forward(currentInput);
      qValues.push(currentQ[0]);

      const tdError = targetQ - currentQ[0];
      tdErrors.push(tdError);

      const target = new Float32Array(currentQ.length);
      target.set(currentQ);
      target[0] = targetQ;

      // 使用重要性采样权重调整损失
      const loss = weight * Math.pow(tdError, 2);
      totalLoss += loss;

      this.qNetwork.backward(currentInput, target, currentQ);
    }

    // 更新优先级
    this.prioritizedBuffer.updatePriorities(indices, tdErrors);

    this.updateTrainingStats(totalLoss, experiences.length, qValues);
  }

  /**
   * A3C训练（简化实现）
   */
  private trainA3C(): void {
    if (!this.policyNetwork) {
      this.trainDQN();
      return;
    }

    const batch = this.replayBuffer.sample(this.batchSize);
    let totalLoss = 0;

    // 简化的A3C实现：同时训练价值网络和策略网络
    for (const experience of batch) {
      const { state, action, reward, nextState, done } = experience;

      // 训练价值网络（类似DQN）
      let targetQ = reward;
      if (!done) {
        const nextInput = new Float32Array(nextState.features.length + action.parameters.length);
        nextInput.set(nextState.features, 0);
        nextInput.set(action.parameters, nextState.features.length);

        const nextQValues = this.targetNetwork.forward(nextInput);
        const maxNextQ = Math.max(...Array.from(nextQValues));
        targetQ += this.gamma * maxNextQ;
      }

      const currentInput = new Float32Array(state.features.length + action.parameters.length);
      currentInput.set(state.features, 0);
      currentInput.set(action.parameters, state.features.length);

      const currentQ = this.qNetwork.forward(currentInput);
      const advantage = targetQ - currentQ[0];

      // 简化的策略梯度更新
      const actionProbs = this.policyNetwork.forward(state.features);

      const target = new Float32Array(currentQ.length);
      target.set(currentQ);
      target[0] = targetQ;

      const loss = Math.pow(advantage, 2);
      totalLoss += loss;

      this.qNetwork.backward(currentInput, target, currentQ);
    }

    this.updateTrainingStats(totalLoss, batch.length, []);
  }

  /**
   * PPO训练（简化实现）
   */
  private trainPPO(): void {
    // 简化实现：使用A3C的训练方法
    this.trainA3C();
  }

  /**
   * SAC训练（简化实现）
   */
  private trainSAC(): void {
    // 简化实现：使用带噪声的A3C训练
    this.trainA3C();
  }

  /**
   * 更新训练统计
   */
  private updateTrainingStats(totalLoss: number, batchSize: number, qValues: number[]): void {
    const avgLoss = totalLoss / batchSize;
    this.lossHistory.push(avgLoss);

    if (qValues.length > 0) {
      const avgQValue = qValues.reduce((sum, q) => sum + q, 0) / qValues.length;
      this.qValueHistory.push(avgQValue);

      if (this.qValueHistory.length > 1000) {
        this.qValueHistory.shift();
      }
    }

    // 限制历史记录长度
    if (this.lossHistory.length > 1000) {
      this.lossHistory.shift();
    }

    this.emit('networkTrained', {
      loss: avgLoss,
      batchSize,
      epsilon: this.epsilon,
      algorithm: this.algorithm,
      avgQValue: qValues.length > 0 ? qValues.reduce((sum, q) => sum + q, 0) / qValues.length : 0
    });
  }

  /**
   * 更新目标网络（支持软更新）
   */
  private updateTargetNetwork(): void {
    if (this.tau < 1.0) {
      // 软更新：θ_target = τ * θ_local + (1 - τ) * θ_target
      this.softUpdateTargetNetwork();
    } else {
      // 硬更新：完全复制权重
      this.targetNetwork.setWeights(this.qNetwork.copyWeights());
    }

    this.emit('targetNetworkUpdated', {
      step: this.trainingSteps,
      updateType: this.tau < 1.0 ? 'soft' : 'hard',
      tau: this.tau
    });
  }

  /**
   * 软更新目标网络
   */
  private softUpdateTargetNetwork(): void {
    const mainWeights = this.qNetwork.copyWeights();
    const targetWeights = this.targetNetwork.copyWeights();

    for (let i = 0; i < mainWeights.length; i++) {
      const mainLayer = mainWeights[i];
      const targetLayer = targetWeights[i];

      // 更新权重
      for (let j = 0; j < mainLayer.weights.length; j++) {
        targetLayer.weights[j] = this.tau * mainLayer.weights[j] + (1 - this.tau) * targetLayer.weights[j];
      }

      // 更新偏置
      for (let j = 0; j < mainLayer.biases.length; j++) {
        targetLayer.biases[j] = this.tau * mainLayer.biases[j] + (1 - this.tau) * targetLayer.biases[j];
      }
    }

    this.targetNetwork.setWeights(targetWeights);
  }

  /**
   * N步TD学习
   */
  private addNStepExperience(
    state: State,
    action: Action,
    reward: number,
    nextState: State,
    done: boolean
  ): void {
    const experience: Experience = {
      state,
      action,
      reward,
      nextState,
      done,
      timestamp: Date.now()
    };

    this.nStepBuffer.push(experience);

    // 当缓冲区达到N步或遇到终止状态时，计算N步回报
    if (this.nStepBuffer.length >= this.nStepReturns || done) {
      const nStepReturn = this.calculateNStepReturn();
      const firstExperience = this.nStepBuffer[0];

      // 创建N步经验
      const nStepExperience: Experience = {
        state: firstExperience.state,
        action: firstExperience.action,
        reward: nStepReturn,
        nextState: this.nStepBuffer[this.nStepBuffer.length - 1].nextState,
        done: this.nStepBuffer[this.nStepBuffer.length - 1].done,
        timestamp: firstExperience.timestamp
      };

      // 添加到经验回放缓冲区
      if (this.usePrioritizedReplay && this.prioritizedBuffer) {
        this.prioritizedBuffer.add(nStepExperience, 1.0);
      } else {
        this.replayBuffer.add(nStepExperience);
      }

      // 移除第一个经验
      this.nStepBuffer.shift();
    }
  }

  /**
   * 计算N步回报
   */
  private calculateNStepReturn(): number {
    let nStepReturn = 0;
    let gamma = 1;

    for (const experience of this.nStepBuffer) {
      nStepReturn += gamma * experience.reward;
      gamma *= this.gamma;

      if (experience.done) {
        break;
      }
    }

    return nStepReturn;
  }

  /**
   * 设置算法类型
   */
  public setAlgorithm(algorithm: RLAlgorithm): void {
    this.algorithm = algorithm;

    // 重新初始化算法特定组件
    const stateSize = this.qNetwork['inputSize'] || 64;
    const actionSize = this.qNetwork['outputSize'] || 10;
    const hiddenSizes = [128, 64]; // 默认隐藏层大小

    this.initializeAlgorithmSpecificComponents(stateSize, actionSize, hiddenSizes);

    this.emit('algorithmChanged', { algorithm });
  }

  /**
   * 设置超参数
   */
  public setHyperparameters(params: {
    epsilon?: number;
    epsilonDecay?: number;
    epsilonMin?: number;
    gamma?: number;
    learningRate?: number;
    batchSize?: number;
    targetUpdateFreq?: number;
    tau?: number;
    nStepReturns?: number;
  }): void {
    if (params.epsilon !== undefined) this.epsilon = params.epsilon;
    if (params.epsilonDecay !== undefined) this.epsilonDecay = params.epsilonDecay;
    if (params.epsilonMin !== undefined) this.epsilonMin = params.epsilonMin;
    if (params.gamma !== undefined) this.gamma = params.gamma;
    if (params.learningRate !== undefined) this.learningRate = params.learningRate;
    if (params.batchSize !== undefined) this.batchSize = params.batchSize;
    if (params.targetUpdateFreq !== undefined) this.targetUpdateFreq = params.targetUpdateFreq;
    if (params.tau !== undefined) this.tau = params.tau;
    if (params.nStepReturns !== undefined) this.nStepReturns = params.nStepReturns;

    this.emit('hyperparametersChanged', params);
  }

  /**
   * 获取当前超参数
   */
  public getHyperparameters(): any {
    return {
      epsilon: this.epsilon,
      epsilonDecay: this.epsilonDecay,
      epsilonMin: this.epsilonMin,
      gamma: this.gamma,
      learningRate: this.learningRate,
      batchSize: this.batchSize,
      targetUpdateFreq: this.targetUpdateFreq,
      tau: this.tau,
      nStepReturns: this.nStepReturns,
      algorithm: this.algorithm,
      usePrioritizedReplay: this.usePrioritizedReplay,
      useDoubleDQN: this.useDoubleDQN,
      useDuelingDQN: this.useDuelingDQN,
      useNoisyNetworks: this.useNoisyNetworks
    };
  }

  /**
   * 结束回合
   */
  public endEpisode(): void {
    this.episodeCount++;
    this.rewardHistory.push(this.totalReward);
    
    // 限制历史记录长度
    if (this.rewardHistory.length > 1000) {
      this.rewardHistory.shift();
    }
    
    this.emit('episodeEnded', {
      episode: this.episodeCount,
      totalReward: this.totalReward,
      epsilon: this.epsilon
    });
    
    this.totalReward = 0;
  }

  /**
   * 设置训练模式
   */
  public setTraining(training: boolean): void {
    this.isTraining = training;
  }

  /**
   * 获取训练统计
   */
  public getTrainingStats(): any {
    const recentRewards = this.rewardHistory.slice(-100);
    const recentLosses = this.lossHistory.slice(-100);
    const recentQValues = this.qValueHistory.slice(-100);
    const recentExploration = this.explorationHistory.slice(-100);

    const bufferSize = this.usePrioritizedReplay && this.prioritizedBuffer ?
      this.prioritizedBuffer.size() : this.replayBuffer.size();

    return {
      // 基础统计
      episodeCount: this.episodeCount,
      trainingSteps: this.trainingSteps,
      algorithm: this.algorithm,

      // 探索统计
      epsilon: this.epsilon,
      averageExploration: recentExploration.length > 0 ?
        recentExploration.reduce((sum, e) => sum + e, 0) / recentExploration.length : 0,

      // 奖励统计
      totalReward: this.totalReward,
      averageReward: recentRewards.length > 0 ?
        recentRewards.reduce((sum, r) => sum + r, 0) / recentRewards.length : 0,
      maxReward: recentRewards.length > 0 ? Math.max(...recentRewards) : 0,
      minReward: recentRewards.length > 0 ? Math.min(...recentRewards) : 0,
      rewardVariance: this.calculateVariance(recentRewards),

      // 损失统计
      averageLoss: recentLosses.length > 0 ?
        recentLosses.reduce((sum, l) => sum + l, 0) / recentLosses.length : 0,
      maxLoss: recentLosses.length > 0 ? Math.max(...recentLosses) : 0,
      minLoss: recentLosses.length > 0 ? Math.min(...recentLosses) : 0,
      lossVariance: this.calculateVariance(recentLosses),

      // Q值统计
      averageQValue: recentQValues.length > 0 ?
        recentQValues.reduce((sum, q) => sum + q, 0) / recentQValues.length : 0,
      maxQValue: recentQValues.length > 0 ? Math.max(...recentQValues) : 0,
      minQValue: recentQValues.length > 0 ? Math.min(...recentQValues) : 0,
      qValueVariance: this.calculateVariance(recentQValues),

      // 缓冲区统计
      replayBufferSize: bufferSize,
      bufferUtilization: bufferSize / (this.usePrioritizedReplay && this.prioritizedBuffer ?
        10000 : 10000), // 假设最大缓冲区大小为10000

      // 动作分布
      actionDistribution: Object.fromEntries(this.actionCounts),
      actionEntropy: this.calculateActionEntropy(),

      // 网络统计
      networkParameters: this.qNetwork.getParameterCount(),

      // 超参数
      hyperparameters: this.getHyperparameters(),

      // 性能指标
      learningEfficiency: this.calculateLearningEfficiency(),
      convergenceIndicator: this.calculateConvergenceIndicator()
    };
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;

    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const squaredDiffs = values.map(v => Math.pow(v - mean, 2));
    return squaredDiffs.reduce((sum, d) => sum + d, 0) / values.length;
  }

  /**
   * 计算动作熵（衡量探索多样性）
   */
  private calculateActionEntropy(): number {
    const totalActions = Array.from(this.actionCounts.values()).reduce((sum, count) => sum + count, 0);
    if (totalActions === 0) return 0;

    let entropy = 0;
    for (const count of this.actionCounts.values()) {
      const probability = count / totalActions;
      if (probability > 0) {
        entropy -= probability * Math.log2(probability);
      }
    }

    return entropy;
  }

  /**
   * 计算学习效率
   */
  private calculateLearningEfficiency(): number {
    if (this.rewardHistory.length < 10) return 0;

    const recentRewards = this.rewardHistory.slice(-10);
    const earlyRewards = this.rewardHistory.slice(0, Math.min(10, this.rewardHistory.length));

    const recentAvg = recentRewards.reduce((sum, r) => sum + r, 0) / recentRewards.length;
    const earlyAvg = earlyRewards.reduce((sum, r) => sum + r, 0) / earlyRewards.length;

    return recentAvg - earlyAvg; // 正值表示学习有效
  }

  /**
   * 计算收敛指标
   */
  private calculateConvergenceIndicator(): number {
    if (this.lossHistory.length < 20) return 1; // 未收敛

    const recentLosses = this.lossHistory.slice(-20);
    const lossVariance = this.calculateVariance(recentLosses);

    // 损失方差小表示可能收敛
    return Math.exp(-lossVariance * 10); // 0到1之间，1表示收敛
  }

  /**
   * 获取详细的性能分析
   */
  public getPerformanceAnalysis(): any {
    const stats = this.getTrainingStats();

    return {
      ...stats,

      // 学习曲线分析
      learningCurve: {
        rewardTrend: this.calculateTrend(this.rewardHistory),
        lossTrend: this.calculateTrend(this.lossHistory),
        qValueTrend: this.calculateTrend(this.qValueHistory)
      },

      // 探索vs利用分析
      explorationAnalysis: {
        currentEpsilon: this.epsilon,
        explorationRate: this.calculateExplorationRate(),
        exploitationEffectiveness: this.calculateExploitationEffectiveness()
      },

      // 稳定性分析
      stabilityAnalysis: {
        rewardStability: 1 / (1 + stats.rewardVariance),
        lossStability: 1 / (1 + stats.lossVariance),
        qValueStability: 1 / (1 + stats.qValueVariance)
      },

      // 建议
      recommendations: this.generateRecommendations(stats)
    };
  }

  /**
   * 计算趋势（简单线性回归斜率）
   */
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;

    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;

    const sumX = x.reduce((sum, xi) => sum + xi, 0);
    const sumY = y.reduce((sum, yi) => sum + yi, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return slope;
  }

  /**
   * 计算探索率
   */
  private calculateExplorationRate(): number {
    const recentExploration = this.explorationHistory.slice(-100);
    return recentExploration.length > 0 ?
      recentExploration.reduce((sum, e) => sum + e, 0) / recentExploration.length : 0;
  }

  /**
   * 计算利用效果
   */
  private calculateExploitationEffectiveness(): number {
    // 简化实现：基于最近的奖励趋势
    const recentRewards = this.rewardHistory.slice(-50);
    if (recentRewards.length < 10) return 0.5;

    const trend = this.calculateTrend(recentRewards);
    return Math.max(0, Math.min(1, (trend + 1) / 2)); // 归一化到0-1
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(stats: any): string[] {
    const recommendations: string[] = [];

    // 探索率建议
    if (stats.epsilon < 0.05 && stats.learningEfficiency < 0) {
      recommendations.push('考虑增加探索率，当前学习效率较低');
    }

    // 学习率建议
    if (stats.lossVariance > 1.0) {
      recommendations.push('损失波动较大，考虑降低学习率');
    }

    // 缓冲区建议
    if (stats.bufferUtilization < 0.1) {
      recommendations.push('经验回放缓冲区利用率低，考虑增加训练频率');
    }

    // 收敛建议
    if (stats.convergenceIndicator > 0.9) {
      recommendations.push('模型可能已收敛，考虑停止训练或调整超参数');
    }

    // 动作多样性建议
    if (stats.actionEntropy < 1.0) {
      recommendations.push('动作选择多样性较低，考虑增加探索');
    }

    return recommendations;
  }

  /**
   * 保存模型（简化实现）
   */
  public saveModel(): any {
    return {
      qNetworkWeights: this.qNetwork.copyWeights(),
      targetNetworkWeights: this.targetNetwork.copyWeights(),
      hyperparameters: {
        epsilon: this.epsilon,
        gamma: this.gamma,
        batchSize: this.batchSize
      },
      stats: this.getTrainingStats()
    };
  }

  /**
   * 加载模型（简化实现）
   */
  public loadModel(modelData: any): void {
    if (modelData.qNetworkWeights) {
      this.qNetwork.setWeights(modelData.qNetworkWeights);
    }
    
    if (modelData.targetNetworkWeights) {
      this.targetNetwork.setWeights(modelData.targetNetworkWeights);
    }
    
    if (modelData.hyperparameters) {
      this.epsilon = modelData.hyperparameters.epsilon || this.epsilon;
      this.gamma = modelData.hyperparameters.gamma || this.gamma;
      this.batchSize = modelData.hyperparameters.batchSize || this.batchSize;
    }
  }

  /**
   * 重置系统
   */
  public reset(): void {
    this.replayBuffer.clear();
    if (this.prioritizedBuffer) {
      this.prioritizedBuffer.clear();
    }

    this.rewardHistory = [];
    this.lossHistory = [];
    this.qValueHistory = [];
    this.explorationHistory = [];
    this.actionCounts.clear();
    this.nStepBuffer = [];

    this.trainingSteps = 0;
    this.totalReward = 0;
    this.episodeCount = 0;
    this.epsilon = 0.1;

    this.emit('systemReset');
  }

  /**
   * 运行基准测试
   */
  public async runBenchmark(
    testCases: Array<{
      context: DecisionContext,
      options: DecisionOption[],
      expectedOutcome?: number
    }>,
    iterations: number = 100
  ): Promise<any> {
    const results = {
      totalTests: testCases.length * iterations,
      averageDecisionTime: 0,
      averageConfidence: 0,
      successRate: 0,
      algorithmPerformance: new Map<RLAlgorithm, number>(),
      detailedResults: [] as any[]
    };

    let totalDecisionTime = 0;
    let totalConfidence = 0;
    let successCount = 0;

    for (let i = 0; i < iterations; i++) {
      for (const testCase of testCases) {
        const startTime = performance.now();

        const decision = await this.makeRLDecision(testCase.context, testCase.options);

        const decisionTime = performance.now() - startTime;
        totalDecisionTime += decisionTime;
        totalConfidence += decision.confidence;

        // 评估成功率（如果有期望结果）
        if (testCase.expectedOutcome !== undefined) {
          const actualOutcome = this.evaluateDecisionOutcome(decision, testCase);
          if (Math.abs(actualOutcome - testCase.expectedOutcome) < 0.1) {
            successCount++;
          }
        }

        results.detailedResults.push({
          testCaseIndex: testCases.indexOf(testCase),
          iteration: i,
          decisionTime,
          confidence: decision.confidence,
          selectedOption: decision.selectedOption.id,
          algorithm: this.algorithm
        });
      }
    }

    results.averageDecisionTime = totalDecisionTime / results.totalTests;
    results.averageConfidence = totalConfidence / results.totalTests;
    results.successRate = successCount / results.totalTests;

    this.emit('benchmarkCompleted', results);
    return results;
  }

  /**
   * 评估决策结果
   */
  private evaluateDecisionOutcome(decision: DecisionResult, testCase: any): number {
    // 简化的结果评估
    const option = decision.selectedOption;
    return (option.benefit - option.cost) * decision.confidence - option.risk;
  }

  /**
   * 比较不同算法性能
   */
  public async compareAlgorithms(
    algorithms: RLAlgorithm[],
    testCases: Array<{
      context: DecisionContext,
      options: DecisionOption[]
    }>,
    trainingEpisodes: number = 100
  ): Promise<any> {
    const results = new Map<RLAlgorithm, any>();

    for (const algorithm of algorithms) {
      console.log(`测试算法: ${algorithm}`);

      // 保存当前状态
      const originalAlgorithm = this.algorithm;
      const originalStats = this.getTrainingStats();

      // 切换算法
      this.setAlgorithm(algorithm);
      this.reset();

      // 训练
      for (let episode = 0; episode < trainingEpisodes; episode++) {
        for (const testCase of testCases) {
          const decision = await this.makeRLDecision(testCase.context, testCase.options);

          // 模拟奖励
          const reward = this.simulateReward(decision);
          const state = this.contextToState(testCase.context);
          const actions = this.optionsToActions(testCase.options);
          const selectedAction = actions.find(a => a.id === decision.selectedOption.id) || actions[0];

          // 添加经验
          this.addExperience(state, selectedAction, reward, state, true);
        }

        this.endEpisode();
      }

      // 收集结果
      const finalStats = this.getTrainingStats();
      results.set(algorithm, {
        finalReward: finalStats.averageReward,
        convergenceSpeed: this.calculateConvergenceSpeed(),
        stability: finalStats.rewardVariance,
        efficiency: finalStats.learningEfficiency
      });

      // 恢复原始状态
      this.setAlgorithm(originalAlgorithm);
    }

    this.emit('algorithmComparisonCompleted', Object.fromEntries(results));
    return Object.fromEntries(results);
  }

  /**
   * 模拟奖励
   */
  private simulateReward(decision: DecisionResult): number {
    const option = decision.selectedOption;
    const baseReward = option.benefit - option.cost;
    const riskPenalty = option.risk * Math.random();
    const confidenceBonus = decision.confidence * 0.1;

    return baseReward - riskPenalty + confidenceBonus;
  }

  /**
   * 计算收敛速度
   */
  private calculateConvergenceSpeed(): number {
    if (this.rewardHistory.length < 20) return 0;

    // 找到奖励稳定的点
    const windowSize = 10;
    const threshold = 0.1;

    for (let i = windowSize; i < this.rewardHistory.length; i++) {
      const window = this.rewardHistory.slice(i - windowSize, i);
      const variance = this.calculateVariance(window);

      if (variance < threshold) {
        return (this.rewardHistory.length - i) / this.rewardHistory.length;
      }
    }

    return 0; // 未收敛
  }

  /**
   * 导出完整模型数据
   */
  public exportModel(): any {
    return {
      version: '2.0',
      timestamp: Date.now(),
      algorithm: this.algorithm,

      // 网络权重
      qNetworkWeights: this.qNetwork.copyWeights(),
      targetNetworkWeights: this.targetNetwork.copyWeights(),
      policyNetworkWeights: this.policyNetwork?.copyWeights(),

      // 超参数
      hyperparameters: this.getHyperparameters(),

      // 训练历史
      trainingHistory: {
        rewardHistory: this.rewardHistory,
        lossHistory: this.lossHistory,
        qValueHistory: this.qValueHistory,
        explorationHistory: this.explorationHistory
      },

      // 统计信息
      statistics: this.getTrainingStats(),

      // 经验回放缓冲区（可选）
      experienceBuffer: this.replayBuffer.size() > 0 ? {
        size: this.replayBuffer.size(),
        // 注意：实际导出时可能需要序列化经验数据
      } : null,

      // 元数据
      metadata: {
        trainingSteps: this.trainingSteps,
        episodeCount: this.episodeCount,
        totalReward: this.totalReward,
        actionCounts: Object.fromEntries(this.actionCounts)
      }
    };
  }

  /**
   * 导入模型数据
   */
  public importModel(modelData: any): void {
    if (modelData.version !== '2.0') {
      console.warn('模型版本不匹配，可能存在兼容性问题');
    }

    // 设置算法
    if (modelData.algorithm) {
      this.setAlgorithm(modelData.algorithm);
    }

    // 加载网络权重
    if (modelData.qNetworkWeights) {
      this.qNetwork.setWeights(modelData.qNetworkWeights);
    }

    if (modelData.targetNetworkWeights) {
      this.targetNetwork.setWeights(modelData.targetNetworkWeights);
    }

    if (modelData.policyNetworkWeights && this.policyNetwork) {
      this.policyNetwork.setWeights(modelData.policyNetworkWeights);
    }

    // 设置超参数
    if (modelData.hyperparameters) {
      this.setHyperparameters(modelData.hyperparameters);
    }

    // 恢复训练历史
    if (modelData.trainingHistory) {
      this.rewardHistory = modelData.trainingHistory.rewardHistory || [];
      this.lossHistory = modelData.trainingHistory.lossHistory || [];
      this.qValueHistory = modelData.trainingHistory.qValueHistory || [];
      this.explorationHistory = modelData.trainingHistory.explorationHistory || [];
    }

    // 恢复元数据
    if (modelData.metadata) {
      this.trainingSteps = modelData.metadata.trainingSteps || 0;
      this.episodeCount = modelData.metadata.episodeCount || 0;
      this.totalReward = modelData.metadata.totalReward || 0;

      if (modelData.metadata.actionCounts) {
        this.actionCounts = new Map(Object.entries(modelData.metadata.actionCounts));
      }
    }

    this.emit('modelImported', {
      version: modelData.version,
      algorithm: modelData.algorithm,
      timestamp: modelData.timestamp
    });
  }

  /**
   * 验证模型完整性
   */
  public validateModel(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // 检查网络
      if (!this.qNetwork) {
        errors.push('Q网络未初始化');
      } else {
        const paramCount = this.qNetwork.getParameterCount();
        if (paramCount === 0) {
          errors.push('Q网络没有参数');
        }
      }

      if (!this.targetNetwork) {
        errors.push('目标网络未初始化');
      }

      // 检查算法特定组件
      if ((this.algorithm === RLAlgorithm.A3C || this.algorithm === RLAlgorithm.PPO || this.algorithm === RLAlgorithm.SAC) && !this.policyNetwork) {
        errors.push(`算法 ${this.algorithm} 需要策略网络但未初始化`);
      }

      if (this.usePrioritizedReplay && !this.prioritizedBuffer) {
        errors.push('启用了优先级回放但缓冲区未初始化');
      }

      // 检查超参数
      if (this.epsilon < 0 || this.epsilon > 1) {
        errors.push('探索率超出有效范围 [0, 1]');
      }

      if (this.gamma < 0 || this.gamma > 1) {
        errors.push('折扣因子超出有效范围 [0, 1]');
      }

      if (this.learningRate <= 0) {
        errors.push('学习率必须大于0');
      }

      // 检查缓冲区
      const bufferSize = this.usePrioritizedReplay && this.prioritizedBuffer ?
        this.prioritizedBuffer.size() : this.replayBuffer.size();

      if (bufferSize === 0 && this.trainingSteps > 0) {
        errors.push('已进行训练但经验缓冲区为空');
      }

    } catch (error) {
      errors.push(`验证过程中发生错误: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 清理缓冲区
    this.replayBuffer.clear();
    if (this.prioritizedBuffer) {
      this.prioritizedBuffer.clear();
    }

    // 清理历史数据
    this.rewardHistory = [];
    this.lossHistory = [];
    this.qValueHistory = [];
    this.explorationHistory = [];
    this.nStepBuffer = [];
    this.actionCounts.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.emit('disposed');
  }
}
